import React from "react";
import Image from "next/image";
import HIW1 from "@/../public/img/how-it-works-image/1.png";
import HIW2 from "@/../public/img/how-it-works-image/2.png";
import HIW3 from "@/../public/img/how-it-works-image/3.png";
import HIW4 from "@/../public/img/how-it-works-image/4.png";
import HIW5 from "@/../public/img/how-it-works-image/5.png";
import HIW6 from "@/../public/img/how-it-works-image/6.png";
import MF from "@/../public/img/banner-image/manage-your-funds.png";
import DB from "@/../public/img/banner-image/deposit-into-bank.png";

export default function HowNgnairWorks() {
  return (
    <div className="container">
      <section
        className="how-it-works-area ptb-70"
        style={{ paddingTop: "130px" }}
      >
        <div className="container">
          <div className="section-title" style={{ maxWidth: "1040px" }}>
            <h2>How NGnair Payments Works</h2>
            <div className="bar"></div>
            <p
              style={{ maxWidth: "900px" }}
              className="text-gray-500 md:text-lg"
            >
              NGnair Payments simplifies and secures transactions with a
              flexible, customer-first platform. From mobile to recurring
              payments, we support all major methods—easily integrated into your
              systems. Real-time dashboards, instant fund access, and smart
              analytics help you optimize operations and grow. With top-tier
              encryption and compliance, your data stays protected every step of
              the way.
            </p>
          </div>

          <div className="row">
            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={HIW1} alt="image" />

                <h3>1. Create a Profile</h3>
                <p className="text-gray-500 md:text-lg">
                  Set up your user profile for NGnair Payments in minutes.
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={HIW2} alt="image" />

                <h3>2. Register Business</h3>
                <p className="text-gray-500 md:text-lg">
                  Add your business details. Get your account to start
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={HIW3} alt="image" />

                <h3>3. Integrate</h3>
                <p className="text-gray-500 md:text-lg">
                  You can easily connect our safe payment software to your CRM
                  or Point of Sale.
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={HIW6} alt="image" />

                <h3>4. Accept Payment</h3>
                <p className="text-gray-500 md:text-lg">
                  Begin receiving payments from your customers online, in-store,
                  and mobile.
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={MF} alt="image" />

                <h3>5. Manage your Funds</h3>
                <p className="text-gray-500 md:text-lg">
                  Monitor and Control your finances using real-time reporting
                  and analytics.
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-sm-6 col-md-6">
              <div className="single-how-it-works">
                <Image src={DB} alt="image" />

                <h3>6. Deposit in your Bank</h3>
                <p className="text-gray-500 md:text-lg">
                  Get your money fast. No more holding up funds for days/weeks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
