import React from "react";
import SS1 from "@/../public/img/2.png";
import SS2 from "@/../public/img/circle.png";
import Image from "next/image";

export default function StartServicesArea() {
  return (
    <div>
      <section className="services-area ptb-70 bg-f7fafd">
        <div className="container-fluid p-0">
          <div className="overview-box container flex-col lg:flex-row w-screen">
            <div className="overview-image !max-w-full !px-0">
              <div className="image px-9">
                <Image src={SS1} alt="image" />

                <div className="circle-img">
                  <Image src={SS2} alt="image" />
                </div>
              </div>
            </div>

            <div className="overview-content !max-w-full !px-0">
              <div className="content px-2">
                <h3>
                  <strong>
                    NGsnair Payments For Small- to Medium-Sized Businesses
                  </strong>
                </h3>
                <div className="bar"></div>
                <p>
                  From small to medium business, NGnair Payments provides the
                  tools you need to seamlessly manage payments and track
                  transactions. Small businesses do not get small resources with
                  us. We help secure your funds with easy dispute resolution,
                  fast deposit channels, fast support, no expensive changes. You
                  keep using things you already use, and we&apos;re here to help
                  you whenever you need it. We make sure your money is safe and
                  help you grow your business smoothly.
                </p>

                <ul className="services-list">
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Quick and Easy
                      Setup
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Quick transaction
                      resolution
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Integration with
                      existing systems
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Real-time
                      reporting and analytics
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Flexible billing
                      and subscription options
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Secure & reliable
                      transaction processing
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
