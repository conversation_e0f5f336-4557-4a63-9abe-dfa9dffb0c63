import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface PartnerFormSubmitProps {
  onCancel: () => void;
  loading: boolean;
  disabled: boolean;
}

export function PartnerFormSubmit({
  onCancel,
  loading,
  disabled,
}: PartnerFormSubmitProps) {

  return (
    <div className="flex justify-end gap-3 pt-4">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={loading}
      >
        Cancel
      </Button>
      <Button
        type="submit"
        disabled={disabled || loading}
      >
        {loading ? (
          <span className="flex items-center gap-2">
            <svg
              className="animate-spin h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v8z"
              />
            </svg>
            Submitting...
          </span>
        ) : (
          "Submit Application"
        )}
      </Button>
    </div>
  );
}
