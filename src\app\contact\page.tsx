"use client";

import { ArrowR<PERSON>, Users, Mail, Phone, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";
import { Suspense } from "react";
const PartnerModal = dynamic(() => import("@/components/partner-modal"), {
  ssr: false,
});
import HeroSection from "@/components/hero-section";
import CTABanner from "@/components/cta-banner";

export default function ContactPage() {
  const contactOptions = [
    {
      title: "Partner Sign‑Up",
      description:
        "Ready to launch NGnair in your market? Become a partner and unlock NGnair POS devices, AI underwriting, and real‑time rails.",
      icon: <Users className="size-6" />,
      cta: "Become a partner",
      gradient: "from-blue-500 to-cyan-500",
    },
  ];

  const contactInfo = [
    {
      title: "Email",
      value: "<EMAIL>",
      icon: <Mail className="size-5" />,
    },
    {
      title: "Phone",
      value: "+****************",
      icon: <Phone className="size-5" />,
    },
    {
      title: "Address",
      value: "123 Market Street, San Francisco, CA 94105",
      icon: <MapPin className="size-5" />,
    },
  ];

  return (
    <div className="flex min-h-[100dvh] flex-col bg-background">
      <main className="flex-1">
        <HeroSection
          badge={
            <Badge
              className="rounded-full px-4 py-1.5 text-sm font-medium"
              variant="secondary"
            >
              Contact & CTAs
            </Badge>
          }
          headline={
            <>
              Let's Transform Your{" "}
              <span className="text-accent-custom">Payment Operations</span>
            </>
          }
          subtext={
            <>
              Ready to modernize your payment infrastructure? Choose how you'd
              like to get started with NGnair.
            </>
          }
          rightIcon={<Users className="size-8" />}
          rightTitle="Get Started"
          rightSubtitle="Join our partner network"
          rightGradient="from-blue-500 to-green-500"
        />

        {/* Contact Options */}
        <section className="w-full py-20 md:py-14">
          <div className="container mx-auto px-4 md:px-6">
            <div className="grid gap-8 lg:grid-cols-1 max-w-md mx-auto">
              {contactOptions.map((option, index) => (
                <div key={index}>
                  <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg hover:border-primary/20">
                    <CardContent className="p-8 text-center">
                      <div
                        className={`p-4 rounded-xl bg-gradient-to-br ${option.gradient} text-white w-fit mx-auto mb-6`}
                      >
                        {option.icon}
                      </div>
                      <h3 className="font-semibold text-xl mb-4">
                        {option.title}
                      </h3>
                      <p className="text-muted-foreground mb-8">
                        {option.description}
                      </p>
                      <Suspense
                        fallback={
                          <div className="flex justify-center items-center h-12">
                            <span className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></span>{" "}
                            Loading...
                          </div>
                        }
                      >
                        <PartnerModal>
                          <Button className="w-full rounded-full">
                            {option.cta}
                            <ArrowRight className="ml-2 size-4" />
                          </Button>
                        </PartnerModal>
                      </Suspense>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Information Only */}
        <section className="w-full py-20 md:py-32 bg-muted/30">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-2xl mx-auto">
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-center">
                    Contact Information
                  </h3>
                  <div className="space-y-6">
                    {contactInfo.map((info, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-4 justify-center"
                      >
                        <div className="p-3 rounded-lg bg-primary/10 text-primary">
                          {info.icon}
                        </div>
                        <div>
                          <p className="font-medium">{info.title}</p>
                          <p className="text-muted-foreground">{info.value}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="text-center">
                  <h4 className="text-lg font-semibold mb-4">Office Hours</h4>
                  <div className="space-y-2 text-muted-foreground">
                    <p>Monday - Friday: 9:00 AM - 6:00 PM PST</p>
                    <p>Saturday: 10:00 AM - 2:00 PM PST</p>
                    <p>Sunday: Closed</p>
                  </div>
                </div>

                <div className="text-center">
                  <h4 className="text-lg font-semibold mb-4">Global Support</h4>
                  <p className="text-muted-foreground">
                    Our support team operates across multiple time zones to
                    provide assistance when you need it most.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <CTABanner />
      </main>
    </div>
  );
}
