import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { phoneCodes } from "@/data/phoneCode";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface PartnerBasicInfoFieldsProps {
  formData: {
    firstName: string;
    lastName: string;
    businessName: string;
    email: string;
    phone: string;
  };
  setFormData: React.Dispatch<React.SetStateAction<any>>;
}

export function PartnerBasicInfoFields({
  formData,
  setFormData,
}: PartnerBasicInfoFieldsProps) {
  const [selectedPhoneCode, setSelectedPhoneCode] = useState("+1");
  const [phoneNumber, setPhoneNumber] = useState("");

  // Update the formData when either code or number changes
  const handlePhoneChange = (codeCountry: string, number: string) => {
    // codeCountry is now in the format '+1-United States'
    const [code] = codeCountry.split("-");
    // Only set phone if number is not empty
    const fullPhone = number.trim() ? `${code}${number}` : "";
    setFormData((prev: any) => ({ ...prev, phone: fullPhone }));
  };

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name *</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) =>
              setFormData((prev: any) => ({
                ...prev,
                firstName: e.target.value,
              }))
            }
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name *</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) =>
              setFormData((prev: any) => ({
                ...prev,
                lastName: e.target.value,
              }))
            }
            required
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="businessName">Business Name *</Label>
        <Input
          id="businessName"
          value={formData.businessName}
          onChange={(e) =>
            setFormData((prev: any) => ({
              ...prev,
              businessName: e.target.value,
            }))
          }
          required
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData((prev: any) => ({ ...prev, email: e.target.value }))
            }
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="phone">Phone *</Label>
          <div className="flex gap-2">
            <Select
              value={selectedPhoneCode}
              onValueChange={(codeCountry) => {
                setSelectedPhoneCode(codeCountry);
                handlePhoneChange(codeCountry, phoneNumber);
              }}
            >
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {phoneCodes.map((item) => (
                  <SelectItem
                    key={item.code + "-" + item.country}
                    value={item.code + "-" + item.country}
                  >
                    {item.flag} {item.code}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              id="phone"
              type="tel"
              placeholder="************"
              value={phoneNumber}
              onChange={(e) => {
                const number = e.target.value.replace(/[^\d\-\s]/g, ""); // Allow only digits, dashes, spaces
                setPhoneNumber(number);
                handlePhoneChange(selectedPhoneCode, number);
              }}
              className="flex-1"
              required
              minLength={6}
              pattern="[0-9\-\s]+"
            />
          </div>
        </div>
      </div>
    </>
  );
}
