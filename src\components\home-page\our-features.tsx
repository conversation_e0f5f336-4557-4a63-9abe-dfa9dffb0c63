import React from "react";
import OF1 from "@/../public/img/features-img1.png";
import Image from "next/image";

export default function OurFeature() {
  return (
    <div>
      <section className="features-section ptb-70 bg-f7fafd">
        <div className="container">
          <div className="section-title" style={{ maxWidth: "1040px" }}>
            <h2>Our Features</h2>
            <div className="bar"></div>
            <p style={{ maxWidth: "900px" }}>
              We offer good controlling and managing finances , smooth and easy
              integration, transparent fees, scalable solutions, and a deposit
              guarantee for peace of mind.
            </p>
          </div>
        </div>

        <div className="container-fluid">
          <div className="row align-items-center">
            <div className="col-lg-5 col-md-12">
              <div className="features-box-list">
                <div className="row">
                  <div className="col-lg-12 col-sm-6 col-md-6">
                    <div className="features-box">
                      <div className="icon">
                        <i className="flaticon-settings"></i>
                      </div>

                      <h3>OmniChannel Transaction Portal</h3>
                      <p>
                        View and Manage all transactions with your business from
                        a single system. Easily navigate within a few clicks to
                        find a transaction, global search, manage subscriptions,
                        enable card vault, enable card updates.
                      </p>
                    </div>
                  </div>

                  <div className="col-lg-12 col-sm-6 col-md-6">
                    <div className="features-box">
                      <div className="icon bg-f78acb">
                        <i className="flaticon-envelope-of-white-paper"></i>
                      </div>

                      <h3>Smooth Integration</h3>
                      <p>
                        Experience effortless integration with CRM, ERP and POS
                        systems and many more.
                      </p>
                    </div>
                  </div>

                  <div className="col-lg-12 col-sm-6 col-md-6">
                    <div className="features-box">
                      <div className="icon bg-cdf1d8">
                        <i className="flaticon-menu"></i>
                      </div>

                      <h3>Transparent Fees</h3>
                      <p>
                        Enjoy clear, upfront fee structures with no hidden
                        charges.
                      </p>
                    </div>
                  </div>

                  <div className="col-lg-12 col-sm-6 col-md-6">
                    <div className="features-box">
                      <div className="icon bg-c679e3">
                        <i className="flaticon-info"></i>
                      </div>

                      <h3>Manage your Funds</h3>
                      <p>
                        Monitor and Control your finances using real-time
                        reporting and analytics.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-7 col-md-12">
              <div className="features-image">
                <Image src={OF1} alt="image" />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
