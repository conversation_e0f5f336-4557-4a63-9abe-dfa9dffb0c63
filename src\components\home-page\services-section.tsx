import { FileText, Brain, Smartphone, BarChart, Users, Shield } from "lucide-react";
import { Card, CardContent } from "../ui/card";

  const services = [
    {
      title: "Merchant Onboarding & KYC/KYB",
      description: "Automated forms, document capture, checks, and workflows for faster merchant approvals.",
      icon: <FileText className="size-6" />,
    },
    {
      title: "AI Underwriting Engine",
      description: "Instant decisions with configurable rules and ML signals per processor program.",
      icon: <Brain className="size-6" />,
    },
    {
      title: "NGnair POS Marketplace",
      description: "Curated POS devices and software bundles for your merchant customers.",
      icon: <Smartphone className="size-6" />,
    },
    {
      title: "Transaction Lifecycle & Reporting",
      description: "From authorization to deposit — searchable, exportable, auditable transaction data.",
      icon: <BarChart className="size-6" />,
    },
    {
      title: "Multi‑Agent Pay Splits",
      description: "Assign multiple agents to merchants with transparent splits and commissions.",
      icon: <Users className="size-6" />,
    },
    {
      title: "Open Banking Integration",
      description: "Instant bank account verification and linking to reduce fraud and friction.",
      icon: <Shield className="size-6" />,
    },
  ]

export default function ServicesSection() {
  return (

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {services.map((service, idx) => (
            <div key={idx} >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md">
                    <CardContent className="p-6 flex flex-col h-full">
                      <div className="size-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary mb-4">
                        {service.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                      <p className="text-muted-foreground">{service.description}</p>
                    </CardContent>
                  </Card>
            </div>
          ))}
        </div>

  );
}
