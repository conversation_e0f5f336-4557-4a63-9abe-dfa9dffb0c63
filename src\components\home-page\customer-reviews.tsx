'use client'
import React, { Component } from "react";
import { useState, useEffect } from 'react';
import Carousel from 'react-bootstrap/Carousel';
import Image, { StaticImageData } from 'next/image';
import CR1 from '@/../public/img/client-image/1.jpg';
import CR2 from '@/../public/img/client-image/2.jpg';
import CR3 from '@/../public/img/client-image/3.jpg';
import CR4 from '@/../public/img/client-image/4.jpg';
import CR5 from '@/../public/img/client-image/5.jpg';
import Slider from "react-slick";

export interface ChatBoxDataType {
	index: number;
	value: {
		name?: string;
		description?: string;
		address?: string;
		img?: any;
		position?: string;
	}
}
export default function CustomerReviews() {

	const [chatBoxData, setChatBoxData] = useState<ChatBoxDataType>({
		index: 0,
		value: {}
	});

	const settings = {
		className: "center",
		centerMode: true,
		focusOnSelect: true,
		draggable: true,
		infinite: true,
		centerPadding: "5px",
		slidesToShow: 5,
		autoplay: true,
		autoplaySpeed: 2000,
		afterChange: (currentSlide: any) => setChatBoxData({ ...chatBoxData, index: currentSlide }),
	};

	const testiMonials = [
		{
			name: 'John Lucy',
			description: 'Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
			address: 'USA',
			img: CR1,
			position: 'web developer'
		},
		{
			name: 'John Smith',
			description: 'Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
			address: 'USA',
			img: CR2,
			position: 'Mobile App developer'
		},
		{
			name: 'Maxwel Warner',
			description: 'Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
			address: 'USA',
			img: CR3,
			position: 'Mobile App developer'

		},
		{
			name: 'Ross Taylor',
			description: 'Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
			address: 'USA',
			img: CR4,
			position: 'App developer'

		},
		{
			name: 'James Anderson',
			description: 'Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
			address: 'USA',
			img: CR5,
			position: 'Web developer'

		}
	];

	useEffect(() => {
		const chatBoxValue = testiMonials?.find((item, idx) => idx == chatBoxData?.index)
		setChatBoxData({ ...chatBoxData, value: { ...chatBoxValue } })
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [chatBoxData?.index])


	return (
		<section className="feedback-area ptb-70 bg-f7fafd">
			<div className="container">
				<div className="section-title">
					<h2>What customers say about Us</h2>
					<div className="bar"></div>
					<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
				</div>

				<div className="container flex justify-center">
					<div className="!w-[50%] p-4 talk-bubble tri-right left-top">
						<div className="talktext flex">
							<div className="w-2/3">
								{chatBoxData && <Image src={chatBoxData?.value?.img} alt="carousel item image" className="rounded-full border-2 border-green-400 p-1 !opacity-100" />}
							</div>
							<div className=" flex flex-col items-start ">
								<h3 className="text-lg">{chatBoxData.value.name}</h3>
								<p className="text-xs text-gray-400">{chatBoxData?.value?.position}</p>
								<p className="text-sm text-gray-600">{chatBoxData?.value?.description}</p>
							</div>
						</div>
					</div>
				</div>
				<div className="w-full px-[30%]">
					<Slider {...settings} className="!p-0 !m-0"	>
						{
							testiMonials?.map((item, index) => {
								return (
									<div key={index} className="rounded-full !w-[100px]" >
										<Image src={item?.img} alt="carousel item image" className="rounded-full border-2 border-green-400 p-1 !opacity-100" />
									</div>
								)
							})
						}
					</Slider>
				</div>

				<div className="client-thumbnails">
					<div className='w-2/5 mx-auto'>


					</div>

				</div>
			</div>
		</section>
	);
}
