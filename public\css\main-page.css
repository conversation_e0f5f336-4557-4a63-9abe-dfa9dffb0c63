.nav-items{
    font-size: 17px;
    font-weight: 500;
    color: #0e314c;
    text-transform: capitalize;
    font-family: 'Raleway', sans-serif;
    margin-inline: 15px;
}
.robotfonts{
    font-family: '<PERSON>o', sans-serif;
}
.ralwayfonts{
    font-family: 'Raleway', sans-serif;

}
.nav-items:hover{
    
    color: #234390;
}

.banner-section {
    position: relative;
    z-index: 1;
    background: transparent url(../img/shape-bg.png) left bottom no-repeat;
  }
/* #######################################33 */
/* Banner Setion */


body {
  padding: 0;
  margin: 0;
  font-family: "Roboto", sans-serif;
  font-size: 15px;
}
Link{
    text-decoration: none;
    
}
img {
  max-width: 100%;
}

.d-table {
  width: 100%;
  height: 100%;
}
.d-table-cell {
  vertical-align: middle;
}

a {
  color: #0e314c;
  text-decoration: none;
  transition: 0.5s;
  outline: 0 !important;
}
a:hover {
  color: #22418E;
  text-decoration: none;
}

button {
  outline: 0 !important;
}

.ptb-70 {
  padding-top: 70px;
  padding-bottom: 70px;
}

.bg-f7fafd {
  background-color: #f7fafd;
}

.bg-f6f4f8 {
  background-color: #f6f4f8;
}

.bg-f4fcff {
  background-color: #f4fcff;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  font-family: "Raleway", sans-serif;
}

p {
  line-height: 1.7;
  margin-bottom: 15px;
  color: #6084a4;
  font-weight: 300;
  font-size: 15px;
}
p:last-child {
  margin-bottom: 0;
}

.mfp-bg {
  position: fixed !important;
}

.mfp-wrap {
  position: fixed !important;
}

.mfp-container {
  position: fixed;
}

/*btn btn-primary*/
.btn {
  font-weight: 700;
  border: none;
  padding: 14.5px 30px;
  text-transform: uppercase;
  font-size: 14px;
  line-height: initial;
  border-radius: 4px;
  transition: 0.5s;
  position: relative;
  z-index: 1;
}
.btn.disabled, .btn:disabled {
  opacity: 1;
}

.btn-primary {
  background-color: transparent;
  color: #ffffff;
}
.btn-primary::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 4px;
  transition: 0.5s;
}
.btn-primary::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 4px;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.btn-primary:hover, .btn-primary:focus {
  background-color: transparent !important;
  color: #ffffff !important;
  box-shadow: unset !important;
}
.btn-primary:hover::before, .btn-primary:focus::before {
  opacity: 0;
  visibility: hidden;
}
.btn-primary:hover::after, .btn-primary:focus::after {
  opacity: 1;
  visibility: visible;
}

/*section-title*/
.section-title {
  text-align: center;
  max-width: 720px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50px;
  margin-top: -9px;
}
.section-title h2 {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.section-title .bar {
  height: 5px;
  width: 90px;
  background: #cdf1d8;
  margin: 15px auto 20px;
  position: relative;
  border-radius: 30px;
}
.section-title .bar::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2.6px;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #44ce6f;
  animation-duration: 3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: MOVE-BG;
}
.section-title p {
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
}

/*form-control*/
.form-control {
  height: 50px;
  padding: 0 15px;
  font-size: 15px;
  line-height: 50px;
  color: #0e314c;
  background-color: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 0;
  transition: 0.5s;
}
.form-control:focus {
  box-shadow: unset !important;
  border-color: #22418E;
}

/*================================================
Preloader Area CSS
=================================================*/
.preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  top: 0;
  left: 0;
}
.preloader .loader {
  position: absolute;
  top: 43%;
  left: 0;
  right: 0;
  transform: translateY(-43%);
  text-align: center;
  margin: 0 auto;
  width: 50px;
  height: 50px;
}
.preloader .box {
  width: 100%;
  height: 100%;
  background: #ffffff;
  animation: animate 0.5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
.preloader .shadow {
  width: 100%;
  height: 5px;
  background: #000;
  opacity: 0.1;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: shadow 0.5s linear infinite;
}

@keyframes loader {
  0% {
    left: -100px;
  }
  100% {
    left: 110%;
  }
}
@keyframes animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes shadow {
  50% {
    transform: scale(1.2, 1);
  }
}
/*================================================
Navbar Area CSS
=================================================*/
.luvion-responsive-nav {
  display: none;
}

.navbar-brand {
  padding-top: 0;
  padding-bottom: 0;
}
.navbar-brand img {
  transition: 0.5s;
}
.navbar-brand img:nth-child(2) {
  display: none;
}

.luvion-nav {
  background-color: transparent;
  padding-top: 0;
  padding-bottom: 0;
  padding-right: 0;
  padding-left: 0;
}
.luvion-nav .navbar {
  padding-right: 0;
  padding-top: 0;
  padding-left: 0;
  padding-bottom: 0;
}
.luvion-nav .navbar ul {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 0;
}
.luvion-nav .navbar .navbar-nav {
  font-family: "Raleway", sans-serif;
  margin-left: auto;
}
.luvion-nav .navbar .navbar-nav .nav-item {
  position: relative;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 0;
  padding-right: 0;
}
.luvion-nav .navbar .navbar-nav .nav-item a {
  font-size: 17px;
  font-weight: 500;
  color: #e5e1e1;
  text-transform: capitalize;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-left: 15px;
  margin-right: 15px;
}
.luvion-nav .navbar .navbar-nav .nav-item a:hover, .luvion-nav .navbar .navbar-nav .nav-item a:focus, .luvion-nav .navbar .navbar-nav .nav-item a.active {
  color: #ffffff;
}
.luvion-nav .navbar .navbar-nav .nav-item a i {
  font-size: 10px;
  margin-left: 1px;
}
.luvion-nav .navbar .navbar-nav .nav-item:last-child a {
  margin-right: 0;
}
.luvion-nav .navbar .navbar-nav .nav-item:first-child a {
  margin-left: 0;
}
.luvion-nav .navbar .navbar-nav .nav-item:hover a, .luvion-nav .navbar .navbar-nav .nav-item.active a {
  color: #ffffff;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu {
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  position: absolute;
  border: none;
  top: 80px;
  left: 0;
  width: 270px;
  z-index: 99;
  display: block;
  opacity: 0;
  visibility: hidden;
  border-radius: 0;
  transition: all 0.3s ease-in-out;
  padding-top: 20px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 20px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li {
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {
  text-transform: capitalize;
  padding: 8px 15px;
  margin: 0;
  color: #0e314c;
  font-size: 15.5px;
  font-weight: 500;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu {
  left: -270px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu {
  left: 220px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  left: -270px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  left: -270px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  left: -270px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  left: -270px;
  top: 0;
  opacity: 0;
  visibility: hidden;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {
  color: #22418E;
}
.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: -15px;
}
.luvion-nav .navbar .navbar-nav .nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  top: 100%;
}
.luvion-nav .navbar .others-options {
  margin-left: 25px;
}
.luvion-nav .navbar .others-options .login-btn {
  color: #ffffff;
  font-size: 17px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.luvion-nav .navbar .others-options .login-btn i {
  margin-right: 2px;
  color: #eae563;
}
.luvion-nav .navbar .others-options .login-btn:hover {
  color: #ffffff;
}

.navbar-area {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
  z-index: 999;
  background-color: transparent;
  transition: 0.5s;
  padding-top: 20px;
  padding-bottom: 20px;
}
.navbar-area.is-sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.06);
  background-color: #ffffff !important;
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  transition: 0.5s;
}
.navbar-area.is-sticky .navbar-brand img:nth-child(1) {
  display: none;
}
.navbar-area.is-sticky .navbar-brand img:nth-child(2) {
  display: block;
}
.navbar-area.is-sticky .luvion-nav {
  background-color: #ffffff;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn {
  color: #0e314c;
}
.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn i {
  color: #22418E;
}
.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn:hover {
  color: #22418E;
}
.navbar-area.navbar-style-two .navbar-brand img {
  display: block !important;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn i {
  color: #0e314c;
}
.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn:hover {
  color: #22418E;
}

.luvion-responsive-nav .mean-container a.meanmenu-reveal span {
  top: 8px;
  height: 4px;
  margin-top: -6px;
  position: relative;
}

/* Override the hover styles for the login button */
.navbar-area .luvion-nav .navbar .others-options .btn-secondary:hover,
.navbar-area .luvion-nav .navbar .others-options .btn-secondary:focus {
  background-color: #234390 !important; /* Keep the blue background */
  color: #ffffff !important; /* Keep the text white */
  opacity: 1;
}

/* This targets both light and dark themes */
.theme-light .navbar-area .luvion-nav .navbar .others-options .btn-secondary:hover,
.theme-dark .navbar-area .luvion-nav .navbar .others-options .btn-secondary:hover {
  background-color: #234390 !important;
  color: #ffffff !important;
}

/* Ensure the transition doesn't change the color */
.navbar-area .luvion-nav .navbar .others-options .btn-secondary::before,
.navbar-area .luvion-nav .navbar .others-options .btn-secondary::after {
  opacity: 0 !important;
  visibility: hidden !important;
}

@media only screen and (max-width: 991px) {
  .luvion-responsive-nav {
    display: block;
  }
  .luvion-responsive-nav .luvion-responsive-menu {
    position: relative;
  }
  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul {
    font-size: 14px;
  }
  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul li a.active {
    color: #22418E;
  }
  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul li li a {
    font-size: 13.5px;
  }
  .luvion-responsive-nav .luvion-responsive-menu.mean-container .navbar-nav {
    overflow-y: scroll;
    height: 350px;
    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);
  }
  .luvion-responsive-nav .mean-container a.meanmenu-reveal {
    color: #ffffff;
  }
  .luvion-responsive-nav .mean-container a.meanmenu-reveal span {
    background: #ffffff;
  }
  .luvion-responsive-nav .logo {
    position: relative;
    width: 50%;
    z-index: 999;
  }
  .luvion-responsive-nav .logo a img:nth-child(2) {
    display: none;
  }
  .luvion-responsive-nav .others-options {
    position: absolute;
    right: 52px;
    top: 3px;
  }
  .luvion-responsive-nav .others-options .login-btn {
    color: #ffffff;
    font-size: 15px;
    font-weight: 400;
    font-family: "Raleway", sans-serif;
  }
  .luvion-responsive-nav .others-options .login-btn i {
    margin-right: 2px;
    color: #eae563;
  }
  .luvion-responsive-nav .others-options .login-btn:hover {
    color: #22418E;
  }
  .navbar-area {
    border-bottom: 1px solid rgba(255, 255, 255, 0.09);
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .navbar-area.is-sticky {
    border-bottom: none;
    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);
  }
  .navbar-area.is-sticky .luvion-responsive-nav .mean-container a.meanmenu-reveal {
    color: #0e314c;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .mean-container a.meanmenu-reveal span {
    background: #0e314c;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .logo a img:nth-child(1) {
    display: none;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .logo a img:nth-child(2) {
    display: block;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn {
    color: #0e314c;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn i {
    color: #22418E;
  }
  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn:hover {
    color: #22418E;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .mean-container a.meanmenu-reveal {
    color: #0e314c;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .mean-container a.meanmenu-reveal span {
    background: #0e314c;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .logo a img {
    display: inline-block !important;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .others-options .login-btn {
    color: #0e314c;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .others-options .login-btn i {
    color: #22418E;
  }
  .navbar-area.navbar-style-two .luvion-responsive-nav .others-options .login-btn:hover {
    color: #22418E;
  }
  .luvion-nav {
    display: none;
  }
}
/*================================================
Main Banner Area CSS
=================================================*/
.main-banner {
  height: 100vh;
  background-image: url(../img/main-banner1.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.main-banner-content {
  margin-top: -20px;
  max-width: 600px;
}
.main-banner-content h1 {
  margin-bottom: 0;
  color: #ffffff;
  font-size: 52px;
  font-weight: 700;
}
.main-banner-content p {
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-size: 18px;
  max-width: 400px;
  margin-top: 20px;
  margin-bottom: 0;
}
.main-banner-content .btn {
  margin-top: 30px;
}

.main-banner-section {
  height: 800px;
  background-image: url(../img/main-banner2.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.banner-content {
  margin-top: -50px;
}
.banner-content h1 {
  margin-bottom: 0;
  color: #ffffff;
  font-size: 50px;
  font-weight: 700;
}
.banner-content p {
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-size: 18px;
  max-width: 400px;
  margin-top: 20px;
  margin-bottom: 0;
}
.banner-content .btn {
  margin-top: 30px;
}

.money-transfer-form {
  background-color: #ffffff;
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  padding: 30px;
  border-radius: 5px;
  margin-top: -50px;
  margin-left: 100px;
}
.money-transfer-form::before {
  content: "";
  position: absolute;
  z-index: -1;
  background: #ffffff;
  width: 96%;
  opacity: 0.62;
  height: 50%;
  bottom: -10px;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 3px;
}
.money-transfer-form::after {
  content: "";
  position: absolute;
  z-index: -1;
  background: #ffffff;
  width: 96%;
  opacity: 0.62;
  height: 50%;
  top: -10px;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 3px;
}
.money-transfer-form form {
  text-align: center;
}
.money-transfer-form form .form-group {
  margin-bottom: 15px;
  position: relative;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  padding: 36px 10px 12px 15px;
  border-radius: 5px;
}
.money-transfer-form form .form-group .amount-currency-select {
  right: 0;
  top: 0;
  position: absolute;
  height: 100%;
}
.money-transfer-form form .form-group .nice-select {
  border: none;
  background-color: #0e314c;
  border-radius: 0 5px 5px 0;
  height: 100%;
  padding: 0 35px 0 25px;
  line-height: 72px;
  font-size: 17px;
  font-weight: 700;
}
.money-transfer-form form .form-group .nice-select:after {
  right: 20px;
  border-color: #ffffff;
  width: 8px;
  height: 8px;
}
.money-transfer-form form .form-group .nice-select span {
  color: #ffffff;
}
.money-transfer-form form .form-group .nice-select .list {
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  width: 100%;
  padding-top: 20px;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 20px;
  margin-top: 0;
  margin-bottom: 0;
}
.money-transfer-form form .form-group .nice-select .list .option {
  line-height: initial;
  min-height: auto;
  text-align: center;
  margin-top: 12px;
  padding-left: 0;
  padding-right: 0;
}
.money-transfer-form form .form-group .nice-select .list .option:hover, .money-transfer-form form .form-group .nice-select .list .option:focus, .money-transfer-form form .form-group .nice-select .list .option.focus, .money-transfer-form form .form-group .nice-select .list .option.selected {
  background-color: transparent;
}
.money-transfer-form form .form-group .nice-select .list .option:hover {
  color: #22418E;
}
.money-transfer-form form .form-group .nice-select .list .option:first-child {
  margin-top: 0;
}
.money-transfer-form form .form-group label {
  position: absolute;
  left: 15px;
  top: 10px;
  color: #f9f9f9;
  margin-bottom: 0;
  display: block;
  font-weight: 300;
  font-size: 13.5px;
}
.money-transfer-form form .form-group .form-control {
  background-color: transparent;
  border: none;
  padding-left: 0;
  height: auto;
  line-height: initial;
  padding-right: 95px;
  color: #ffffff;
  font-size: 17px;
  font-weight: 500;
}
.money-transfer-form form .form-group .form-control::-moz-placeholder {
  color: #ffffff;
}
.money-transfer-form form .form-group .form-control::placeholder {
  color: #ffffff;
}
.money-transfer-form form .currency-info {
  margin-bottom: 25px;
  margin-top: 25px;
  text-align: left;
  position: relative;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 20px;
}
.money-transfer-form form .currency-info .bar {
  height: 100%;
  width: 2px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.money-transfer-form form .currency-info .bar::before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  content: "";
  position: absolute;
  top: -1px;
  left: -2px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.money-transfer-form form .currency-info .bar::after {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  content: "";
  position: absolute;
  bottom: -1px;
  left: -2px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.money-transfer-form form .currency-info span {
  display: inline-block;
  color: #6084a4;
  font-size: 14px;
}
.money-transfer-form form .currency-info span strong {
  color: #0e314c;
}
.money-transfer-form form .currency-info span:last-child {
  margin-left: 15px;
}
.money-transfer-form form .money-transfer-info {
  color: #6084a4;
  font-size: 14px;
}
.money-transfer-form form .money-transfer-info strong {
  display: block;
  margin-top: 3px;
  color: #0e314c;
  font-size: 16px;
}
.money-transfer-form form button {
  margin-top: 15px;
}
.money-transfer-form form .btn::before {
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
}
.money-transfer-form form .btn::after {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.money-transfer-form form .terms-info {
  margin-top: 15px;
}
.money-transfer-form form .terms-info p {
  font-size: 14px;
}
.money-transfer-form form .terms-info p a {
  display: inline-block;
  color: #0e314c;
}
.money-transfer-form form .terms-info p a:hover {
  color: #22418E;
}

.banner-section {
  position: relative;
  z-index: 1;
  background: transparent url(../img/shape-bg.png) left bottom no-repeat;

}

.hero-content {
  margin-top: -50px;
}
.hero-content h1 {
  margin-bottom: 0;
  font-size: 50px;
  font-weight: 700;
}
.hero-content p {
  font-family: "Raleway", sans-serif;
  font-size: 18px;
  max-width: 400px;
  margin-top: 20px;
  margin-bottom: 0;
}
.hero-content .btn {
  margin-top: 30px;
}

.hero-image {
  text-align: center;
  position: relative;
  z-index: 1;
}
.hero-image .main-image img:nth-child(2) {
  position: absolute;
  left: -30px;
  bottom: 60px;
  border-radius: 5px;
}
.hero-image .main-image img:nth-child(3) {
  position: absolute;
  right: -80px;
  top: 110px;
}
.hero-image .main-mobile-image {
  display: none;
}
.hero-image .circle-image {
  position: absolute;
  left: 0;
  right: 0;
  top: -15px;
  margin: 0 auto;
  z-index: -1;
}
.hero-image .video-btn {
  display: inline-block;
  position: absolute;
  z-index: 1;
  color: #ffffff;
  border-radius: 30px;
  padding: 10px 25px;
  bottom: 55px;
  left: 50%;
  transform: translateX(-50%);
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  font-size: 15px;
}
.hero-image .video-btn i {
  margin-right: 2px;
}
.hero-image .video-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  z-index: -1;
  border-radius: 30px;
  transition: 0.5s;
}
.hero-image .video-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 30px;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.hero-image .video-btn:hover::after {
  opacity: 1;
  visibility: visible;
}
.hero-image .video-btn:hover::before {
  opacity: 0;
  visibility: hidden;
}

.home-area {
  z-index: 1;
  position: relative;
  padding-top: 200px;
  padding-bottom: 250px;
  background-image: url(../img/banner-image/banner-bg.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.home-area::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  z-index: -1;
  opacity: 0.85;
  position: absolute;
  background-color: #3059bc;
}
.home-area:hover .home-slides.owl-theme .owl-nav {
  visibility: visible;
  opacity: 1;
}

.banner-item {
  position: relative;
}

.banner-item-content {
  z-index: 1;
  position: relative;
}
.banner-item-content h1 {
  margin-bottom: 0;
  color: #ffffff;
  font-size: 60px;
  font-weight: 700;
}
.banner-item-content p {
  color: #ffffff;
  max-width: 500px;
  font-family: "Raleway", sans-serif;
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 0;
}
.banner-item-content .btn {
  margin-top: 30px;
}

.banner-item-image {
  margin-left: -48px;
}

.home-slides.owl-theme .owl-nav {
  top: 50%;
  opacity: 0;
  right: -25px;
  margin-top: 0;
  position: absolute;
  visibility: hidden;
  transition: 0.5s;
  transform: translateY(-50%);
}
.home-slides.owl-theme .owl-nav [className*=owl-] {
  padding: 0;
  width: 50px;
  height: 50px;
  margin: 5px 0;
  display: block;
  font-size: 20px;
  border-radius: 0;
  color: #0e314c;
  transition: 0.5s;
  background-color: #ffffff;
}
.home-slides.owl-theme .owl-nav [className*=owl-]:hover {
  background-color: #22418E;
  color: #ffffff;
}

.owl-item.active .banner-item-content {
  overflow: hidden;
}
.owl-item.active .banner-item-content h1 {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: fadeInLeft;
  animation-delay: 0.3s;
}
.owl-item.active .banner-item-content p {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: fadeInLeft;
  animation-delay: 0.7s;
}
.owl-item.active .banner-item-content .btn {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: fadeInLeft;
  animation-delay: 0.9s;
}
.owl-item.active .banner-item-image {
  overflow: hidden;
}
.owl-item.active .banner-item-image img {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: fadeInUp;
  animation-delay: 0.7s;
}

/* New Demo */
.banner-slider-eight .main-banner {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.banner-slider-eight .main-banner.bg-8 {
  background-image: url(../img/main-banner8.jpg);
}
.banner-slider-eight.owl-carousel.owl-theme .owl-nav {
  margin-top: 0;
  transition: 0.5s;
}
.banner-slider-eight.owl-carousel.owl-theme .owl-nav [className*=owl-] {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 25px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  text-align: center;
  border: 1px solid #ffffff;
  color: #ffffff;
  transform: translateY(-50%);
  transition: all ease 0.5s;
  font-size: 20px;
  overflow: hidden;
}
.banner-slider-eight.owl-carousel.owl-theme .owl-nav [className*=owl-].owl-next {
  left: auto;
  right: 25px;
}
.banner-slider-eight.owl-carousel.owl-theme .owl-nav [className*=owl-]:hover {
  border: 1px solid #22418E;
  background-color: #ffffff;
  color: #22418E;
}

.banner-video {
  position: relative;
  overflow: hidden;
}
.banner-video video {
  position: absolute;
  left: 0;
  top: 0;
  width: auto;
  height: auto;
  min-height: 100%;
  min-width: 100%;
  z-index: -2;
}
.banner-video .main-banner-content {
  position: relative;
  overflow: hidden;
  z-index: 3;
}
.banner-video .main-banner {
  background-image: none;
}

/*================================================
About Area CSS
=================================================*/
.about-content {
  padding-right: 25px;
}
.about-content span {
  display: block;
  margin-bottom: 10px;
  text-transform: uppercase;
  color: #22418E;
  font-size: 12.5px;
  font-family: "Raleway", sans-serif;
  font-weight: 700;
}
.about-content h2 {
  font-size: 40px;
  font-weight: 700;
}
.about-content p {
  line-height: 1.8;
  color: #5d7079;
  margin-bottom: 0;
  margin-top: 12px;
}

.about-image {
  position: relative;
}
.about-image .video-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 60px;
  height: 60px;
  line-height: 61px;
  text-align: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  color: #ffffff;
  font-size: 23px;
  z-index: 1;
}
.about-image .video-btn::after {
  z-index: -1;
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 60px;
  height: 60px;
  animation: ripple 1.9s ease-out infinite;
  opacity: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
}
.about-image .video-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  z-index: -1;
  border-radius: 50%;
  transition: 0.5s;
}
.about-image .video-btn:hover::before {
  opacity: 0;
  visibility: hidden;
}

@keyframes ripple {
  0%, 35% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.8;
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}
/*================================================
Featured Boxes Area CSS
=================================================*/
.featured-boxes-area {
  position: relative;
  z-index: 1;
  margin-top: -100px;
}

.featured-boxes-inner {
  background-color: #ffffff;
  border-radius: 5px;
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);
}
.featured-boxes-inner .col-lg-3 {
  border-right: 1px solid #eeeeee;
}
.featured-boxes-inner .col-lg-3:last-child {
  border-right: none;
}

.single-featured-box {
  text-align: center;
  position: relative;
  padding: 115px 25px 40px 25px;
  overflow: hidden;
}
.single-featured-box .icon {
  transition: 0.4s;
  color: #22418E;
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translate(-50%, 0);
  margin-top: -12px;
}
.single-featured-box .icon i::before {
  font-size: 55px;
}
.single-featured-box .icon.color-fb7756 {
  color: #fb7756;
}
.single-featured-box .icon.color-facd60 {
  color: #facd60;
}
.single-featured-box .icon.color-1ac0c6 {
  color: #1ac0c6;
}
.single-featured-box h3 {
  transition: 0.4s;
  margin-bottom: 0;
  margin-top: 5px;
  font-size: 22px;
  font-weight: 600;
}
.single-featured-box p {
  transition: 0.4s;
  font-size: 14px;
  margin-top: 12px;
  margin-bottom: 0;
}
.single-featured-box .read-more-btn {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: 0.6;
  font-size: 15px;
  font-weight: 400;
}
.single-featured-box .read-more-btn::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 1px;
  width: 0;
  transition: 0.5s;
  height: 1px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.single-featured-box .read-more-btn:hover::before {
  width: 100%;
}
.single-featured-box:hover .icon {
  top: 23px;
  animation: iconTop 0.4s ease-in-out;
}
.single-featured-box:hover h3 {
  transform: translateY(-20px);
}
.single-featured-box:hover p {
  transform: translateY(-20px);
}
.single-featured-box:hover .read-more-btn {
  opacity: 1;
  visibility: visible;
  bottom: 23px;
}

@keyframes iconTop {
  0% {
    transform: translate(-50%, 0);
  }
  25% {
    opacity: 0;
    transform: translate(-50%, -70%);
  }
  50% {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}
/*================================================
Services Area CSS
=================================================*/
.services-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.overview-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center !important;
}
.overview-box .overview-content {
  flex: 0 0 50%;
  max-width: 50%;
}
.overview-box .overview-content .content {
  max-width: 640px;
  padding-left: 50px;
}
.overview-box .overview-content .content .sub-title {
  display: block;
  margin-bottom: 10px;
  text-transform: uppercase;
  color: #22418E;
  font-size: 12.5px;
  font-family: "Raleway", sans-serif;
  font-weight: 700;
}
.overview-box .overview-content .content.left-content {
  margin-left: auto;
  padding-right: 50px;
  padding-left: 0;
}
.overview-box .overview-content .content h2 {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.overview-box .overview-content .content .bar {
  height: 5px;
  width: 90px;
  background: #cdf1d8;
  margin: 20px 0 25px;
  position: relative;
  border-radius: 30px;
}
.overview-box .overview-content .content .bar::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2.7px;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #44ce6f;
  animation-duration: 3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: MOVE-BG;
}
.overview-box .overview-content .content .services-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center !important;
  padding-left: 0;
  list-style-type: none;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: 0;
  margin-top: -5px;
}
.overview-box .overview-content .content .services-list li {
  flex: 0 0 50%;
  max-width: 50%;
  padding-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
}
.overview-box .overview-content .content .services-list li span {
  display: block;
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  color: #6084a4;
  z-index: 1;
  border-radius: 5px;
  transition: 0.5s;
  padding-right: 15px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 35px;
  font-size: 14px;
  font-weight: 400;
}
.overview-box .overview-content .content .services-list li span i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #22418E;
  transition: 0.5s;
}
.overview-box .overview-content .content .services-list li span i::before {
  font-size: 12px;
}
.overview-box .overview-content .content .services-list li span::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 0;
  height: 100%;
  border-radius: 5px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.overview-box .overview-content .content .services-list li span:hover {
  color: #ffffff;
}
.overview-box .overview-content .content .services-list li span:hover i {
  color: #ffffff;
}
.overview-box .overview-content .content .services-list li span:hover::before {
  width: 100%;
}
.overview-box .overview-content .content .features-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center !important;
  padding-left: 0;
  list-style-type: none;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: 0;
  margin-top: -5px;
}
.overview-box .overview-content .content .features-list li {
  flex: 0 0 50%;
  max-width: 50%;
  padding-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
}
.overview-box .overview-content .content .features-list li span {
  display: block;
  color: #6084a4;
  position: relative;
  padding-left: 31px;
  font-size: 14px;
  font-weight: 400;
}
.overview-box .overview-content .content .features-list li span i {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #ffffff;
  transition: 0.5s;
  width: 22px;
  height: 20px;
  text-align: center;
  line-height: 18px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 3px;
}
.overview-box .overview-content .content .features-list li span i::before {
  font-size: 10px;
}
.overview-box .overview-content .content .btn {
  margin-top: 30px;
}
.overview-box .overview-image {
  flex: 0 0 50%;
  max-width: 50%;
}
.overview-box .overview-image .image {
  text-align: center;
  position: relative;
  z-index: 1;
}
.overview-box .overview-image .image .circle-img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  right: 0;
  margin: 0 auto;
}
.overview-box .overview-image .image .circle-img img {
  animation-name: rotateMe;
  animation-duration: 35s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes MOVE-BG {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(88px);
  }
}
@keyframes rotateMe {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/*================================================
Comparisons Area CSS
=================================================*/
.comparisons-table {
  background-color: #ffffff;
  box-shadow: 0px -10px 30px rgba(0, 0, 0, 0.05);
}
.comparisons-table table {
  margin-bottom: 0;
  text-align: center;
}
.comparisons-table table thead tr {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.comparisons-table table thead th {
  vertical-align: middle;
  border: none;
  color: #ffffff;
  padding: 16px 20px;
  font-family: "Raleway", sans-serif;
  font-size: 19px;
  font-weight: 600;
}
.comparisons-table table thead th:nth-child(1) {
  text-align: left;
  padding-left: 50px;
}
.comparisons-table table tbody td {
  vertical-align: middle;
  color: #0e314c;
  padding: 16px 20px;
  border-color: #eeeeee;
  font-family: "Raleway", sans-serif;
  font-size: 15px;
  font-weight: 400;
}
.comparisons-table table tbody td:nth-child(1) {
  text-align: left;
  padding-left: 50px;
}
.comparisons-table table tbody td i {
  width: 20px;
  height: 20px;
  line-height: 17px;
  border-radius: 100%;
  background-color: #22418E;
  color: #ffffff;
  display: block;
  margin: 0 auto;
}
.comparisons-table table tbody td i::before {
  font-size: 8px;
}
.comparisons-table table tbody tr:nth-child(1) td i, .comparisons-table table tbody tr:nth-child(8) td i {
  background-color: #facd60;
}
.comparisons-table table tbody tr:nth-child(2) td i, .comparisons-table table tbody tr:nth-child(9) td i {
  background-color: #44cd6f;
}
.comparisons-table table tbody tr:nth-child(3) td i, .comparisons-table table tbody tr:nth-child(10) td i {
  background-color: #fd6c28;
}
.comparisons-table table tbody tr:nth-child(5) td i, .comparisons-table table tbody tr:nth-child(12) td i {
  background-color: #1ac0c6;
}
.comparisons-table table tbody tr:nth-child(6) td i, .comparisons-table table tbody tr:nth-child(13) td i {
  background-color: #f45271;
}
.comparisons-table table tbody tr:nth-child(7) td i, .comparisons-table table tbody tr:nth-child(14) td i {
  background-color: #fd6d27;
}

/*================================================
Features Area CSS
=================================================*/
.features-area {
  padding-bottom: 50px;
}

.single-features-box {
  margin-bottom: 30px;
  background-color: #ffffff;
  border-radius: 5px;
  transition: 0.5s;
  position: relative;
  z-index: 1;
  padding: 30px;
}
.single-features-box .icon {
  width: 65px;
  height: 65px;
  text-align: center;
  line-height: 65px;
  background-color: rgba(231, 70, 69, 0.2);
  border-radius: 50%;
  color: #22418E;
  transition: 0.5s;
  margin-bottom: 18px;
}
.single-features-box .icon i::before {
  font-size: 30px;
}
.single-features-box .icon.bg-f78acb {
  background-color: rgba(247, 138, 203, 0.3);
  color: #f78acb;
}
.single-features-box .icon.bg-cdf1d8 {
  background-color: #cdf1d8;
  color: #44ce6f;
}
.single-features-box .icon.bg-c679e3 {
  color: #c679e3;
  background: #edc3fc;
}
.single-features-box .icon.bg-eb6b3d {
  color: #eb6b3d;
  background: rgba(235, 107, 61, 0.3);
}
.single-features-box h3 {
  margin-bottom: 0;
  transition: 0.5s;
  font-size: 22px;
  font-weight: 600;
}
.single-features-box h3 a {
  display: inline-block;
}
.single-features-box p {
  font-size: 14px;
  transition: 0.5s;
  margin-top: 10px;
  margin-bottom: 0;
}
.single-features-box::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 5px;
  transition: 0.5s;
}
.single-features-box:hover {
  transform: translateY(-10px);
}
.single-features-box:hover::before {
  width: 100%;
}
.single-features-box:hover .icon {
  transform: rotate(360deg);
  color: #22418E;
  background-color: #ffffff;
}
.single-features-box:hover h3 {
  color: #ffffff;
}
.single-features-box:hover h3 a {
  color: #ffffff;
}
.single-features-box:hover p {
  color: #ffffff;
}

.features-box-list {
  margin-left: auto;
  max-width: 465px;
}
.features-box-list .col-lg-12:last-child .features-box {
  margin-bottom: 0;
}

.features-box {
  margin-bottom: 20px;
  background-color: #ffffff;
  transition: 0.5s;
  position: relative;
  z-index: 1;
  padding-top: 35px;
  padding-bottom: 35px;
  padding-right: 25px;
  padding-left: 110px;
}
.features-box .icon {
  width: 65px;
  height: 65px;
  text-align: center;
  line-height: 65px;
  background-color: rgba(231, 70, 69, 0.2);
  border-radius: 50%;
  color: #22418E;
  transition: 0.5s;
  position: absolute;
  left: 25px;
  top: 35px;
}
.features-box .icon i::before {
  font-size: 30px;
}
.features-box .icon.bg-f78acb {
  background-color: rgba(247, 138, 203, 0.3);
  color: #f78acb;
}
.features-box .icon.bg-cdf1d8 {
  background-color: #cdf1d8;
  color: #44ce6f;
}
.features-box .icon.bg-c679e3 {
  color: #c679e3;
  background: #edc3fc;
}
.features-box .icon.bg-eb6b3d {
  color: #eb6b3d;
  background: rgba(235, 107, 61, 0.3);
}
.features-box h3 {
  margin-bottom: 0;
  transition: 0.5s;
  font-size: 22px;
  font-weight: 600;
}
.features-box h3 a {
  display: inline-block;
}
.features-box p {
  font-size: 14px;
  transition: 0.5s;
  margin-top: 10px;
  margin-bottom: 0;
}
.features-box::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 2px;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.features-box:hover {
  transform: translateY(-10px);
}
.features-box:hover::before {
  width: 100%;
}
.features-box:hover .icon {
  transform: rotate(360deg);
  color: #22418E;
  background-color: #ffffff;
}
.features-box:hover h3 {
  color: #ffffff;
}
.features-box:hover h3 a {
  color: #ffffff;
}
.features-box:hover p {
  color: #ffffff;
}

.features-image {
  text-align: center;
}

/*================================================
How It Works Area CSS
=================================================*/
.how-it-works-area {
  padding-bottom: 40px;
}

.single-how-it-works {
  text-align: center;
  background-color: #ffffff;
  margin-bottom: 30px;
  box-shadow: 0 7px 20px rgba(0, 0, 0, 0.03);
  padding: 36px;
  border-radius: 5px;
  transition: 0.5s;
}
.single-how-it-works img {
  display: inline-block;
  margin-bottom: 30px;
}
.single-how-it-works h3 {
  margin-bottom: 0;
  font-size: 20px;
  font-weight: 600;
}
.single-how-it-works p {
  margin-top: 12px;
  margin-bottom: 0;
}
.single-how-it-works:hover {
  transform: translateY(-10px);
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);
}

/*================================================
Team Area CSS
=================================================*/
.team-area {
  padding-bottom: 40px;
}

.single-team-member {
  display: flex;
  flex-wrap: wrap;
  align-items: center !important;
  margin-right: -10px;
  margin-left: -10px;
  margin-bottom: 30px;
}
.single-team-member .member-image {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
}
.single-team-member .member-image img {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  border-radius: 3px;
}
.single-team-member .member-image .social {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  margin: 0 auto;
  bottom: 0;
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
.single-team-member .member-image .social li {
  display: inline-block;
}
.single-team-member .member-image .social li a {
  margin: 0 2px;
  display: inline-block;
}
.single-team-member .member-image .social li a i {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 50%;
  background-color: #44ce6f;
  color: #ffffff;
  font-size: 13px;
  transition: 0.5s;
}
.single-team-member .member-image .social li a i.fa-facebook-f {
  background: #3b5998;
}
.single-team-member .member-image .social li a i.fa-linkedin-in {
  background: #0077b5;
}
.single-team-member .member-image .social li a i:hover {
  background-color: #44ce6f;
  color: #ffffff;
}
.single-team-member .member-content {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
  padding-left: 10px;
  padding-right: 10px;
}
.single-team-member .member-content h3 {
  margin-bottom: 0;
  font-size: 22px;
  font-weight: 700;
}
.single-team-member .member-content span {
  display: block;
  font-size: 13px;
  color: #22418E;
  margin-top: 8px;
}
.single-team-member .member-content p {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 10px;
}
.single-team-member:hover .member-image .social {
  bottom: 15px;
  opacity: 1;
  visibility: visible;
}

/*================================================
Invoicing Area CSS
=================================================*/
.invoicing-area {
  overflow: hidden;
}

.invoicing-content {
  margin-left: auto;
  max-width: 625px;
}
.invoicing-content h2 {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.invoicing-content .bar {
  height: 5px;
  width: 90px;
  background: #cdf1d8;
  margin: 20px 0 25px;
  position: relative;
  border-radius: 30px;
}
.invoicing-content .bar::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2.7px;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #44ce6f;
  animation-duration: 3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: MOVE-BG;
}
.invoicing-content p {
  margin-bottom: 0;
}
.invoicing-content .btn {
  margin-top: 20px;
}

.invoicing-image {
  text-align: center;
  position: relative;
  z-index: 1;
}
.invoicing-image .main-image img:nth-child(2) {
  position: absolute;
  left: 40px;
  top: 15px;
}
.invoicing-image .main-image img:nth-child(3) {
  position: absolute;
  left: 40px;
  top: 170px;
}
.invoicing-image .main-image img:nth-child(4) {
  position: absolute;
  right: 40px;
  top: 60px;
}
.invoicing-image .main-mobile-image {
  display: none;
}
.invoicing-image .circle-image img {
  position: absolute;
  z-index: -1;
}
.invoicing-image .circle-image img:nth-child(1) {
  top: -30px;
  left: 50px;
  animation: moveLeftBounce 5s linear infinite;
}
.invoicing-image .circle-image img:nth-child(2) {
  right: 50px;
  bottom: -30px;
  animation: moveBounce 5s linear infinite;
}

@keyframes moveBounce {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes moveLeftBounce {
  0% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(20px);
  }
  100% {
    transform: translateX(0px);
  }
}
/*================================================
Information Area CSS
=================================================*/
.information-area {
  padding-top: 70px;
}
.information-area .col-lg-3:last-child .single-information-box {
  padding-left: 0;
}

.single-information-box {
  position: relative;
  padding-left: 55px;
}
.single-information-box .icon {
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  border-radius: 50%;
  background-color: #22418E;
  color: #ffffff;
  position: absolute;
  left: 0;
  top: 0;
}
.single-information-box .icon i::before {
  font-size: 20px;
}
.single-information-box h3 {
  margin-bottom: 0;
  font-size: 17px;
  font-weight: 700;
}
.single-information-box p {
  line-height: initial;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 0;
}
.single-information-box .btn-box .app-store-btn {
  border-radius: 3px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 10px 15px 10px 35px;
  font-size: 11px;
}
.single-information-box .btn-box .app-store-btn i {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.single-information-box .btn-box .app-store-btn i::before {
  font-size: 20px;
}
.single-information-box .btn-box .app-store-btn span {
  display: block;
  font-size: 15px;
  font-weight: 500;
}
.single-information-box .btn-box .app-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.single-information-box .btn-box .app-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.single-information-box .btn-box .app-store-btn:hover {
  color: #ffffff;
}
.single-information-box .btn-box .app-store-btn:hover::after {
  opacity: 1;
  visibility: visible;
}
.single-information-box .btn-box .app-store-btn:hover::before {
  opacity: 0;
  visibility: hidden;
}
.single-information-box .btn-box .play-store-btn {
  margin-left: 5px;
  border-radius: 3px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 10px 15px 10px 35px;
  font-size: 11px;
}
.single-information-box .btn-box .play-store-btn i {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.single-information-box .btn-box .play-store-btn i::before {
  font-size: 20px;
}
.single-information-box .btn-box .play-store-btn span {
  display: block;
  font-size: 15px;
  font-weight: 500;
}
.single-information-box .btn-box .play-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.single-information-box .btn-box .play-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
}
.single-information-box .btn-box .play-store-btn:hover {
  color: #ffffff;
}
.single-information-box .btn-box .play-store-btn:hover::after {
  opacity: 0;
  visibility: hidden;
}
.single-information-box .btn-box .play-store-btn:hover::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Pricing Area CSS
=================================================*/
.pricing-area {
  padding-bottom: 40px;
}

.single-pricing-box {
  background-color: #ffffff;
  margin-bottom: 30px;
  box-shadow: 0 11px 60px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
  transition: 0.5s;
  border-radius: 5px;
}
.single-pricing-box .pricing-header h3 {
  margin-bottom: 0;
  font-size: 22px;
  font-weight: 600;
}
.single-pricing-box .pricing-header p {
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 0;
}
.single-pricing-box .price {
  font-size: 50px;
  font-weight: 400;
  font-family: "Raleway", sans-serif;
  margin-bottom: 10px;
}
.single-pricing-box .price span {
  display: inline-block;
  margin-left: -10px;
  font-size: 18px;
  font-weight: 300;
}
.single-pricing-box .buy-btn {
  margin-bottom: 25px;
}
.single-pricing-box .buy-btn .btn {
  padding: 13px 30px;
  font-size: 13px;
}
.single-pricing-box .buy-btn .btn-primary {
  border-radius: 30px;
}
.single-pricing-box .buy-btn .btn-primary::after {
  border-radius: 30px;
}
.single-pricing-box .buy-btn .btn-primary::before {
  background: linear-gradient(to right top, #a3a3a3, #9a9a9a, #909090, #878787, #7e7e7e);
  border-radius: 30px;
}
.single-pricing-box .pricing-features {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.single-pricing-box .pricing-features li {
  margin-bottom: 12px;
  color: #6084a4;
  position: relative;
  padding-left: 19px;
  font-weight: 300;
}
.single-pricing-box .pricing-features li:last-child {
  margin-bottom: 0;
}
.single-pricing-box .pricing-features li i {
  color: #22418E;
  font-size: 12px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.single-pricing-box .pricing-features li i::before {
  font-size: 12px;
}
.single-pricing-box:hover .buy-btn .btn-primary::after {
  opacity: 1;
  visibility: visible;
}

/*================================================
FunFacts Area CSS
=================================================*/
.funfacts-area {
  position: relative;
  z-index: 1;
}
.funfacts-area .map-bg {
  position: absolute;
  z-index: -1;
  top: 120px;
  left: 0;
  right: 0;
  text-align: center;
  margin: 0 auto;
}
.funfacts-area .row {
  padding-left: 100px;
  padding-right: 100px;
}

.funfact {
  text-align: center;
}
.funfact h3 {
  color: #22418E;
  margin-bottom: 2px;
  font-size: 35px;
  font-weight: 600;
}
.funfact h3 .odometer {
  position: relative;
  top: -2px;
}
.funfact p {
  line-height: initial;
  margin-bottom: 0;
}

.contact-cta-box {
  margin: 70px auto 0;
  max-width: 600px;
  border: 1px dashed #ebebeb;
  padding: 30px 210px 30px 30px;
  border-radius: 5px;
  position: relative;
}
.contact-cta-box h3 {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 22px;
}
.contact-cta-box p {
  margin-bottom: 0;
  line-height: initial;
}
.contact-cta-box .btn {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
}

/*================================================
Feedback Area CSS
=================================================*/
.feedback-slides {
  position: relative;
  max-width: 750px;
  margin: 0 auto;
}
.feedback-slides .client-feedback {
  position: relative;
}
.feedback-slides .client-feedback .single-feedback {
  background: #ffffff;
  border-radius: 5px;
  margin-bottom: 60px;
  position: relative;
  padding-left: 170px;
  padding-right: 40px;
  padding-top: 40px;
  padding-bottom: 40px;
}
.feedback-slides .client-feedback .single-feedback .client-img {
  position: absolute;
  left: 40px;
  top: 40px;
}
.feedback-slides .client-feedback .single-feedback .client-img img {
  border-radius: 50%;
  border: 2px solid #44ce6f;
  padding: 4px;
}
.feedback-slides .client-feedback .single-feedback h3 {
  margin-bottom: 0;
  font-weight: 600;
  font-size: 18px;
}
.feedback-slides .client-feedback .single-feedback span {
  display: block;
  color: #22418E;
  margin-top: 5px;
  margin-bottom: 12px;
  font-weight: 300;
  font-family: "Raleway", sans-serif;
  font-size: 14px;
}
.feedback-slides .client-feedback .single-feedback::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -25px;
  width: 50px;
  height: 50px;
  background: #ffffff;
  right: 0;
  margin: 0 auto;
  transform: rotate(45deg);
}
.feedback-slides .client-thumbnails {
  position: relative;
  margin: 0 85px;
}
.feedback-slides .client-thumbnails .item .img-fill {
  cursor: pointer;
  position: relative;
  text-align: center;
}
.feedback-slides .client-thumbnails .item .img-fill img {
  opacity: 0.4;
  transition: 0.5s;
  display: inline-block;
  position: relative;
  border: 2px solid #44ce6f;
  border-radius: 50%;
  padding: 3px;
  width: 85px;
}
.feedback-slides .client-thumbnails .item:hover .img-fill img, .feedback-slides .client-thumbnails .item.slick-center .img-fill img {
  opacity: 1;
}
.feedback-slides .client-thumbnails .next-arrow, .feedback-slides .client-thumbnails .prev-arrow {
  position: absolute;
  width: 40px;
  height: 40px;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #5e5e5e;
  border: 1px solid #5e5e5e;
  z-index: 1;
  border-radius: 50%;
  line-height: 40px;
  outline: 0 !important;
  transition: 0.5s;
}
.feedback-slides .client-thumbnails .next-arrow::before, .feedback-slides .client-thumbnails .next-arrow::before, .feedback-slides .client-thumbnails .prev-arrow::before, .feedback-slides .client-thumbnails .prev-arrow::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
  border-radius: 50%;
  z-index: -1;
}
.feedback-slides .client-thumbnails .next-arrow:hover, .feedback-slides .client-thumbnails .prev-arrow:hover {
  color: #ffffff;
  border-color: #22418E;
}
.feedback-slides .client-thumbnails .next-arrow:hover::before, .feedback-slides .client-thumbnails .prev-arrow:hover::before {
  opacity: 1;
  visibility: visible;
}
.feedback-slides .client-thumbnails .next-arrow {
  right: -20px;
}
.feedback-slides .client-thumbnails .prev-arrow {
  left: -20px;
}

/*================================================
Ready To Talk Area CSS
=================================================*/
.ready-to-talk {
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  padding-top: 70px;
  padding-bottom: 145px;
}

.ready-to-talk-content h3 {
  color: #ffffff;
  text-transform: capitalize;
  margin-bottom: 10px;
  font-size: 40px;
  font-weight: 600;
}
.ready-to-talk-content p {
  color: #ffffff;
  margin-bottom: 20px;
}
.ready-to-talk-content .btn-primary::after {
  background: #ffffff;
}
.ready-to-talk-content .btn-primary:hover, .ready-to-talk-content .btn-primary:focus {
  color: #22418E !important;
}
.ready-to-talk-content span a {
  margin-left: 15px;
  color: #ffffff;
  text-decoration: underline;
}

/*================================================
Partner Area CSS
=================================================*/
.partner-area {
  text-align: center;
  position: relative;
  margin-top: -120px;
  background: transparent;
}
.partner-area h3 {
  color: #ffffff;
  margin-bottom: 0;
  font-size: 22px;
  font-weight: 600;
}

.partner-inner {
  background: #ffffff;
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  margin-top: 20px;
  text-align: center;
  padding-top: 5px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 30px;
}
.partner-inner a {
  display: block;
  margin-top: 25px;
  position: relative;
}
.partner-inner a img {
  transition: 0.5s;
}
.partner-inner a img:nth-child(2) {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 auto;
  opacity: 0;
  visibility: hidden;
}
.partner-inner a:hover img:nth-child(1) {
  opacity: 0;
  visibility: hidden;
}
.partner-inner a:hover img:nth-child(2) {
  opacity: 1;
  top: 0;
  visibility: visible;
}

/*================================================
App Download Area CSS
=================================================*/
.app-download-area {
  padding-top: 95px;
  padding-bottom: 50px;
}

.app-image {
  position: relative;
  z-index: 1;
  text-align: left;
}
.app-image .main-image img:nth-child(2) {
  position: absolute;
  right: 20px;
  top: 0;
}
.app-image .main-mobile-image {
  display: none;
}
.app-image .circle-img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-55%);
  z-index: -1;
  right: 0;
  margin: 0 auto;
}
.app-image .circle-img img {
  animation-name: rotateMe;
  animation-duration: 35s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.app-download-content {
  padding-left: 20px;
}
.app-download-content h2 {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.app-download-content .bar {
  height: 5px;
  width: 90px;
  background: #cdf1d8;
  margin: 20px 0 25px;
  position: relative;
  border-radius: 30px;
}
.app-download-content .bar::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2.7px;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #44ce6f;
  animation-duration: 3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: MOVE-BG;
}
.app-download-content p {
  margin-bottom: 0;
}
.app-download-content .btn-box {
  margin-top: 25px;
}
.app-download-content .btn-box .app-store-btn {
  border-radius: 3px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 12px 25px 12px 60px;
  font-size: 12px;
}
.app-download-content .btn-box .app-store-btn i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.app-download-content .btn-box .app-store-btn i::before {
  font-size: 35px;
}
.app-download-content .btn-box .app-store-btn span {
  display: block;
  font-size: 18px;
  font-weight: 500;
}
.app-download-content .btn-box .app-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.app-download-content .btn-box .app-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.app-download-content .btn-box .app-store-btn:hover {
  color: #ffffff;
}
.app-download-content .btn-box .app-store-btn:hover::after {
  opacity: 1;
  visibility: visible;
}
.app-download-content .btn-box .app-store-btn:hover::before {
  opacity: 0;
  visibility: hidden;
}
.app-download-content .btn-box .play-store-btn {
  margin-left: 12px;
  border-radius: 3px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 12px 25px 12px 60px;
  font-size: 12px;
}
.app-download-content .btn-box .play-store-btn i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.app-download-content .btn-box .play-store-btn i::before {
  font-size: 35px;
}
.app-download-content .btn-box .play-store-btn span {
  display: block;
  font-size: 18px;
  font-weight: 500;
}
.app-download-content .btn-box .play-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.app-download-content .btn-box .play-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
}
.app-download-content .btn-box .play-store-btn:hover {
  color: #ffffff;
}
.app-download-content .btn-box .play-store-btn:hover::after {
  opacity: 0;
  visibility: hidden;
}
.app-download-content .btn-box .play-store-btn:hover::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Account Create Area CSS
=================================================*/
.account-create-area {
  position: relative;
  z-index: 1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  padding-top: 100px;
  padding-bottom: 100px;
}
.account-create-area::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.2;
  height: 100%;
  z-index: -1;
  background-image: url(../img/bg_lines.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 200%;
}

.account-create-content {
  text-align: center;
}
.account-create-content h2 {
  color: #ffffff;
  max-width: 530px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.account-create-content p {
  color: #ffffff;
  line-height: initial;
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 0;
}
.account-create-content .btn-primary {
  margin-top: 30px;
  padding: 16px 30px;
}
.account-create-content .btn-primary::before {
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
}
.account-create-content .btn-primary::after {
  background: #ffffff;
}
.account-create-content .btn-primary:hover, .account-create-content .btn-primary:focus {
  color: #0e314c !important;
}

/*================================================
Blog Area CSS
=================================================*/
.blog-area {
  padding-bottom: 40px;
}
.blog-area .pagination-area {
  margin-bottom: 30px;
}

.single-blog-post {
  background: #f7fafd;
  position: relative;
  transition: 0.5s;
  margin-bottom: 30px;
}
.single-blog-post .blog-image {
  position: relative;
}
.single-blog-post .blog-image a {
  display: block;
}
.single-blog-post .blog-image .date {
  position: absolute;
  left: 20px;
  bottom: -20px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  color: #ffffff;
  padding: 10px 16px 8px;
  border-radius: 50px;
  font-family: "Raleway", sans-serif;
  font-size: 13px;
}
.single-blog-post .blog-image .date i {
  margin-right: 2px;
}
.single-blog-post .blog-post-content {
  transition: 0.5s;
  padding-top: 40px;
  padding-left: 20px;
  padding-bottom: 20px;
  padding-right: 20px;
}
.single-blog-post .blog-post-content h3 {
  line-height: 30px;
  margin-bottom: 0;
  font-size: 22px;
  font-weight: 600;
}
.single-blog-post .blog-post-content span {
  display: block;
  color: #0e314c;
  font-size: 14.5px;
  margin-top: 13px;
  margin-bottom: 12px;
}
.single-blog-post .blog-post-content span a {
  color: #6084a4;
}
.single-blog-post .blog-post-content span a:hover {
  color: #22418E;
}
.single-blog-post .blog-post-content .read-more-btn {
  font-size: 14.5px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.single-blog-post .blog-post-content .read-more-btn i {
  font-size: 12px;
  margin-left: 2px;
}
.single-blog-post:hover {
  transform: translateY(-7px);
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}

/*================================================
Blog Details Area CSS
=================================================*/
.blog-details .article-content {
  margin-top: 30px;
}
.blog-details .article-content .entry-meta {
  margin-bottom: 15px;
}
.blog-details .article-content .entry-meta ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.blog-details .article-content .entry-meta ul li {
  position: relative;
  display: inline-block;
  color: #0e314c;
  margin-right: 21px;
  font-weight: 300;
}
.blog-details .article-content .entry-meta ul li span {
  display: inline-block;
  color: #0e314c;
}
.blog-details .article-content .entry-meta ul li a {
  display: inline-block;
  color: #6084a4;
}
.blog-details .article-content .entry-meta ul li a:hover {
  color: #22418E;
}
.blog-details .article-content .entry-meta ul li i {
  color: #22418E;
  margin-right: 2px;
}
.blog-details .article-content .entry-meta ul li::before {
  content: "";
  position: absolute;
  top: 11px;
  right: -15px;
  width: 6px;
  height: 1px;
  background: #22418E;
}
.blog-details .article-content .entry-meta ul li:last-child {
  margin-right: 0;
}
.blog-details .article-content .entry-meta ul li:last-child::before {
  display: none;
}
.blog-details .article-content h3 {
  margin-bottom: 15px;
  font-size: 22px;
  font-weight: 600;
}
.blog-details .article-content p {
  line-height: 1.8;
}
.blog-details .article-content .wp-block-gallery.columns-3 {
  padding-left: 0;
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  margin-right: -10px;
  margin-left: -10px;
  margin-bottom: 30px;
  margin-top: 30px;
}
.blog-details .article-content .wp-block-gallery.columns-3 li {
  flex: 0 0 33.3333%;
  max-width: 33.3333%;
  padding-right: 10px;
  padding-left: 10px;
}
.blog-details .article-content .wp-block-gallery.columns-3 li figure {
  margin-bottom: 0;
}
.blog-details .article-content .article-features-list {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 15px;
  margin-left: 20px;
}
.blog-details .article-content .article-features-list li {
  margin-bottom: 12px;
  color: #6084a4;
  position: relative;
  padding-left: 15px;
  font-weight: 300;
}
.blog-details .article-content .article-features-list li::before {
  background: #22418E;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  position: absolute;
}
.blog-details .article-content .article-features-list li:last-child {
  margin-bottom: 0;
}
.blog-details .article-footer {
  display: flex;
  flex-wrap: wrap;
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
  margin-top: 30px;
}
.blog-details .article-footer .article-tags {
  flex: 0 0 50%;
  max-width: 50%;
}
.blog-details .article-footer .article-tags span {
  display: inline-block;
  color: #0e314c;
  font-size: 20px;
  margin-right: 5px;
  position: relative;
  top: 2px;
}
.blog-details .article-footer .article-tags a {
  display: inline-block;
  color: #6084a4;
  font-weight: 300;
}
.blog-details .article-footer .article-tags a:hover {
  color: #22418E;
}
.blog-details .article-footer .article-share {
  flex: 0 0 50%;
  max-width: 50%;
}
.blog-details .article-footer .article-share .social {
  padding-left: 0;
  list-style-type: none;
  text-align: right;
  margin-bottom: 0;
}
.blog-details .article-footer .article-share .social li {
  display: inline-block;
}
.blog-details .article-footer .article-share .social li a {
  color: #0e314c;
  background-color: #f7f7f7;
  width: 33px;
  height: 33px;
  line-height: 35px;
  text-align: center;
  border-radius: 50%;
  font-size: 13px;
  display: inline-block;
}
.blog-details .article-footer .article-share .social li a:hover {
  color: #ffffff;
  background-color: #22418E;
}

blockquote, .blockquote {
  overflow: hidden;
  background-color: #fafafa;
  padding: 50px !important;
  position: relative;
  text-align: center;
  z-index: 1;
  font-family: "Raleway", sans-serif;
  margin-bottom: 20px;
  margin-top: 20px;
}
blockquote p, .blockquote p {
  color: #0e314c;
  line-height: 1.6;
  margin-bottom: 0;
  font-style: italic;
  font-weight: 600;
  font-size: 20px !important;
}
blockquote cite, .blockquote cite {
  display: none;
}
blockquote::before, .blockquote::before {
  color: #efefef;
  content: "\f10d";
  position: absolute;
  left: 50px;
  top: -60px;
  z-index: -1;
  font-family: "Font Awesome 5 Free";
  font-size: 140px;
  font-weight: 900;
}

.comments-area {
  margin-top: 28px;
}
.comments-area .comments-title {
  line-height: initial;
  margin-bottom: 25px;
  font-size: 22px;
  font-weight: 600;
}
.comments-area ol, .comments-area ul {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.comments-area .comment-list {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.comments-area .children {
  margin-left: 20px;
}
.comments-area .comment-body {
  border-bottom: 1px solid #eeeeee;
  padding-left: 65px;
  color: #0e314c;
  margin-bottom: 20px;
  padding-bottom: 20px;
}
.comments-area .comment-body .reply {
  margin-top: 15px;
}
.comments-area .comment-body .reply a {
  border: 1px solid #eeeeee;
  color: #6084a4;
  display: inline-block;
  padding: 5px 20px;
  border-radius: 30px;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.comments-area .comment-body .reply a:hover {
  color: #ffffff;
  background-color: #22418E;
  border-color: #22418E;
}
.comments-area .comment-meta {
  margin-bottom: 0.8em;
}
.comments-area .comment-author {
  font-size: 16px;
  margin-bottom: 0.4em;
  position: relative;
  z-index: 2;
}
.comments-area .comment-author .avatar {
  height: 50px;
  left: -65px;
  position: absolute;
  width: 50px;
}
.comments-area .comment-author .fn {
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.comments-area .comment-author .says {
  display: none;
}
.comments-area .comment-metadata {
  color: #6084a4;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 400;
}
.comments-area .comment-metadata a {
  color: #6084a4;
}
.comments-area .comment-metadata a:hover {
  color: #22418E;
}
.comments-area .comment-content p {
  font-size: 14px;
}
.comments-area .comment-respond .comment-reply-title {
  margin-bottom: 0;
  font-size: 22px;
  font-weight: 600;
}
.comments-area .comment-respond .comment-reply-title #cancel-comment-reply-link {
  font-size: 15px;
  display: inline-block;
}
.comments-area .comment-respond .comment-form {
  overflow: hidden;
}
.comments-area .comment-respond .comment-notes {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 8px;
}
.comments-area .comment-respond .comment-notes .required {
  color: red;
}
.comments-area .comment-respond .comment-form-comment {
  margin-top: 15px;
  float: left;
  width: 100%;
}
.comments-area .comment-respond label {
  display: block;
  margin-bottom: 5px;
  color: #0e314c;
  font-weight: 400;
  font-family: "Raleway", sans-serif;
}
.comments-area .comment-respond input[type=date], .comments-area .comment-respond input[type=time], .comments-area .comment-respond input[type=datetime-local], .comments-area .comment-respond input[type=week], .comments-area .comment-respond input[type=month], .comments-area .comment-respond input[type=text], .comments-area .comment-respond input[type=email], .comments-area .comment-respond input[type=url], .comments-area .comment-respond input[type=password], .comments-area .comment-respond input[type=search], .comments-area .comment-respond input[type=tel], .comments-area .comment-respond input[type=number], .comments-area .comment-respond textarea {
  display: block;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #eeeeee;
  padding: 0.625em 0.7375em;
  outline: 0;
  transition: 0.5s;
}
.comments-area .comment-respond input[type=date]:focus, .comments-area .comment-respond input[type=time]:focus, .comments-area .comment-respond input[type=datetime-local]:focus, .comments-area .comment-respond input[type=week]:focus, .comments-area .comment-respond input[type=month]:focus, .comments-area .comment-respond input[type=text]:focus, .comments-area .comment-respond input[type=email]:focus, .comments-area .comment-respond input[type=url]:focus, .comments-area .comment-respond input[type=password]:focus, .comments-area .comment-respond input[type=search]:focus, .comments-area .comment-respond input[type=tel]:focus, .comments-area .comment-respond input[type=number]:focus, .comments-area .comment-respond textarea:focus {
  border-color: #22418E;
}
.comments-area .comment-respond .comment-form-author {
  float: left;
  width: 50%;
  padding-right: 10px;
  margin-bottom: 20px;
}
.comments-area .comment-respond .comment-form-email {
  float: left;
  width: 50%;
  padding-left: 12px;
  margin-bottom: 20px;
}
.comments-area .comment-respond .comment-form-url {
  float: left;
  width: 100%;
  margin-bottom: 20px;
}
.comments-area .comment-respond .comment-form-cookies-consent {
  width: 100%;
  float: left;
  position: relative;
  padding-left: 20px;
  margin-bottom: 20px;
}
.comments-area .comment-respond .comment-form-cookies-consent input {
  position: absolute;
  left: 0;
  top: 6px;
}
.comments-area .comment-respond .comment-form-cookies-consent label {
  display: inline-block;
  margin: 0;
  color: #6084a4;
  font-weight: normal;
}
.comments-area .comment-respond .form-submit {
  float: left;
  width: 100%;
}
.comments-area .comment-respond .form-submit input {
  background-color: #22418E;
  border: none;
  color: #ffffff;
  padding: 11px 25px 10px;
  display: inline-block;
  cursor: pointer;
  outline: 0;
  border-radius: 0;
  text-transform: uppercase;
  transition: 0.5s;
  font-family: "Raleway", sans-serif;
  font-weight: 700;
  font-size: 14px;
}
.comments-area .comment-respond .form-submit input:hover, .comments-area .comment-respond .form-submit input:focus {
  color: #ffffff;
  background-color: #44ce6f;
}

/*================================================
Page Title Area CSS
=================================================*/
.page-title-area {
  position: relative;
  z-index: 1;
  background-color: #0e314c;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 200px;
  padding-bottom: 120px;
}
.page-title-area::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000000;
  z-index: -1;
  opacity: 0.6;
}
.page-title-area::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.04;
  height: 100%;
  z-index: -1;
  background-image: url(../img/bg_lines.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 200%;
}
.page-title-area.item-bg1 {
  background-image: url(../img/page-title-bg1.jpg);
}
.page-title-area.item-bg2 {
  background-image: url(../img/page-title-bg2.jpg);
}

.page-title-content {
  text-align: center;
}
.page-title-content h2 {
  color: #ffffff;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 700;
}
.page-title-content p {
  color: #ffffff;
  max-width: 600px;
  font-family: "Raleway", sans-serif;
  margin-top: 10px;
  margin-bottom: 0;
  margin-left: auto;
  margin-right: auto;
}

.page-title-section {
  background: transparent url(../img/page-title-shape.jpg) right top no-repeat;
  padding-top: 200px;
  padding-bottom: 120px;
}

.page-title-text {
  position: relative;
}
.page-title-text h2 {
  max-width: 700px;
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 700;
}
.page-title-text p {
  max-width: 600px;
  font-family: "Raleway", sans-serif;
  margin-top: 8px;
  margin-bottom: 0;
}
.page-title-text ul {
  padding-left: 0;
  list-style-type: none;
  font-family: "Raleway", sans-serif;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin-bottom: 0;
}
.page-title-text ul li {
  display: inline-block;
  color: #22418E;
  margin-right: 10px;
  margin-left: 10px;
  position: relative;
  font-weight: 500;
  font-size: 15px;
}
.page-title-text ul li a {
  color: #0e314c;
}
.page-title-text ul li a:hover {
  color: #44ce6f;
}
.page-title-text ul li::before {
  content: "\f054";
  position: absolute;
  right: -15px;
  top: 5px;
  color: #0e314c;
  font-weight: 900;
  font-family: "Font Awesome 5 Free";
  font-size: 10px;
}
.page-title-text ul li:last-child::before {
  display: none;
}
.page-title-text ul li:first-child {
  margin-left: 0;
}

/*================================================
Pagination Area CSS
=================================================*/
.pagination-area {
  margin-top: 20px;
  text-align: center;
}
.pagination-area .page-numbers {
  width: 40px;
  height: 40px;
  margin: 0 3px;
  display: inline-block;
  background: #ffffff;
  line-height: 42px;
  color: #0e314c;
  box-shadow: 0 2px 10px 0 #d8dde6;
  position: relative;
  z-index: 1;
  font-size: 16px;
  font-weight: 400;
}
.pagination-area .page-numbers::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
.pagination-area .page-numbers.current, .pagination-area .page-numbers:hover, .pagination-area .page-numbers:focus {
  color: #ffffff;
}
.pagination-area .page-numbers.current::before, .pagination-area .page-numbers:hover::before, .pagination-area .page-numbers:focus::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Widget Sidebar Area CSS
=================================================*/
.widget-area .widget {
  margin-top: 30px;
}
.widget-area .widget:first-child {
  margin-top: 0;
}
.widget-area .widget .widget-title {
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
  margin-bottom: 20px;
  text-transform: capitalize;
  position: relative;
  font-weight: 600;
  font-size: 21px;
}
.widget-area .widget .widget-title::before {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  bottom: -1px;
  left: 0;
  width: 50px;
  height: 1px;
}
.widget-area .widget_search {
  box-shadow: 0px 0px 29px 0px rgba(102, 102, 102, 0.1);
  background-color: #ffffff;
  padding: 15px;
}
.widget-area .widget_search form {
  position: relative;
}
.widget-area .widget_search form label {
  display: block;
  margin-bottom: 0;
}
.widget-area .widget_search form .screen-reader-text {
  display: none;
}
.widget-area .widget_search form .search-field {
  background-color: transparent;
  height: 50px;
  padding: 6px 15px;
  border: 1px solid #eeeeee;
  width: 100%;
  display: block;
  outline: 0;
  transition: 0.5s;
}
.widget-area .widget_search form .search-field:focus {
  border-color: #22418E;
}
.widget-area .widget_search form button {
  position: absolute;
  right: 0;
  outline: 0;
  bottom: 0;
  height: 50px;
  width: 50px;
  z-index: 1;
  border: none;
  color: #ffffff;
  background-color: transparent;
}
.widget-area .widget_search form button::before {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  top: 0;
  transition: 0.5s;
}
.widget-area .widget_search form button::after {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  top: 0;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.widget-area .widget_search form button:hover::before {
  opacity: 0;
  visibility: hidden;
}
.widget-area .widget_search form button:hover::after {
  opacity: 1;
  visibility: visible;
}
.widget-area .widget_luvion_posts_thumb {
  position: relative;
  overflow: hidden;
}
.widget-area .widget_luvion_posts_thumb .item {
  overflow: hidden;
  margin-bottom: 15px;
}
.widget-area .widget_luvion_posts_thumb .item:last-child {
  margin-bottom: 0;
}
.widget-area .widget_luvion_posts_thumb .item .thumb {
  float: left;
  height: 80px;
  overflow: hidden;
  position: relative;
  width: 80px;
  margin-right: 15px;
}
.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage {
  width: 80px;
  height: 80px;
  display: inline-block;
  background-size: cover !important;
  background-repeat: no-repeat;
  background-position: center center !important;
}
.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg1 {
  background-image: url(../img/blog-image/1.jpg);
}
.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg2 {
  background-image: url(../img/blog-image/2.jpg);
}
.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg3 {
  background-image: url(../img/blog-image/3.jpg);
}
.widget-area .widget_luvion_posts_thumb .item .info {
  overflow: hidden;
}
.widget-area .widget_luvion_posts_thumb .item .info time {
  display: block;
  color: #6084a4;
  text-transform: uppercase;
  margin-top: 5px;
  margin-bottom: 3px;
  font-size: 11px;
}
.widget-area .widget_luvion_posts_thumb .item .info .title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 16px;
  font-weight: 600;
}
.widget-area .widget_recent_entries ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.widget-area .widget_recent_entries ul li {
  position: relative;
  margin-bottom: 12px;
  padding-left: 14px;
  line-height: 1.5;
  font-weight: 500;
  font-size: 14.5px;
}
.widget-area .widget_recent_entries ul li:last-child {
  margin-bottom: 0;
}
.widget-area .widget_recent_entries ul li::before {
  background: #22418E;
  position: absolute;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 7px;
}
.widget-area .widget_recent_entries ul li .post-date {
  display: block;
  font-size: 13px;
  color: #6084a4;
  margin-top: 4px;
}
.widget-area .widget_recent_comments ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.widget-area .widget_recent_comments ul li {
  position: relative;
  margin-bottom: 12px;
  color: #0e314c;
  padding-left: 14px;
  line-height: 1.5;
  font-weight: 500;
  font-size: 14.5px;
}
.widget-area .widget_recent_comments ul li:last-child {
  margin-bottom: 0;
}
.widget-area .widget_recent_comments ul li::before {
  background: #22418E;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 7px;
  position: absolute;
}
.widget-area .widget_recent_comments ul li span {
  display: inline-block;
}
.widget-area .widget_recent_comments ul li a {
  display: inline-block;
}
.widget-area .widget_archive ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.widget-area .widget_archive ul li {
  position: relative;
  margin-bottom: 12px;
  padding-left: 14px;
  font-weight: 500;
  font-size: 14.5px;
}
.widget-area .widget_archive ul li:last-child {
  margin-bottom: 0;
}
.widget-area .widget_archive ul li::before {
  background: #22418E;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 7px;
  position: absolute;
}
.widget-area .widget_categories ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.widget-area .widget_categories ul li {
  position: relative;
  margin-bottom: 12px;
  padding-left: 14px;
  font-weight: 500;
  font-size: 14.5px;
}
.widget-area .widget_categories ul li:last-child {
  margin-bottom: 0;
}
.widget-area .widget_categories ul li::before {
  background: #22418E;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 7px;
  position: absolute;
}
.widget-area .widget_categories ul li .post-count {
  float: right;
}
.widget-area .widget_meta ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.widget-area .widget_meta ul li {
  position: relative;
  margin-bottom: 12px;
  padding-left: 14px;
  font-weight: 500;
  font-size: 14.5px;
}
.widget-area .widget_meta ul li:last-child {
  margin-bottom: 0;
}
.widget-area .widget_meta ul li::before {
  background: #22418E;
  height: 7px;
  width: 7px;
  content: "";
  left: 0;
  top: 7px;
  position: absolute;
}
.widget-area .widget_tag_cloud .widget-title {
  margin-bottom: 12px;
}
.widget-area .tagcloud a {
  display: inline-block;
  padding: 6px 13px;
  border: 1px dashed #eeeeee;
  position: relative;
  font-weight: 600;
  font-size: 13.5px !important;
  margin-top: 8px;
  margin-right: 4px;
}
.widget-area .tagcloud a::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
  transform: scale(0.8);
}
.widget-area .tagcloud a:hover, .widget-area .tagcloud a:focus {
  color: #ffffff;
  border-color: #22418E;
}
.widget-area .tagcloud a:hover::before, .widget-area .tagcloud a:focus::before {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

/*================================================
404 Error Area CSS
=================================================*/
.error-area {
  height: 100vh;
}

.error-content {
  text-align: center;
  margin: 0 auto;
  max-width: 700px;
}
.error-content h3 {
  font-size: 40px;
  font-weight: 700;
  margin-top: 35px;
  margin-bottom: 15px;
}
.error-content p {
  max-width: 520px;
  margin: 0 auto 20px;
}

/*================================================
FAQ Area CSS
=================================================*/
.faq-content h2 {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.faq-content .bar {
  height: 5px;
  width: 90px;
  background: #cdf1d8;
  margin: 20px 0 25px;
  position: relative;
  border-radius: 30px;
}
.faq-content .bar::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2.7px;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #44ce6f;
  animation-duration: 3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: MOVE-BG;
}
.faq-content .faq-image {
  margin-top: 20px;
}

.faq-accordion .accordion {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}
.faq-accordion .accordion .accordion-item {
  display: block;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  background: #ffffff;
  margin-bottom: 10px;
}
.faq-accordion .accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.faq-accordion .accordion .accordion-title {
  padding: 12px 20px 12px 51px;
  color: #0e314c;
  position: relative;
  border-bottom: 1px solid transparent;
  margin-bottom: -1px;
  display: block;
  font-size: 15px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.faq-accordion .accordion .accordion-title i {
  position: absolute;
  left: 0;
  top: 0;
  width: 40px;
  text-align: center;
  height: 100%;
  background: #22418E;
  color: #ffffff;
}
.faq-accordion .accordion .accordion-title i::before {
  position: absolute;
  left: 0;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  font-size: 13px;
}
.faq-accordion .accordion .accordion-title.active {
  border-bottom-color: #eeeeee;
}
.faq-accordion .accordion .accordion-title.active i::before {
  content: "\f068";
}
.faq-accordion .accordion .accordion-content {
  display: none;
  position: relative;
  padding: 15px;
  font-size: 14.5px;
}
.faq-accordion .accordion .accordion-content.show {
  display: block;
}

.faq-contact {
  margin-top: 70px;
}

.faq-contact-form {
  max-width: 750px;
  margin: 0 auto;
  text-align: center;
}
.faq-contact-form form .form-group {
  margin-bottom: 15px;
}
.faq-contact-form form .form-control {
  background-color: #ffffff;
  border: none;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;
  height: 50px;
  font-family: "Raleway", sans-serif;
  font-size: 14.5px;
}
.faq-contact-form form .row {
  margin-left: -7px;
  margin-right: -7px;
}
.faq-contact-form form .row .col-lg-12, .faq-contact-form form .row .col-lg-6 {
  padding-left: 7px;
  padding-right: 7px;
}
.faq-contact-form form textarea.form-control {
  height: auto;
  padding-top: 15px;
  line-height: initial;
}
.faq-contact-form form .btn {
  margin-top: 8px;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);
}
.faq-contact-form form .help-block ul {
  padding-left: 0;
  list-style-type: none;
  text-align: left;
  margin-top: 5px;
  margin-bottom: 0;
}
.faq-contact-form form .help-block ul li {
  color: red;
  font-weight: 300;
}
.faq-contact-form form #msgSubmit {
  margin-bottom: 0;
  text-align: center !important;
}
.faq-contact-form form #msgSubmit.text-danger, .faq-contact-form form #msgSubmit.text-success {
  margin-top: 20px;
  font-size: 22px;
  font-weight: 300;
}

/*================================================
Login Area CSS
=================================================*/
.login-image {
  height: 100%;
  width: 100%;
  background-image: url(../img/login-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.login-image img {
  display: none;
}

.login-content {
  height: 100vh;
}
.login-content .login-form {
  text-align: center;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}
.login-content .login-form .logo {
  margin-bottom: 35px;
}
.login-content .login-form .logo a {
  display: inline-block;
}
.login-content .login-form h3 {
  margin-bottom: 0;
  font-size: 30px;
  font-weight: 700;
}
.login-content .login-form p {
  margin-top: 7px;
  margin-bottom: 0;
}
.login-content .login-form form {
  margin-top: 35px;
}
.login-content .login-form form .form-group {
  margin-bottom: 15px;
}
.login-content .login-form form .form-control {
  background-color: #ffffff;
  color: #0e314c;
  border: none;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;
  height: 50px;
  font-family: "Raleway", sans-serif;
  font-size: 14px;
}
.login-content .login-form form .btn {
  display: block;
  width: 100%;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);
}
.login-content .login-form form .forgot-password {
  text-align: right;
  margin-top: 15px;
}
.login-content .login-form form .forgot-password a {
  display: inline-block;
  color: #22418E;
  text-decoration: underline;
}
.login-content .login-form form .connect-with-social {
  margin-top: 15px;
}
.login-content .login-form form .connect-with-social button {
  display: block;
  width: 100%;
  position: relative;
  border: 1px solid #22418E;
  background-color: transparent;
  transition: 0.5s;
  padding: 11px 30px;
  border-radius: 2px;
  color: #22418E;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
}
.login-content .login-form form .connect-with-social button i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
  font-size: 20px;
}
.login-content .login-form form .connect-with-social button.facebook {
  border-color: #3b5998;
  color: #3b5998;
}
.login-content .login-form form .connect-with-social button.facebook:hover {
  background-color: #3b5998;
  color: #ffffff;
  border-color: #3b5998;
}
.login-content .login-form form .connect-with-social button.google {
  margin-top: 10px;
  border-color: #EA4335;
  color: #EA4335;
}
.login-content .login-form form .connect-with-social button.google:hover {
  background-color: #EA4335;
  color: #ffffff;
  border-color: #EA4335;
}
.login-content .login-form form .connect-with-social button:hover {
  background-color: #22418E;
  color: #ffffff;
  border-color: #22418E;
}

/*================================================
Signup Area CSS
=================================================*/
.signup-image {
  height: 100%;
  width: 100%;
  background-image: url(../img/signup-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.signup-image img {
  display: none;
}

.signup-content {
  height: 100vh;
}
.signup-content .signup-form {
  text-align: center;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}
.signup-content .signup-form .logo {
  margin-bottom: 35px;
}
.signup-content .signup-form .logo a {
  display: inline-block;
}
.signup-content .signup-form h3 {
  margin-bottom: 0;
  font-size: 30px;
  font-weight: 700;
}
.signup-content .signup-form p {
  margin-top: 7px;
  margin-bottom: 0;
}
.signup-content .signup-form form {
  margin-top: 35px;
}
.signup-content .signup-form form .form-group {
  margin-bottom: 15px;
}
.signup-content .signup-form form .form-control {
  background-color: #ffffff;
  color: #0e314c;
  border: none;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;
  height: 50px;
  font-family: "Raleway", sans-serif;
  font-size: 14px;
}
.signup-content .signup-form form .btn {
  display: block;
  width: 100%;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);
}
.signup-content .signup-form form .connect-with-social {
  margin-top: 20px;
}
.signup-content .signup-form form .connect-with-social span {
  display: block;
  text-transform: uppercase;
  color: #6084a4;
  margin-bottom: 20px;
}
.signup-content .signup-form form .connect-with-social button {
  display: block;
  width: 100%;
  position: relative;
  border: 1px solid #22418E;
  background-color: transparent;
  transition: 0.5s;
  padding: 11px 30px;
  border-radius: 2px;
  color: #22418E;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
}
.signup-content .signup-form form .connect-with-social button i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
  font-size: 20px;
}
.signup-content .signup-form form .connect-with-social button.facebook {
  border-color: #3b5998;
  color: #3b5998;
}
.signup-content .signup-form form .connect-with-social button.facebook:hover {
  background-color: #3b5998;
  color: #ffffff;
  border-color: #3b5998;
}
.signup-content .signup-form form .connect-with-social button.google {
  margin-top: 10px;
  border-color: #EA4335;
  color: #EA4335;
}
.signup-content .signup-form form .connect-with-social button.google:hover {
  background-color: #EA4335;
  color: #ffffff;
  border-color: #EA4335;
}
.signup-content .signup-form form .connect-with-social button:hover {
  background-color: #22418E;
  color: #ffffff;
  border-color: #22418E;
}

/*================================================
Contact Area CSS
=================================================*/
.contact-area {
  position: relative;
  z-index: 1;
}

.contact-form form .form-group {
  margin-bottom: 20px;
}
.contact-form form .form-control {
  background-color: #ffffff;
  border: none;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;
  height: 50px;
  font-family: "Raleway", sans-serif;
  font-size: 14.5px;
}
.contact-form form .row {
  margin-left: -7px;
  margin-right: -7px;
}
.contact-form form .row .col-lg-12, .contact-form form .row .col-lg-6 {
  padding-left: 7px;
  padding-right: 7px;
}
.contact-form form textarea.form-control {
  height: auto;
  padding-top: 15px;
  line-height: initial;
}
.contact-form form .btn {
  margin-top: 8px;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);
}
.contact-form form .help-block ul {
  padding-left: 0;
  list-style-type: none;
  margin-top: 5px;
  margin-bottom: 0;
}
.contact-form form .help-block ul li {
  color: red;
  font-weight: 300;
}
.contact-form form #msgSubmit {
  margin-bottom: 0;
  text-align: left !important;
}
.contact-form form #msgSubmit.text-danger, .contact-form form #msgSubmit.text-success {
  margin-top: 8px;
  font-size: 22px;
  font-weight: 300;
}

.contact-info {
  padding-right: 25px;
}
.contact-info ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.contact-info ul li {
  position: relative;
  padding-left: 95px;
  color: #6084a4;
  margin-bottom: 35px;
  font-weight: 300;
  line-height: 1.7;
}
.contact-info ul li .icon {
  border: 1px dashed #22418E;
  width: 75px;
  height: 75px;
  line-height: 75px;
  color: #ffffff;
  border-radius: 50%;
  font-size: 25px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  transition: 0.5s;
}
.contact-info ul li .icon::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 50%;
  margin: 5px;
  z-index: -1;
  transition: 0.5s;
}
.contact-info ul li .icon::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 50%;
  margin: 5px;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.contact-info ul li span {
  display: block;
  margin-bottom: 3px;
  color: #0e314c;
  font-size: 22px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.contact-info ul li a {
  display: block;
  color: #6084a4;
}
.contact-info ul li a:hover {
  color: #22418E;
}
.contact-info ul li:hover .icon {
  border-color: #44ce6f;
}
.contact-info ul li:hover .icon::before {
  opacity: 0;
  visibility: hidden;
}
.contact-info ul li:hover .icon::after {
  opacity: 1;
  visibility: visible;
}
.contact-info ul li:last-child {
  margin-bottom: 0;
}

.bg-map {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  text-align: center;
  margin: 0 auto;
  opacity: 0.5;
}

/*================================================
Footer Area CSS
=================================================*/
.footer-area {
  position: relative;
  z-index: 1;
  padding-top: 70px;
  background-color: #f7fafd;
}

.single-footer-widget {
  margin-bottom: 30px;
}
.single-footer-widget .logo a {
  display: block;
}
.single-footer-widget .logo p {
  font-size: 14.5px;
  margin-top: 20px;
  margin-bottom: 0;
}
.single-footer-widget h3 {
  margin-bottom: 24px;
  font-size: 22px;
  font-weight: 600;
}
.single-footer-widget .list {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.single-footer-widget .list li {
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 14.5px;
}
.single-footer-widget .list li a {
  color: #6084a4;
  display: inline-block;
}
.single-footer-widget .list li a:hover {
  padding-left: 5px;
  color: #22418E;
}
.single-footer-widget .list li:last-child {
  margin-bottom: 0;
}
.single-footer-widget .footer-contact-info {
  margin-bottom: 0;
  padding-left: 0;
  list-style-type: none;
}
.single-footer-widget .footer-contact-info li {
  position: relative;
  color: #6084a4;
  margin-bottom: 8px;
  line-height: 1.7;
  font-weight: 400;
  font-size: 14.5px;
}
.single-footer-widget .footer-contact-info li a {
  color: #6084a4;
  display: inline-block;
}
.single-footer-widget .footer-contact-info li a:hover {
  color: #22418E;
}
.single-footer-widget .footer-contact-info li:last-child {
  margin-bottom: 0;
}
.single-footer-widget .footer-contact-info li span {
  display: inline-block;
  font-weight: 500;
}
.single-footer-widget .social-links {
  padding-left: 0;
  list-style-type: none;
  margin-top: 13px;
  margin-bottom: 0;
}
.single-footer-widget .social-links li {
  margin-right: 2px;
  display: inline-block;
}
.single-footer-widget .social-links li a {
  width: 30px;
  text-align: center;
  line-height: 29px;
  height: 30px;
  border: 1px solid #ece9e9;
  border-radius: 50%;
  color: #22418E;
  display: inline-block;
  font-size: 14px;
}
.single-footer-widget .social-links li a:hover {
  color: #ffffff;
  border-color: #22418E;
  background-color: #22418E;
}

.copyright-area {
  border-top: 1px solid #d8ebfd;
  text-align: center;
  margin-top: 40px;
  padding-top: 25px;
  padding-bottom: 25px;
}
.copyright-area p {
  font-size: 14.5px;
}
.copyright-area p a {
  display: inline-block;
  color: #0e314c;
  font-weight: 400;
}
.copyright-area p a:hover {
  color: #22418E;
}

.map-image {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 auto;
  text-align: center;
  z-index: -1;
  opacity: 0.7;
}
.map-image img {
  max-width: 40%;
}

/*================================================
Go Top CSS
=================================================*/
.go-top {
  position: fixed;
  cursor: pointer;
  top: 50%;
  right: 15px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  z-index: 4;
  width: 40px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  opacity: 0;
  visibility: hidden;
  transition: 0.9s;
  color: #ffffff;
}
.go-top.active {
  top: 98%;
  transform: translateY(-98%);
  opacity: 1;
  visibility: visible;
}
.go-top::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
.go-top:hover, .go-top:focus {
  color: #ffffff;
}
.go-top:hover::before, .go-top:focus::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Main Banner Area CSS
=================================================*/
.banner-wrapper {
  position: relative;
  z-index: 1;
  padding-top: 90px;
}
.banner-wrapper::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  background: linear-gradient(151.59deg, #ff2f2f 10.43%, #000460 92.78%), radial-gradient(100% 246.94% at 100% 0, #fff 0, #020063 100%), linear-gradient(143.18deg, #1400ff 0.45%, #3a0000), linear-gradient(167.55deg, #ff002e, #ff003d 74.04%), linear-gradient(351.18deg, #b25bba 7.52%, #1700a7 77.98%), linear-gradient(202.44deg, #a90000 12.99%, #00ffe0 87.21%), linear-gradient(165.77deg, #b7d500 15.35%, #20a 89.57%);
  background-blend-mode: overlay, color-burn, screen, overlay, difference, difference, normal;
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 70%, 0% 100%);
  clip-path: polygon(0 0, 100% 0, 100% 70%, 0% 100%);
}
.banner-wrapper .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
.banner-wrapper .container-fluid .row {
  margin-left: 0;
  margin-right: 0;
}
.banner-wrapper .container-fluid .row .col-lg-6 {
  padding-left: 0;
  padding-right: 0;
}

.banner-wrapper-content {
  max-width: 640px;
  padding-right: 20px;
  margin-left: auto;
  margin-top: -50px;
}
.banner-wrapper-content h1 {
  margin-bottom: 0;
  color: #ffffff;
  font-size: 50px;
  font-weight: 700;
}
.banner-wrapper-content p {
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-size: 18px;
  max-width: 400px;
  margin-top: 20px;
  margin-bottom: 0;
}
.banner-wrapper-content .btn {
  margin-top: 30px;
}

.banner-wrapper-image {
  text-align: right;
}

/*================================================
Partner Area CSS
=================================================*/
.single-partner-item {
  text-align: center;
}
.single-partner-item img {
  display: inline-block !important;
  width: auto !important;
}

/*================================================
Payment Experience Area CSS
=================================================*/
.payment-experience-content h2 {
  margin-bottom: 18px;
  font-size: 40px;
  font-weight: 600;
}
.payment-experience-content .link-btn {
  display: inline-block;
  font-weight: 600;
  color: #22418E;
  position: relative;
  margin-top: 5px;
  line-height: 1;
}
.payment-experience-content .link-btn::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.payment-experience-content .link-btn:hover::before {
  width: 0;
}

.single-payment-experience-box {
  margin-top: 20px;
}
.single-payment-experience-box .icon {
  margin-bottom: 22px;
  width: 90px;
  height: 90px;
  text-align: center;
  line-height: 97px;
  background-color: #f3c7db;
  border-radius: 50%;
  position: relative;
  color: #ffffff;
  transition: 0.5s;
  font-size: 40px;
  padding-right: 5px;
}
.single-payment-experience-box .icon i {
  position: relative;
  z-index: 1;
}
.single-payment-experience-box .icon::before {
  content: "";
  transition: 0.5s;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #f281ac;
  border-radius: 50%;
  margin-top: 5px;
  margin-right: 5px;
}
.single-payment-experience-box h3 {
  margin-bottom: 10px;
  font-size: 22px;
  font-weight: 600;
}
.single-payment-experience-box:hover .icon {
  background-color: #0e314c;
}
.single-payment-experience-box:hover .icon::before {
  background-color: #22418E;
}

.col-lg-3:nth-child(2) .single-payment-experience-box .icon {
  background-color: #c1e9c5;
}
.col-lg-3:nth-child(2) .single-payment-experience-box .icon::before {
  background-color: #7dd179;
}
.col-lg-3:nth-child(2) .single-payment-experience-box:hover .icon {
  background-color: #0e314c;
}
.col-lg-3:nth-child(2) .single-payment-experience-box:hover .icon::before {
  background-color: #22418E;
}
.col-lg-3:nth-child(3) .single-payment-experience-box .icon {
  background-color: #bdc7d4;
}
.col-lg-3:nth-child(3) .single-payment-experience-box .icon::before {
  background-color: #73819c;
}
.col-lg-3:nth-child(3) .single-payment-experience-box:hover .icon {
  background-color: #0e314c;
}
.col-lg-3:nth-child(3) .single-payment-experience-box:hover .icon::before {
  background-color: #22418E;
}
.col-lg-3:nth-child(4) .single-payment-experience-box .icon {
  background-color: #bce5e5;
}
.col-lg-3:nth-child(4) .single-payment-experience-box .icon::before {
  background-color: #72c7c2;
}
.col-lg-3:nth-child(4) .single-payment-experience-box:hover .icon {
  background-color: #0e314c;
}
.col-lg-3:nth-child(4) .single-payment-experience-box:hover .icon::before {
  background-color: #22418E;
}

/*================================================
Support Area CSS
=================================================*/
.support-image {
  position: relative;
  padding-right: 130px;
  text-align: left;
}
.support-image img:nth-child(2) {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
}

.support-content h2 {
  margin-bottom: 20px;
  font-size: 40px;
  font-weight: 600;
}
.support-content .btn {
  margin-top: 10px;
}

/*================================================
Business Area CSS
=================================================*/
.business-area {
  background-color: #051947;
}

.business-content h2 {
  color: #ffffff;
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}
.business-content .single-business-box {
  margin-top: 35px;
}
.business-content .single-business-box h3 {
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 22px;
  font-weight: 600;
}
.business-content .single-business-box p {
  color: #ffffff;
  opacity: 0.9;
}

.business-image {
  margin-left: 50px;
  text-align: center;
  background: linear-gradient(125deg, #FDFF9C 0%, #0500FF 100%), linear-gradient(180deg, #D3D3D3 0%, #161616 100%), linear-gradient(310deg, #00F0FF 0%, #00F0FF 20%, #0017E3 calc(20% + 1px), #0017E3 40%, #000F8F calc(40% + 1px), #000F8F 70%, #00073F calc(70% + 1px), #00073F 100%), linear-gradient(285deg, #FFB6B9 0%, #FFB6B9 35%, #FAE3D9 calc(35% + 1px), #FAE3D9 45%, #BBDED6 calc(45% + 1px), #BBDED6 65%, #61C0BF calc(65% + 1px), #61C0BF 100%);
  background-blend-mode: overlay, overlay, exclusion, normal;
  padding: 50px;
}

/*================================================
Testimonials Area CSS
=================================================*/
.testimonials-area {
  padding-bottom: 40px;
}

.single-testimonials-box {
  box-shadow: 5px 5px #8b98b5;
  margin-bottom: 30px;
  padding: 30px;
  transition: 0.5s;
  border: 1px solid #8b98b5;
}
.single-testimonials-box .rating {
  margin-bottom: 15px;
}
.single-testimonials-box .rating i {
  font-size: 15px;
  color: #ffc107;
}
.single-testimonials-box p {
  margin: 0;
  color: #374d7f;
  font-size: 15px;
  font-weight: 500;
}
.single-testimonials-box h3 {
  color: #0e314c;
  margin-top: 20px;
  margin-bottom: 0;
  font-size: 15px;
  font-weight: 700;
}
.single-testimonials-box h3 span {
  font-weight: 400;
  color: #6084a4;
  display: inline-block;
  margin-left: 5px;
}
.single-testimonials-box:hover {
  box-shadow: 5px 5px #0e314c;
}

/*================================================
Global Area CSS
=================================================*/
.global-area {
  background-color: #051947;
}
.global-area .section-title h2 {
  color: #ffffff;
}
.global-area .section-title p {
  color: #ffffff;
}

.global-content {
  padding-right: 30px;
}
.global-content ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.global-content ul li {
  margin-bottom: 12px;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.12);
  padding: 30px 40px 28px;
  position: relative;
  border-radius: 2px;
  z-index: 1;
  font-size: 20px;
  font-weight: 600;
}
.global-content ul li:last-child {
  margin-bottom: 0;
}
.global-content ul li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 0%;
  border-radius: 2px;
  height: 100%;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.global-content ul li:hover::before {
  width: 100%;
}

/*================================================
Success Story Area CSS
=================================================*/
.success-story-inner {
  background-color: #051947;
  position: relative;
  z-index: 1;
  padding-top: 50px;
  padding-bottom: 50px;
  padding-left: 90px;
  padding-right: 135px;
}
.success-story-inner::before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 30%;
  z-index: -1;
  background: linear-gradient(125deg, #00FF57 0%, #010033 40%, #460043 70%, #F0FFC5 100%), linear-gradient(55deg, #0014C9 0%, #410060 100%), linear-gradient(300deg, #FFC700 0%, #001AFF 100%), radial-gradient(135% 215% at 115% 40%, #393939 0%, #393939 40%, #849561 calc(40% + 1px), #849561 60%, #EED690 calc(60% + 1px), #EED690 80%, #ECEFD8 calc(80% + 1px), #ECEFD8 100%), linear-gradient(125deg, #282D4F 0%, #282D4F 40%, #23103A calc(40% + 1px), #23103A 70%, #A0204C calc(70% + 1px), #A0204C 88%, #FF6C00 calc(88% + 1px), #FF6C00 100%);
  background-blend-mode: overlay, screen, overlay, overlay, normal;
}
.success-story-inner .owl-theme .owl-nav {
  margin-top: 0;
  position: absolute;
  right: -90px;
  top: 50%;
  transform: translateY(-50%);
}
.success-story-inner .owl-theme .owl-nav [className*=owl-] {
  display: block;
  width: 45px;
  height: 45px;
  line-height: 48px;
  background-color: #ffffff;
  border-radius: 50%;
  margin: 10px 0;
  font-size: 18px;
  transition: 0.5s;
}
.success-story-inner .owl-theme .owl-nav [className*=owl-]:hover {
  background-color: #22418E;
  color: #ffffff;
}

.single-success-story-box .content {
  padding-right: 40px;
}
.single-success-story-box .content p {
  margin: 0;
  color: #ffffff;
  font-size: 22px;
  font-weight: 600;
}
.single-success-story-box .content h3 {
  color: #e8e8e8;
  margin-top: 25px;
  margin-bottom: 0;
  font-size: 15px;
  font-weight: 700;
}
.single-success-story-box .content h3 span {
  font-weight: 300;
  color: #e7e7e7;
  display: inline-block;
  margin-left: 5px;
}
.single-success-story-box .content .btn {
  margin-top: 30px;
}

/*================================================
Payment Features Area CSS
=================================================*/
.payment-features-area {
  position: relative;
  z-index: 1;
}
.payment-features-area::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  background-color: #f4fcff;
  -webkit-clip-path: polygon(0 63%, 100% 30%, 100% 100%, 0% 100%);
  clip-path: polygon(0 63%, 100% 30%, 100% 100%, 0% 100%);
}

.payment-features-overview {
  display: flex;
  align-items: center !important;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  margin-top: 70px;
}
.payment-features-overview .payment-features-content {
  flex: 0 0 50%;
  max-width: 50%;
  padding-left: 15px;
  padding-right: 15px;
}
.payment-features-overview .payment-features-content .content {
  padding-left: 15px;
}
.payment-features-overview .payment-features-content .content h2 {
  margin-bottom: 18px;
  font-size: 40px;
  font-weight: 600;
}
.payment-features-overview .payment-features-content .content .link-btn {
  display: inline-block;
  font-weight: 600;
  color: #22418E;
  position: relative;
  margin-top: 5px;
  line-height: 1;
}
.payment-features-overview .payment-features-content .content .link-btn::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.payment-features-overview .payment-features-content .content .link-btn:hover::before {
  width: 0;
}
.payment-features-overview .payment-features-image {
  flex: 0 0 50%;
  max-width: 50%;
  padding-left: 15px;
  padding-right: 15px;
}
.payment-features-overview .payment-features-image .image {
  margin-right: 15px;
  text-align: center;
  background: linear-gradient(45deg, #000850 0%, #000320 100%), radial-gradient(100% 225% at 100% 0%, #FF6928 0%, #000000 100%), linear-gradient(225deg, #FF7A00 0%, #000000 100%), linear-gradient(135deg, #CDFFEB 10%, #CDFFEB 35%, #009F9D 35%, #009F9D 60%, #07456F 60%, #07456F 67%, #0F0A3C 67%, #0F0A3C 100%);
  background-blend-mode: screen, overlay, hard-light, normal;
  padding: 50px;
}
.payment-features-overview:first-child {
  margin-top: 0;
}
.payment-features-overview:nth-child(2) .payment-features-content .content, .payment-features-overview:nth-child(4) .payment-features-content .content, .payment-features-overview:nth-child(6) .payment-features-content .content {
  padding-left: 0;
  padding-right: 15px;
}
.payment-features-overview:nth-child(2) .payment-features-image .image, .payment-features-overview:nth-child(4) .payment-features-image .image, .payment-features-overview:nth-child(6) .payment-features-image .image {
  background: linear-gradient(123deg, #FFFFFF 0%, #00B2FF 100%), linear-gradient(236deg, #BAFF99 0%, #005E64 100%), linear-gradient(180deg, #FFFFFF 0%, #002A5A 100%), linear-gradient(225deg, #0094FF 20%, #BFF4ED 45%, #280F34 45%, #280F34 70%, #FF004E 70%, #E41655 85%, #B30753 85%, #B30753 100%), linear-gradient(135deg, #0E0220 15%, #0E0220 35%, #E40475 35%, #E40475 60%, #48E0E4 60%, #48E0E4 68%, #D7FBF6 68%, #D7FBF6 100%);
  background-blend-mode: overlay, overlay, overlay, darken, normal;
  margin-right: 0;
  margin-left: 15px;
}

/*================================================
Get Started Area CSS
=================================================*/
.get-started-area {
  background: linear-gradient(129.96deg, #FF2F2F 10.43%, #000460 92.78%), radial-gradient(100% 246.94% at 100% 0%, #FFFFFF 0%, #020063 100%), linear-gradient(58.72deg, #2200F2 0%, #530000 100%), linear-gradient(154.03deg, #B70000 0%, #FF003D 74.04%), linear-gradient(341.1deg, #FF0000 7.52%, #0038FF 77.98%), linear-gradient(136.23deg, #00C2FF 11.12%, #FF0000 86.47%), radial-gradient(57.37% 100% at 50% 0%, #B50000 0%, #0034BB 100%);
  background-blend-mode: overlay, color-burn, screen, overlay, difference, difference, normal;
}

.get-started-title h2 {
  color: #ffffff;
  margin-bottom: 30px;
  font-size: 40px;
  font-weight: 600;
}

.get-started-content p {
  color: #e9fffe;
  font-size: 20px;
  font-weight: 500;
}

.dark-version {
  top: 20%;
  left: 20px;
  z-index: 99991;
  position: fixed;
  transform: translateY(-20%);
}
.dark-version .switch {
  width: 60px;
  height: 34px;
  position: relative;
  display: inline-block;
}
.dark-version .switch input {
  width: 0;
  height: 0;
  opacity: 0;
}
.dark-version .slider {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
  position: absolute;
  transition: 0.5s;
  background: #22418E;
}
.dark-version .slider:before {
  top: 0;
  bottom: 0;
  width: 30px;
  left: 2.5px;
  content: "";
  height: 30px;
  margin: auto 0;
  position: absolute;
  transition: 0.5s;
  box-shadow: 0 0px 15px rgba(32, 32, 32, 0.2392156863);
  background: white url("../img/sun.png");
  background-position: center;
  background-repeat: no-repeat;
}
.dark-version input:checked + .slider {
  background-color: #44ce6f;
}
.dark-version input:checked + .slider:before {
  transform: translateX(24px);
  background: white url("../img/night-mode.png");
  background-repeat: no-repeat;
  background-position: center;
}
.dark-version .slider.round {
  border-radius: 50px;
}
.dark-version .slider.round:before {
  border-radius: 50%;
}

.buy-now-btn {
  z-index: 9;
  right: 35px;
  width: 65px;
  height: 65px;
  bottom: 100px;
  position: fixed;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  background-color: #81b441;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  animation-name: tada;
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
}
.buy-now-btn img {
  left: 0;
  right: 0;
  top: 50%;
  position: absolute;
  transform: translateY(-50%);
  margin-left: auto;
  margin-right: auto;
}
.buy-now-btn::before {
  top: 0;
  left: 0;
  z-index: -1;
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  animation-delay: 0.2s;
  background-color: #81b441;
  animation: ripple 1.5s ease-out infinite;
}

@keyframes ripple {
  from {
    opacity: 1;
    transform: scale(0);
  }
  to {
    opacity: 0;
    transform: scale(2);
  }
}
/*new-css "Home Demo - 6" */
.ptb-100 {
  padding-top: 100px;
  padding-bottom: 100px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-75 {
  padding-bottom: 75px;
}

.wrap-section-title {
  max-width: 535px;
  margin: 0 0 50px 0;
}
.wrap-section-title span {
  font-size: 16.5px;
  color: #22418E;
  display: inline-block;
  margin-bottom: 18px;
  font-weight: 400;
  letter-spacing: 1px;
}
.wrap-section-title h2 {
  font-size: 40px;
  margin-bottom: 0;
  line-height: 1.4;
}

/*================================================
Navbar Area CSS
=================================================*/
.navbar-area.navbar-with-position-relative {
  background-color: #ffffff;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a {
  color: #6084a4;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #0e314c;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn {
  color: #6084a4;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn i {
  margin-right: 2px;
  color: #eae563;
}
.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn:hover {
  color: #22418E;
}
.navbar-area.navbar-with-position-relative.is-sticky .navbar-brand img:nth-child(1) {
  display: block;
}
.navbar-area.navbar-with-position-relative.is-sticky .navbar-brand img:nth-child(2) {
  display: none;
}

/*================================================
Main Banner Woman Area CSS
=================================================*/
.main-banner-woman-area {
  background-color: #FFFDEF;
  padding-top: 150px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.main-banner-woman-area .container {
  position: relative;
}
.main-banner-woman-area .banner-woman-list {
  left: 15px;
  bottom: 70px;
  padding-left: 0;
  margin-bottom: 0;
  position: absolute;
  list-style-type: none;
}
.main-banner-woman-area .banner-woman-list li {
  font-size: 16px;
  color: #5C5C5C;
  font-weight: 400;
  display: inline-block;
  margin-right: 20px;
  position: relative;
  padding-left: 45px;
}
.main-banner-woman-area .banner-woman-list li i {
  display: inline-block;
  height: 35px;
  width: 35px;
  line-height: 35px;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);
  background-color: #ffffff;
  color: #DD2476;
  font-size: 16px;
  text-align: center;
  border-radius: 30px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.5s;
}
.main-banner-woman-area .banner-woman-list li:last-child {
  margin-right: 0;
}
.main-banner-woman-area .banner-woman-list li:hover i {
  background-color: #22418E;
  color: #ffffff;
}

.main-banner-woman-content {
  padding-right: 30px;
  position: relative;
  top: -85px;
}
.main-banner-woman-content h1 {
  font-size: 60px;
  font-weight: 600;
  margin-bottom: 0;
}
.main-banner-woman-content p {
  font-size: 20px;
  font-weight: 400;
  color: #0e314c;
  max-width: 390px;
  margin-top: 18px;
  margin-bottom: 0;
}
.main-banner-woman-content .btn-list {
  padding: 0;
  margin-top: 30px;
  margin-bottom: 0;
}
.main-banner-woman-content .btn-list li {
  list-style-type: none;
  display: inline-block;
  margin-right: 20px;
}
.main-banner-woman-content .btn-list li:last-child {
  margin-right: 0;
}
.main-banner-woman-content .btn-list li .discover-more-btn {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  color: #0e314c;
  border-bottom: 1px solid #0e314c;
  transition: 0.5s;
}
.main-banner-woman-content .btn-list li .discover-more-btn:hover {
  color: #22418E;
  border-bottom: 1px solid #22418E;
}

.main-banner-woman-image {
  text-align: center;
  position: relative;
}
.main-banner-woman-image img {
  z-index: 9;
  position: relative;
}
.main-banner-woman-image .woman-shape {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  bottom: 0;
}
.main-banner-woman-image .woman-shape img {
  z-index: -1;
}

.banner-woman-shape {
  position: absolute;
  bottom: -20px;
  right: 1.2%;
  transform: translateX(-1.2%);
}

/*================================================
Partner Area CSS
=================================================*/
.single-partner-card {
  text-align: center;
  margin-bottom: 25px;
}
.single-partner-card a:nth-child(1) {
  display: block;
}
.single-partner-card a:nth-child(2) {
  display: none;
}

/*================================================
About Us Area CSS
=================================================*/
.about-us-area {
  background-color: #222222;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.about-us-content span {
  font-size: 16.5px;
  color: #22418E;
  display: inline-block;
  margin-bottom: 18px;
  font-weight: 400;
  letter-spacing: 1px;
}
.about-us-content h3 {
  font-size: 40px;
  margin-bottom: 20px;
  color: #ffffff;
  line-height: 1.4;
}
.about-us-content p {
  color: #ffffff;
  margin-bottom: 0;
}
.about-us-content .list {
  padding: 0;
  margin-top: 35px;
  margin-bottom: 0;
}
.about-us-content .list li {
  font-size: 16px;
  color: #ffffff;
  font-weight: 400;
  display: inline-block;
  margin-bottom: 35px;
  position: relative;
  padding: 3.8px 0 2px 42px;
}
.about-us-content .list li i {
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 31.8px;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);
  background-color: #ffffff;
  color: #DD2476;
  font-size: 10px;
  text-align: center;
  border-radius: 30px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.5s;
}
.about-us-content .list li:last-child {
  margin-bottom: 0;
}
.about-us-content .list li:hover i {
  background-color: #22418E;
  color: #ffffff;
}
.about-us-content .about-btn {
  margin-top: 30px;
}
.about-us-content .about-btn .sign-up-btn {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  color: #22418E;
  border-bottom: 1px solid #22418E;
  transition: 0.5s;
}
.about-us-content .about-btn .sign-up-btn:hover {
  color: #ffffff;
  border-bottom: 1px solid #ffffff;
}

.about-us-right-content {
  padding-left: 35px;
}
.about-us-right-content p {
  font-size: 20px;
  color: #ffffff;
}
.about-us-right-content .information {
  margin-top: 30px;
}
.about-us-right-content .information .title {
  margin-left: 15px;
}
.about-us-right-content .information .title h3 {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 0;
}
.about-us-right-content .information .title span {
  font-size: 12.8px;
  display: inline-block;
  margin-top: 12px;
  color: #ffffff;
}

.about-us-shape {
  position: absolute;
  right: -100px;
  bottom: -100px;
  z-index: -1;
}

/*================================================
Flexibility Area CSS
=================================================*/
.flexibility-content {
  padding-left: 30px;
  position: relative;
  top: -30px;
}
.flexibility-content span {
  font-size: 16.5px;
  color: #22418E;
  display: inline-block;
  margin-bottom: 18px;
  font-weight: 400;
  letter-spacing: 1px;
}
.flexibility-content h3 {
  font-size: 40px;
  margin-bottom: 0;
  line-height: 1.4;
}
.flexibility-content .list {
  padding: 0;
  margin-top: 30px;
  margin-bottom: 0;
}
.flexibility-content .list li {
  font-size: 16px;
  font-weight: 400;
  display: inline-block;
  margin-bottom: 20px;
  position: relative;
  background-color: #FFFDEF;
  color: #5C5C5C;
  padding: 20.5px 20px 20px 62px;
  width: 100%;
  border-radius: 5px;
}
.flexibility-content .list li i {
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 31.8px;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);
  background-color: #ffffff;
  color: #DD2476;
  font-size: 10px;
  text-align: center;
  border-radius: 30px;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.5s;
}
.flexibility-content .list li:last-child {
  margin-bottom: 0;
}
.flexibility-content .list li:hover i {
  background-color: #22418E;
  color: #ffffff;
}
.flexibility-content p {
  margin-top: 25px;
}

/*================================================
Fun Facts Area CSS
=================================================*/
.funfacts-style-area {
  position: relative;
  z-index: 1;
}
.funfacts-style-area::before {
  position: absolute;
  content: "";
  height: 50%;
  width: 100%;
  background: #ffffff;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
}
.funfacts-style-area::after {
  position: absolute;
  content: "";
  height: 50%;
  width: 100%;
  background: #F5F5F5;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.funfacts-style-inner-box {
  background-image: url(../img/home-six/funfacts-style-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 20px;
  padding-left: 50px;
  padding-right: 50px;
}
.funfacts-style-inner-box .funfact h3 {
  color: #ffffff;
}
.funfacts-style-inner-box .funfact p {
  color: #ffffff;
}
.funfacts-style-inner-box .contact-cta-box {
  max-width: 800px;
  border: 1px solid rgba(255, 255, 255, 0.29);
}
.funfacts-style-inner-box .contact-cta-box h3 {
  color: #ffffff;
}
.funfacts-style-inner-box .contact-cta-box p {
  color: #ffffff;
}
.funfacts-style-inner-box .contact-cta-box .btn-primary::before {
  background: #0e314c;
}

/*================================================
Security Services Area CSS
=================================================*/
.security-services-area {
  background-color: #F5F5F5;
}

.security-services-card {
  padding: 30px;
  border-left: 1px solid #22418E;
  margin-bottom: 25px;
  transition: 0.5s;
}
.security-services-card .icon {
  margin-bottom: 30px;
  line-height: 1;
}
.security-services-card .icon i {
  color: #22418E;
}
.security-services-card .icon i::before {
  font-size: 50px;
}
.security-services-card h3 {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 20px;
}
.security-services-card p {
  margin-bottom: 18px;
}
.security-services-card .read-more-btn {
  position: relative;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  color: #22418E;
  border-bottom: 1px solid #22418E;
  transition: 0.5s;
}
.security-services-card .read-more-btn:hover {
  color: #0e314c;
  border-bottom: 1px solid #0e314c;
}
.security-services-card:hover {
  background-color: #ffffff;
}

/*================================================
Awesome Features Area CSS
=================================================*/
.awesome-features-area {
  overflow: hidden;
}

.awesome-features-content {
  padding-right: 30px;
  position: relative;
  top: -30px;
}
.awesome-features-content span {
  font-size: 16.5px;
  color: #22418E;
  display: inline-block;
  margin-bottom: 18px;
  font-weight: 400;
  letter-spacing: 1px;
}
.awesome-features-content h3 {
  font-size: 40px;
  margin-bottom: 0;
  line-height: 1.4;
}
.awesome-features-content .list {
  padding: 0;
  margin-top: 30px;
  margin-bottom: 0;
}
.awesome-features-content .list li {
  font-size: 16px;
  font-weight: 400;
  display: inline-block;
  margin-bottom: 20px;
  position: relative;
  background-color: #FFFDEF;
  color: #5C5C5C;
  padding: 20.5px 20px 20px 62px;
  width: 100%;
  border-radius: 5px;
}
.awesome-features-content .list li i {
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 31.8px;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);
  background-color: #ffffff;
  color: #DD2476;
  font-size: 10px;
  text-align: center;
  border-radius: 30px;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.5s;
}
.awesome-features-content .list li:last-child {
  margin-bottom: 0;
}
.awesome-features-content .list li:hover i {
  background-color: #22418E;
  color: #ffffff;
}
.awesome-features-content p {
  margin-top: 25px;
}

.awesome-features-image {
  position: relative;
  top: 1px;
}

/*================================================
Testimonials Wrap Area CSS
=================================================*/
.testimonials-wrap-area {
  background-color: #222222;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.testimonials-card {
  max-width: 750px;
  margin: auto;
  text-align: center;
  position: relative;
}
.testimonials-card p {
  font-size: 20px;
  color: #ffffff;
}
.testimonials-card .information {
  margin-top: 30px;
}
.testimonials-card .information img {
  width: auto;
  display: inline-block;
}
.testimonials-card .information .title {
  margin-left: 15px;
  text-align: left;
}
.testimonials-card .information .title h3 {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 0;
}
.testimonials-card .information .title span {
  font-size: 12.8px;
  display: inline-block;
  margin-top: 12px;
  color: #ffffff;
}
.testimonials-card .vector-icon-image {
  position: absolute;
  left: -162px;
  top: 0;
}

.testimonials-wrap-slides.owl-theme .owl-nav {
  margin-top: 0;
}
.testimonials-wrap-slides.owl-theme .owl-nav [className*=owl-] {
  padding: 0;
  width: 50px;
  height: 50px;
  margin: 0;
  font-size: 30px;
  border-radius: 0;
  background-color: transparent;
  color: #ffffff;
  top: 45%;
  transform: translateY(-45%);
  left: 0;
  position: absolute;
  transition: 0.5s;
}
.testimonials-wrap-slides.owl-theme .owl-nav [className*=owl-].owl-next {
  right: 0;
  left: auto;
}
.testimonials-wrap-slides.owl-theme .owl-nav [className*=owl-]:hover {
  color: #22418E;
}

.testimonials-wrap-shape {
  position: absolute;
  left: -100px;
  bottom: -100px;
  z-index: -1;
}

/*================================================
Blog Area CSS
=================================================*/
.single-blog-card {
  margin-bottom: 25px;
}
.single-blog-card .image {
  position: relative;
  padding-right: 50px;
  border-radius: 15px;
}
.single-blog-card .image img {
  border-radius: 5px;
}
.single-blog-card .image .date {
  color: #22418E;
  transform: rotate(90deg);
  transform-origin: 0;
  position: absolute;
  padding-left: 60px;
  right: -38%;
  top: 25px;
}
.single-blog-card .image .date::before {
  left: 0;
  top: 50%;
  width: 50px;
  height: 1px;
  content: "";
  position: absolute;
  transform: translateY(-50%);
  background-color: #22418E;
}
.single-blog-card .content {
  background-color: #ffffff;
  padding: 25px 25px 0 25px;
  position: relative;
  margin-top: -50px;
  z-index: 1;
  margin-left: 30px;
  border-radius: 5px;
}
.single-blog-card .content h3 {
  font-size: 22px;
  margin-bottom: 12px;
  line-height: 1.5;
}
.single-blog-card .content p {
  margin-bottom: 18px;
}
.single-blog-card .content .read-more-btn {
  position: relative;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  color: #22418E;
  border-bottom: 1px solid #22418E;
  transition: 0.5s;
}
.single-blog-card .content .read-more-btn:hover {
  color: #0e314c;
  border-bottom: 1px solid #0e314c;
}

/*================================================
Subscribe Wrap Area CSS
=================================================*/
.subscribe-wrap-inner-box {
  background-image: url(../img/home-six/subscribe-wrap-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 80px 50px;
  border-radius: 20px;
}

.subscribe-wrap-box-content {
  padding: 50px;
  border: 1px solid rgba(255, 255, 255, 0.29);
  border-radius: 20px;
}
.subscribe-wrap-box-content .subscribe-content h3 {
  font-size: 34px;
  color: #ffffff;
  margin-bottom: 15px;
}
.subscribe-wrap-box-content .subscribe-content p {
  color: #ffffff;
  margin-bottom: 0;
}
.subscribe-wrap-box-content .newsletter-form {
  position: relative;
  padding-right: 180px;
}
.subscribe-wrap-box-content .newsletter-form .input-newsletter {
  display: block;
  width: 100%;
  border: none;
  background: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  height: 50px;
  padding-left: 18px;
  border-radius: 5px;
  outline: 0;
  font-size: 16px;
  font-weight: 400;
}
.subscribe-wrap-box-content .newsletter-form .input-newsletter::-moz-placeholder {
  color: #ffffff;
  -moz-transition: 0.5s;
  transition: 0.5s;
}
.subscribe-wrap-box-content .newsletter-form .input-newsletter::placeholder {
  color: #ffffff;
  transition: 0.5s;
}
.subscribe-wrap-box-content .newsletter-form .input-newsletter:focus::-moz-placeholder {
  color: transparent;
}
.subscribe-wrap-box-content .newsletter-form .input-newsletter:focus::placeholder {
  color: transparent;
}
.subscribe-wrap-box-content .newsletter-form .validation-danger {
  color: #ffffff;
  margin-top: 10px;
}
.subscribe-wrap-box-content .newsletter-form .validation-success {
  margin-top: 10px;
}
.subscribe-wrap-box-content .newsletter-form .btn-primary {
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 5px;
  height: 50px;
}
.subscribe-wrap-box-content .newsletter-form .btn-primary::before {
  background: #0e314c;
}

/*================================================
Footer Area CSS
=================================================*/
.footer-area.footer-style-wrap {
  background-color: #222222;
}
.footer-area.footer-style-wrap .single-footer-widget .logo a {
  display: block;
}
.footer-area.footer-style-wrap .single-footer-widget .logo p {
  color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget .social-links li a {
  background-color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget .social-links li a:hover {
  background-color: #22418E;
  color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget h3 {
  color: #ffffff;
  border-bottom: 1px solid #505050;
  padding-bottom: 18px;
  position: relative;
}
.footer-area.footer-style-wrap .single-footer-widget h3::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 100px;
  background-color: #22418E;
  left: 0;
  bottom: -1px;
}
.footer-area.footer-style-wrap .single-footer-widget .list li {
  position: relative;
  padding-left: 15px;
}
.footer-area.footer-style-wrap .single-footer-widget .list li a {
  color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget .list li a:hover {
  color: #22418E;
}
.footer-area.footer-style-wrap .single-footer-widget .list li::before {
  position: absolute;
  content: "";
  height: 8px;
  width: 8px;
  background-color: #22418E;
  border-radius: 50px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li {
  color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li a {
  color: #ffffff;
}
.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li a:hover {
  color: #22418E;
}
.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li span {
  color: #ffffff;
}
.footer-area.footer-style-wrap .copyright-area {
  border-top: 1px solid #3D3D3D;
}
.footer-area.footer-style-wrap .copyright-area p {
  color: #ffffff;
}
.footer-area.footer-style-wrap .copyright-area p a {
  color: #ffffff;
}
.footer-area.footer-style-wrap .copyright-area p a:hover {
  color: #22418E;
}

/* Start "Currency Transfer Provider Demo" "CSS" */
.ptb-100 {
  padding-top: 100px;
  padding-bottom: 100px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-75 {
  padding-bottom: 75px;
}

.currency-transfer-provider-with-background-color {
  background-color: #F4F7F9;
}

.section-title.ctp-title {
  max-width: 1050px;
  margin-top: -10px;
  margin-bottom: 38px;
}
.section-title.ctp-title h2 {
  color: #212529;
  margin-bottom: 0;
  font-weight: bold;
}

/*================================================
Navbar Area CSS
=================================================*/
.currency-transfer-provider-navbar {
  background: rgba(0, 0, 0, 0.3);
}
.currency-transfer-provider-navbar.is-sticky {
  background-color: #ffffff !important;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav {
  background-color: transparent;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .navbar-list ul li a {
  color: #0e314c;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .navbar-list ul li a:hover {
  color: #22418E;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button {
  color: #0e314c;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon i {
  color: #0e314c;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon i {
  color: #0e314c;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .login-btn {
  color: #0e314c;
}
.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .login-btn:hover {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .container-fluid {
  padding-left: 35px;
  padding-right: 35px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list {
  padding: 0px 30px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul {
  padding: 0;
  margin-bottom: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li {
  display: inline-block;
  margin-right: 35px;
  position: relative;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  font-size: 16px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li::before {
  content: "";
  position: absolute;
  right: -20px;
  top: 4px;
  height: 15px;
  width: 2px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li:last-child {
  margin-right: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li:last-child::before {
  display: none;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li a {
  color: #ffffff;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li a:hover {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a {
  color: #ffffff;
  font-weight: 500;
  font-size: 16px;
  transition: 0.5s;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a:hover, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a:focus, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a.active {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item:hover a, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item.active a {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {
  color: #0e314c;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options {
  margin-left: 30px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item {
  margin-right: 25px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item:last-child {
  margin-right: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .login-btn {
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  font-size: 16px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .login-btn:hover {
  color: #22418E;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option {
  padding: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button {
  padding: 0;
  background-color: transparent;
  border: none;
  color: #ffffff;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  position: relative;
  padding-left: 22px;
  padding-right: 20px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon {
  position: absolute;
  left: 0;
  top: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon i {
  font-size: 15px;
  color: #ffffff;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon {
  position: absolute;
  right: 0;
  top: -2px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon i {
  font-size: 12px;
  color: #ffffff;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button::after {
  display: none;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu {
  border-radius: 0;
  border: 0;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 0;
  margin-top: 12px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  color: #6084a4;
  font-size: 14px;
  padding: 10px 20px;
  border-bottom: 1px solid #eeeeee;
  border-radius: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item img {
  width: 25px;
  margin-right: 10px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item.selected {
  background-color: #22418E;
  color: #ffffff;
  border-radius: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:active {
  background-color: #22418E;
  color: #ffffff;
  border-radius: 0;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary {
  border-radius: 30px;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary::before {
  border-radius: 30px;
}
.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary::after {
  border-radius: 30px;
}

.others-option-for-responsive {
  display: none;
}
.others-option-for-responsive .dot-menu {
  padding: 0 10px;
  height: 30px;
  cursor: pointer;
  z-index: 9991;
  position: absolute;
  right: 52px;
  top: -32px;
  font-weight: 400;
}
.others-option-for-responsive .dot-menu .inner {
  display: flex;
  align-items: center;
  height: 30px;
}
.others-option-for-responsive .dot-menu .inner .circle {
  height: 5px;
  width: 5px;
  border-radius: 100%;
  margin: 0 2px;
  transition: 0.5s;
  background-color: #ffffff;
}
.others-option-for-responsive .dot-menu:hover .inner .circle {
  background-color: #22418E;
}
.others-option-for-responsive .container {
  position: relative;
}
.others-option-for-responsive .container .container {
  position: absolute;
  right: 10px;
  top: 10px;
  max-width: 180px;
  background-color: #ffffff;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
  margin-left: auto;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
  transform: scaleX(0);
  z-index: 2;
  padding: 15px;
  border-radius: 0;
}
.others-option-for-responsive .container .container.active {
  opacity: 1;
  visibility: visible;
  transform: scaleX(1);
}
.others-option-for-responsive .option-inner .others-options {
  margin-left: 0;
  display: block !important;
  text-align: center;
}
.others-option-for-responsive .option-inner .others-options .options-item {
  margin-right: 0;
  margin-bottom: 20px;
}
.others-option-for-responsive .option-inner .others-options .options-item:last-child {
  margin-bottom: 0;
}
.others-option-for-responsive .option-inner .others-options .options-item .login-btn {
  color: #6084a4;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  font-size: 16px;
}
.others-option-for-responsive .option-inner .others-options .options-item .login-btn:hover {
  color: #22418E;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button {
  padding: 0;
  background-color: transparent;
  border: none;
  color: #6084a4;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  position: relative;
  padding-left: 22px;
  padding-right: 20px;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button .globe-icon {
  position: absolute;
  left: 0;
  top: 0;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button .globe-icon i {
  font-size: 15px;
  color: #6084a4;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button .chevron-down-icon {
  position: absolute;
  right: 0;
  top: -2px;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button .chevron-down-icon i {
  font-size: 12px;
  color: #6084a4;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option button::after {
  display: none;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu {
  border-radius: 0;
  border: 0;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 0;
  margin-top: 12px;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  color: #6084a4;
  font-size: 14px;
  padding: 10px 20px;
  border-bottom: 1px solid #eeeeee;
  border-radius: 0;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item img {
  width: 25px;
  margin-right: 10px;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item.selected {
  background-color: #22418E;
  color: #ffffff;
  border-radius: 0;
}
.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:active {
  background-color: #22418E;
  color: #ffffff;
  border-radius: 0;
}
.others-option-for-responsive .option-inner .others-options .options-item .btn-primary {
  border-radius: 30px;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.others-option-for-responsive .option-inner .others-options .options-item .btn-primary::before {
  border-radius: 30px;
}
.others-option-for-responsive .option-inner .others-options .options-item .btn-primary::after {
  border-radius: 30px;
}

/*================================================
Currency Transfer Provider Banner Area CSS
=================================================*/
.ctp-banner-area {
  background-image: url(../img/currency-transfer-provider/banner/banner-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  overflow: hidden;
  padding-top: 190px;
  padding-bottom: 100px;
}
.ctp-banner-area .container-fluid {
  padding-left: 35px;
  padding-right: 35px;
}
.ctp-banner-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90.09deg, #000000 0.07%, #000000 56.23%, rgba(0, 0, 0, 0) 91.07%);
  opacity: 0.5;
  z-index: -1;
}

.ctp-banner-content {
  position: relative;
  top: -10px;
}
.ctp-banner-content h1 {
  font-size: 75px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0;
}
.ctp-banner-content h1 span {
  position: relative;
  z-index: 1;
  display: inline-block;
}
.ctp-banner-content h1 span::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  background-image: url(../img/currency-transfer-provider/banner/circle.png);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 318px;
  height: 97px;
  z-index: -1;
}
.ctp-banner-content .ctp-list {
  padding: 0;
  margin-top: 25px;
  margin-bottom: 0;
}
.ctp-banner-content .ctp-list li {
  list-style-type: none;
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  margin-bottom: 20px;
  position: relative;
  padding-left: 30px;
}
.ctp-banner-content .ctp-list li:last-child {
  margin-bottom: 0;
}
.ctp-banner-content .ctp-list li img {
  position: absolute;
  left: 0;
  top: 2px;
}
.ctp-banner-content .video-view {
  margin-top: 30px;
}
.ctp-banner-content .video-view .video-btn i {
  display: inline-block;
  height: 65px;
  width: 65px;
  line-height: 65px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  color: #ffffff;
  font-size: 30px;
  text-align: center;
  border-radius: 50px;
}
.ctp-banner-content .video-view .video-btn span {
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  color: #ffffff;
  margin-left: 10px;
}

.ctp-banner-form {
  max-width: 550px;
  margin-left: auto;
}
.ctp-banner-form .form-header {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  padding: 25px;
  border-radius: 15px;
  text-align: center;
}
.ctp-banner-form .form-header span {
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  color: #ffffff;
  display: inline-block;
  margin-bottom: 12px;
}
.ctp-banner-form .form-header h3 {
  font-size: 30px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 0;
}
.ctp-banner-form .form-content {
  background-color: #ffffff;
  padding: 50px 30px 40px;
  border-radius: 0 0 15px 15px;
  max-width: 490px;
  margin: auto;
}
.ctp-banner-form .form-content .form-group {
  position: relative;
  margin-bottom: 35px;
}
.ctp-banner-form .form-content .form-group.zero {
  margin-bottom: 0;
}
.ctp-banner-form .form-content .form-group label {
  display: block;
  background-color: #ffffff;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
  position: absolute;
  left: 20px;
  top: -10px;
  z-index: 1;
  padding: 5px 10px;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
}
.ctp-banner-form .form-content .form-group .nice-select {
  height: auto;
  line-height: normal;
  padding: 40px 50px 15px 20px;
  float: unset;
  border: none;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
}
.ctp-banner-form .form-content .form-group .nice-select:after {
  right: 20px;
  border-bottom: 2px solid #212529;
  border-right: 2px solid #212529;
  width: 8px;
  height: 8px;
  margin-top: 8px;
}
.ctp-banner-form .form-content .form-group .nice-select .list {
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  width: 100%;
  padding-top: 20px;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 20px;
  margin-top: 0;
  margin-bottom: 0;
}
.ctp-banner-form .form-content .form-group .nice-select .list .option {
  line-height: initial;
  min-height: auto;
  margin-top: 12px;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Raleway", sans-serif;
  transition: 0.5s;
}
.ctp-banner-form .form-content .form-group .nice-select .list .option:hover, .ctp-banner-form .form-content .form-group .nice-select .list .option:focus, .ctp-banner-form .form-content .form-group .nice-select .list .option.focus, .ctp-banner-form .form-content .form-group .nice-select .list .option.selected {
  background-color: transparent;
  font-weight: 500;
}
.ctp-banner-form .form-content .form-group .nice-select .list .option:hover {
  color: #22418E;
}
.ctp-banner-form .form-content .form-group .nice-select .list .option:first-child {
  margin-top: 0;
}
.ctp-banner-form .form-content .form-group .form-control {
  height: 100%;
  padding: 40px 100px 15px 20px;
  float: unset;
  border: none;
  font-size: 16.5px;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
}
.ctp-banner-form .form-content .form-group .form-control::-moz-placeholder {
  color: #212529;
}
.ctp-banner-form .form-content .form-group .form-control::placeholder {
  color: #212529;
}
.ctp-banner-form .form-content .form-group .amount-currency-select {
  position: absolute;
  right: 20px;
  bottom: 15px;
}
.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select {
  padding: 2px 25px 0 0;
  display: inline-block;
  border: none;
  line-height: normal;
  background-color: transparent;
}
.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select:after {
  right: 5px;
  margin-top: -5px;
}
.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select .list {
  width: auto;
}
.ctp-banner-form .form-content .info {
  margin-top: 18px;
  margin-bottom: 20px;
}
.ctp-banner-form .form-content .info p {
  font-size: 16px;
  color: #57647C;
  font-family: "Raleway", sans-serif;
}
.ctp-banner-form .form-content .info p span {
  color: #000000;
  font-weight: 600;
}
.ctp-banner-form .form-content .btn-primary {
  padding: 20px 30px;
  border-radius: 50px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  display: block;
  width: 100%;
  transition: 0.5s;
}
.ctp-banner-form .form-content .btn-primary::before {
  border-radius: 50px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
}
.ctp-banner-form .form-content .btn-primary::after {
  border-radius: 50px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}

/*================================================
Countries Area CSS
=================================================*/
.ctp-countries-card {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 100px;
  padding: 10px 20px 10px 10px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  transition: 0.5s;
}
.ctp-countries-card img {
  margin-right: 10px;
  max-width: 40px;
}
.ctp-countries-card span {
  font-size: 16px;
  color: #212529;
  font-weight: 400;
  font-family: "Raleway", sans-serif;
}
.ctp-countries-card:hover {
  transform: translateY(-5px);
}

/*================================================
Money Transfer Area CSS
=================================================*/
.ctp-money-transfer-card {
  max-width: 385px;
  margin: 0 auto 25px;
  text-align: center;
  transition: 0.5s;
}
.ctp-money-transfer-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #212529;
}
.ctp-money-transfer-card .image {
  display: inline-block;
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  height: 150px;
  width: 150px;
  line-height: 150px;
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}
.ctp-money-transfer-card .image .number {
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  font-size: 15px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  color: #ffffff;
  border-radius: 50px;
  position: absolute;
  right: 0;
  top: 10px;
  transition: 0.5s;
  z-index: 1;
}
.ctp-money-transfer-card .image .number::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-money-transfer-card p {
  margin-bottom: 0;
  color: #57647C;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
}
.ctp-money-transfer-card:hover {
  transform: translateY(-5px);
}
.ctp-money-transfer-card:hover .image .number::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Choose Area CSS
=================================================*/
.ctp-choose-area {
  overflow: hidden;
}
.ctp-choose-area .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.ctp-choose-image {
  background-image: url(../img/currency-transfer-provider/choose.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}
.ctp-choose-image.with-border-radius {
  border-radius: 15px;
}

.ctp-choose-content {
  padding: 85px 90px;
}
.ctp-choose-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 15px;
}
.ctp-choose-content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
  font-size: 16px;
}
.ctp-choose-content .choose-inner-card {
  margin-top: 25px;
}
.ctp-choose-content .choose-inner-card h4 {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  position: relative;
  margin-bottom: 16.5px;
  padding-left: 40px;
}
.ctp-choose-content .choose-inner-card h4 .icon {
  display: inline-block;
  height: 25px;
  width: 25px;
  line-height: 25px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  text-align: center;
  color: #ffffff;
  border-radius: 50px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 15px;
  transition: 0.5s;
  z-index: 1;
}
.ctp-choose-content .choose-inner-card h4 .icon::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 25px;
  width: 25px;
  line-height: 25px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-choose-content .choose-inner-card h4:hover .icon::before {
  opacity: 1;
  visibility: visible;
}
.ctp-choose-content.without-padding {
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 0;
  padding-right: 50px;
}

/*================================================
Services Area CSS
=================================================*/
.ctp-services-card {
  margin-bottom: 25px;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
  border-radius: 15px;
  padding: 25px;
  transition: 0.5s;
}
.ctp-services-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  position: relative;
  margin-bottom: 16.5px;
  padding: 10.5px 0 10.5px 65px;
}
.ctp-services-card h3 .icon {
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  text-align: center;
  color: #ffffff;
  border-radius: 50px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 15px;
  transition: 0.5s;
  z-index: 1;
}
.ctp-services-card h3 .icon::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-services-card h3 .icon img {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin: auto;
}
.ctp-services-card h3:hover .icon::before {
  opacity: 1;
  visibility: visible;
}
.ctp-services-card p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
  font-size: 16px;
}
.ctp-services-card:hover {
  transform: translateY(-5px);
}

/*================================================
Key Features Area CSS
=================================================*/
.ctp-key-features-area {
  background-color: #E5F6F0;
}

.ctp-key-features-tabs .nav {
  padding: 0;
  list-style-type: none;
  border: none;
  text-align: center;
  justify-content: center;
  border-radius: 0;
}
.ctp-key-features-tabs .nav .nav-item {
  margin: 0;
  border-radius: 0;
  border: none;
  font-family: "Raleway", sans-serif;
  display: flex;
  flex-wrap: wrap;
}
.ctp-key-features-tabs .nav .nav-item .nav-link {
  color: #ffffff;
  border: none;
  display: flex;
  flex-wrap: wrap;
  padding: 30px 128.5px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  position: relative;
  z-index: 1;
  font-size: 18px;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  border-radius: 0;
}
.ctp-key-features-tabs .nav .nav-item .nav-link::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
}
.ctp-key-features-tabs .nav .nav-item .nav-link:hover::before, .ctp-key-features-tabs .nav .nav-item .nav-link.active::before {
  opacity: 1;
  visibility: visible;
}
.ctp-key-features-tabs .tab-content .tab-pane {
  background-color: #ffffff;
  padding: 80px 100px;
  border-radius: 0 0 15px 15px;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-image {
  background-image: url(../img/currency-transfer-provider/key-features.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  border-radius: 15px;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 100px;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list {
  padding: 0;
  margin-bottom: 0;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li {
  list-style-type: none;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-weight: 400;
  margin-bottom: 20px;
  position: relative;
  padding-left: 28px;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li:last-child {
  margin-bottom: 0;
}
.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li i {
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 18px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

/*================================================
Working People Area CSS
=================================================*/
.ctp-working-people-area {
  overflow: hidden;
}
.ctp-working-people-area .container-fluid {
  padding-left: 35px;
  padding-right: 0;
}

.ctp-working-people-content {
  padding-top: 70px;
  padding-bottom: 70px;
  padding-right: 70px;
}
.ctp-working-people-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 0;
}
.ctp-working-people-content .working-people-inner-card {
  position: relative;
  margin-top: 30px;
  padding-left: 125px;
}
.ctp-working-people-content .working-people-inner-card .icon {
  display: inline-block;
  height: 115px;
  width: 100px;
  line-height: 115px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  text-align: center;
  color: #ffffff;
  border-radius: 10px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 15px;
  transition: 0.5s;
  z-index: 1;
}
.ctp-working-people-content .working-people-inner-card .icon::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 115px;
  width: 100px;
  line-height: 115px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  border-radius: 10px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-working-people-content .working-people-inner-card h4 {
  font-size: 20px;
  color: #212529;
  font-weight: 600;
  margin-bottom: 15px;
}
.ctp-working-people-content .working-people-inner-card p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
  font-size: 16px;
}
.ctp-working-people-content .working-people-inner-card:hover .icon::before {
  opacity: 1;
  visibility: visible;
}

.ctp-working-people-image {
  display: inline-block;
  background-image: url(../img/currency-transfer-provider/working-people/working-people.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

/*================================================
Protec Area CSS
=================================================*/
.ctp-protec-card {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  padding: 35px 30px 35px 0;
  margin-bottom: 25px;
  transition: 0.5s;
}
.ctp-protec-card .content {
  position: relative;
  padding-left: 110px;
}
.ctp-protec-card .content .number {
  display: inline-block;
  width: 85px;
  height: 120px;
  line-height: 120px;
  background: #E5F6F0;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
}
.ctp-protec-card .content .number span {
  display: inline-block;
  font-size: 40px;
  font-family: "Raleway", sans-serif;
  font-weight: bold;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: rotate(-90deg);
}
.ctp-protec-card .content .number.color-two {
  background: #F8F5EE;
}
.ctp-protec-card .content h3 {
  font-size: 20px;
  color: #212529;
  font-weight: 600;
  margin-bottom: 15px;
}
.ctp-protec-card .content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
  font-size: 16px;
}
.ctp-protec-card:hover {
  transform: translateY(-5px);
}

/*================================================
App Area CSS
=================================================*/
.ctp-app-area {
  background-image: url(../img/currency-transfer-provider/app/app-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
  padding-top: 50px;
}
.ctp-app-area .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.ctp-app-content {
  max-width: 630px;
  margin-left: auto;
  padding-right: 70px;
  position: relative;
  top: -25px;
}
.ctp-app-content .sub {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
  display: inline-block;
  margin-bottom: 15px;
}
.ctp-app-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 15px;
}
.ctp-app-content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
  font-size: 16px;
}
.ctp-app-content .btn-box {
  margin-top: 30px;
}
.ctp-app-content .btn-box .app-store-btn {
  border-radius: 50px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 11px 30px 10px 68px;
  font-size: 12px;
  font-family: "Raleway", sans-serif;
}
.ctp-app-content .btn-box .app-store-btn i {
  position: absolute;
  left: 30px;
  top: 11px;
}
.ctp-app-content .btn-box .app-store-btn i::before {
  font-size: 25px;
}
.ctp-app-content .btn-box .app-store-btn span {
  display: block;
  margin-top: 5px;
  font-size: 15px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.ctp-app-content .btn-box .app-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
}
.ctp-app-content .btn-box .app-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-app-content .btn-box .app-store-btn:hover {
  color: #ffffff;
}
.ctp-app-content .btn-box .app-store-btn:hover::after {
  opacity: 1;
  visibility: visible;
}
.ctp-app-content .btn-box .app-store-btn:hover::before {
  opacity: 0;
  visibility: hidden;
}
.ctp-app-content .btn-box .play-store-btn {
  margin-left: 12px;
  border-radius: 50px;
  display: inline-block;
  position: relative;
  z-index: 1;
  color: #ffffff;
  padding: 11px 30px 10px 68px;
  font-size: 12px;
  font-family: "Raleway", sans-serif;
}
.ctp-app-content .btn-box .play-store-btn i {
  position: absolute;
  left: 30px;
  top: 12px;
}
.ctp-app-content .btn-box .play-store-btn i::before {
  font-size: 25px;
}
.ctp-app-content .btn-box .play-store-btn span {
  display: block;
  margin-top: 5px;
  font-size: 15px;
  font-weight: 600;
  font-family: "Raleway", sans-serif;
}
.ctp-app-content .btn-box .play-store-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  z-index: -1;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-app-content .btn-box .play-store-btn::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  z-index: -1;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
}
.ctp-app-content .btn-box .play-store-btn:hover {
  color: #ffffff;
}
.ctp-app-content .btn-box .play-store-btn:hover::after {
  opacity: 0;
  visibility: hidden;
}
.ctp-app-content .btn-box .play-store-btn:hover::before {
  opacity: 1;
  visibility: visible;
}
.ctp-app-content .info {
  margin-top: 30px;
}
.ctp-app-content .info span {
  color: #212529;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  font-size: 20px;
}

.ctp-app-image {
  text-align: end;
}

/*================================================
Reviews Area CSS
=================================================*/
.ctp-reviews-box {
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  background-color: #ffffff;
  padding: 30px 35px;
  transition: 0.5s;
  margin-bottom: 25px;
}
.ctp-reviews-box .rating {
  padding: 0;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.ctp-reviews-box .rating li {
  list-style-type: none;
  display: inline-block;
  margin-right: 1px;
}
.ctp-reviews-box .rating li:last-child {
  margin-right: 0;
}
.ctp-reviews-box .rating li span {
  font-size: 15px;
  font-weight: 400;
  font-family: "Raleway", sans-serif;
  margin-left: 10px;
  color: #57647C;
}
.ctp-reviews-box h3 {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12px;
}
.ctp-reviews-box p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 12px;
  font-size: 16px;
}
.ctp-reviews-box h4 {
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 600;
  color: #0A2049;
}
.ctp-reviews-box h4 span {
  font-size: 15px;
  font-weight: 400;
  font-family: "Raleway", sans-serif;
  color: #57647C;
}
.ctp-reviews-box:hover {
  transform: translateY(-5px);
}

/*================================================
FAQ Area CSS
=================================================*/
.ctp-faq-accordion {
  max-width: 1095px;
  margin: auto;
}
.ctp-faq-accordion .accordion-item {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  margin-bottom: 15px;
  transition: 0.5s;
  border: none;
}
.ctp-faq-accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.ctp-faq-accordion .accordion-item .accordion-button {
  color: #0A2049;
  position: relative;
  box-shadow: unset;
  margin-bottom: 0;
  display: block;
  border: none;
  width: 100%;
  font-size: 20px;
  font-weight: 600;
  padding: 20px 75px 20px 30px;
  transition: 0.5s;
  background-color: transparent;
  font-family: "Raleway", sans-serif;
}
.ctp-faq-accordion .accordion-item .accordion-button::before {
  position: absolute;
  content: "";
  right: 30px;
  top: 25px;
  display: inline-block;
  background-image: url(../img/currency-transfer-provider/arrow-circle-up.svg);
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
}
.ctp-faq-accordion .accordion-item .accordion-button.collapsed::before {
  background-image: url(../img/currency-transfer-provider/arrow-circle-down.svg);
}
.ctp-faq-accordion .accordion-item .accordion-body {
  padding: 0 30px 30px 30px;
}
.ctp-faq-accordion .accordion-item .accordion-body p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
}

/*================================================
Page Banner Area CSS
=================================================*/
.ctp-page-banner-area {
  background-image: url(../img/currency-transfer-provider/page-banner-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  padding-top: 185px;
  padding-bottom: 115px;
}
.ctp-page-banner-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90.09deg, #000000 0.07%, #000000 56.23%, rgba(0, 0, 0, 0) 91.07%);
  opacity: 0.5;
  z-index: -1;
}

.ctp-page-banner-content h3 {
  font-size: 80px;
  color: #ffffff;
  font-family: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  margin-bottom: 15px;
  font-weight: bold;
}
.ctp-page-banner-content .list {
  padding: 0;
  margin-bottom: 0;
}
.ctp-page-banner-content .list li {
  display: inline-block;
  list-style-type: none;
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  color: #ffffff;
  position: relative;
  margin-left: 35px;
}
.ctp-page-banner-content .list li::before {
  content: "";
  position: absolute;
  left: -16.5px;
  top: 8px;
  height: 15px;
  width: 1px;
  background: #ffffff;
  transform: rotate(20deg);
}
.ctp-page-banner-content .list li:first-child {
  margin-left: 0;
}
.ctp-page-banner-content .list li:first-child::before {
  display: none;
}
.ctp-page-banner-content .list li a {
  display: block;
  color: #ffffff;
}
.ctp-page-banner-content .list li a:hover {
  color: #22418E;
}

/*================================================
About Area CSS
=================================================*/
.ctp-about-image {
  display: inline-block;
  background-image: url(../img/currency-transfer-provider/about.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  border-radius: 15px;
}

.ctp-about-content {
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 100px;
}
.ctp-about-content span {
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
}
.ctp-about-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 15px;
}
.ctp-about-content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
}
.ctp-about-content h4 {
  font-size: 20px;
  color: #212529;
  font-weight: 600;
  margin-bottom: 15px;
}

/*================================================
Fun Facts Area CSS
=================================================*/
.ctp-funfacts-inner-box {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  padding-left: 50px;
  padding-right: 50px;
}
.ctp-funfacts-inner-box .col-lg-3 {
  border-right: 1px solid #e0e0e0;
}
.ctp-funfacts-inner-box .col-lg-3:last-child {
  border-right: none;
}

.ctp-funfact-card {
  margin-bottom: 25px;
  text-align: center;
}
.ctp-funfact-card h3 {
  font-size: 50px;
  font-weight: bold;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}
.ctp-funfact-card h3 .odometer {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.ctp-funfact-card h3 .odometer-value {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.ctp-funfact-card p {
  font-size: 18px;
  font-weight: 500;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
}

/*================================================
Team Area CSS
=================================================*/
.ctp-team-card {
  margin-bottom: 25px;
  transition: 0.5s;
}
.ctp-team-card .team-image {
  border-radius: 15px;
  overflow: hidden;
}
.ctp-team-card .team-image img {
  transition: 0.5s;
  border-radius: 15px;
}
.ctp-team-card .team-content {
  position: relative;
  margin-top: 25px;
  padding-right: 55px;
}
.ctp-team-card .team-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 0;
}
.ctp-team-card .team-content span {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  display: inline-block;
  margin-top: 10px;
}
.ctp-team-card .team-content .icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.ctp-team-card .team-content .icon a {
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  text-align: center;
  color: #ffffff;
  border-radius: 50px;
  font-size: 15px;
  transition: 0.5s;
  position: relative;
  z-index: 1;
}
.ctp-team-card .team-content .icon a::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-team-card .team-content .icon a i {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin: auto;
}
.ctp-team-card:hover {
  transform: translateY(-5px);
}
.ctp-team-card:hover .team-image img {
  transform: scale(1.1);
}
.ctp-team-card:hover .team-content .icon a::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Investors Area CSS
=================================================*/
.ctp-investors-card {
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 100px;
  background-color: #ffffff;
  margin-bottom: 25px;
  padding: 15px 10px;
  transition: 0.5s;
  text-align: center;
}
.ctp-investors-card:hover {
  transform: translateY(-5px);
}

/*================================================
Send Money Online Area CSS
=================================================*/
.ctp-send-money-online-area {
  background-image: url(../img/currency-transfer-provider/send-money-online-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  margin-left: 35px;
  margin-right: 35px;
  position: relative;
  z-index: 1;
}
.ctp-send-money-online-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.ctp-send-money-online-content h1 {
  font-size: 40px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0;
}
.ctp-send-money-online-content .ctp-list {
  padding: 0;
  margin-top: 25px;
  margin-bottom: 0;
}
.ctp-send-money-online-content .ctp-list li {
  list-style-type: none;
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  margin-bottom: 20px;
  position: relative;
  padding-left: 30px;
}
.ctp-send-money-online-content .ctp-list li:last-child {
  margin-bottom: 0;
}
.ctp-send-money-online-content .ctp-list li img {
  position: absolute;
  left: 0;
  top: 2px;
}
.ctp-send-money-online-content .video-view {
  margin-top: 30px;
}
.ctp-send-money-online-content .video-view .video-btn i {
  display: inline-block;
  height: 65px;
  width: 65px;
  line-height: 65px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  color: #ffffff;
  font-size: 30px;
  text-align: center;
  border-radius: 50px;
}
.ctp-send-money-online-content .video-view .video-btn span {
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  color: #ffffff;
  margin-left: 10px;
}

.ctp-send-money-online-form {
  max-width: 550px;
  margin-left: auto;
}
.ctp-send-money-online-form .form-header {
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  padding: 25px;
  border-radius: 15px;
  text-align: center;
}
.ctp-send-money-online-form .form-header span {
  font-size: 18px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  color: #ffffff;
  display: inline-block;
  margin-bottom: 12px;
}
.ctp-send-money-online-form .form-header h3 {
  font-size: 30px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 0;
}
.ctp-send-money-online-form .form-content {
  background-color: #ffffff;
  padding: 50px 30px 40px;
  border-radius: 0 0 15px 15px;
  max-width: 490px;
  margin: auto;
}
.ctp-send-money-online-form .form-content .form-group {
  position: relative;
  margin-bottom: 35px;
}
.ctp-send-money-online-form .form-content .form-group.zero {
  margin-bottom: 0;
}
.ctp-send-money-online-form .form-content .form-group label {
  display: block;
  background-color: #ffffff;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
  position: absolute;
  left: 20px;
  top: -10px;
  z-index: 1;
  padding: 5px 10px;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
}
.ctp-send-money-online-form .form-content .form-group .nice-select {
  height: auto;
  line-height: normal;
  padding: 40px 50px 15px 20px;
  float: unset;
  border: none;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
}
.ctp-send-money-online-form .form-content .form-group .nice-select:after {
  right: 20px;
  border-bottom: 2px solid #212529;
  border-right: 2px solid #212529;
  width: 8px;
  height: 8px;
  margin-top: 8px;
}
.ctp-send-money-online-form .form-content .form-group .nice-select .list {
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  width: 100%;
  padding-top: 20px;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 20px;
  margin-top: 0;
  margin-bottom: 0;
}
.ctp-send-money-online-form .form-content .form-group .nice-select .list .option {
  line-height: initial;
  min-height: auto;
  margin-top: 12px;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Raleway", sans-serif;
  transition: 0.5s;
}
.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:hover, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option:focus, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option.focus, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option.selected {
  background-color: transparent;
  font-weight: 500;
}
.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:hover {
  color: #22418E;
}
.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:first-child {
  margin-top: 0;
}
.ctp-send-money-online-form .form-content .form-group .form-control {
  height: 100%;
  padding: 40px 100px 15px 20px;
  float: unset;
  border: none;
  font-size: 16.5px;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
}
.ctp-send-money-online-form .form-content .form-group .form-control::-moz-placeholder {
  color: #212529;
}
.ctp-send-money-online-form .form-content .form-group .form-control::placeholder {
  color: #212529;
}
.ctp-send-money-online-form .form-content .form-group .amount-currency-select {
  position: absolute;
  right: 20px;
  bottom: 15px;
}
.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select {
  padding: 2px 25px 0 0;
  display: inline-block;
  border: none;
  line-height: normal;
  background-color: transparent;
}
.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select:after {
  right: 5px;
  margin-top: -5px;
}
.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select .list {
  width: auto;
}
.ctp-send-money-online-form .form-content .info {
  margin-top: 18px;
  margin-bottom: 20px;
}
.ctp-send-money-online-form .form-content .info p {
  font-size: 16px;
  color: #57647C;
  font-family: "Raleway", sans-serif;
}
.ctp-send-money-online-form .form-content .info p span {
  color: #000000;
  font-weight: 600;
}
.ctp-send-money-online-form .form-content .btn-primary {
  padding: 20px 30px;
  border-radius: 50px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
  display: block;
  width: 100%;
  transition: 0.5s;
}
.ctp-send-money-online-form .form-content .btn-primary::before {
  border-radius: 50px;
  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);
  transition: 0.5s;
}
.ctp-send-money-online-form .form-content .btn-primary::after {
  border-radius: 50px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
}

/*================================================
Contact Area CSS
=================================================*/
.ctp-contact-form {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  padding: 40px;
}
.ctp-contact-form h3 {
  font-size: 40px;
  font-weight: bold;
  color: #212529;
  margin-bottom: 40px;
}
.ctp-contact-form .form-group {
  position: relative;
  margin-bottom: 35px;
}
.ctp-contact-form .form-group label {
  display: block;
  background-color: #ffffff;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
  position: absolute;
  left: 20px;
  top: -10px;
  z-index: 1;
  padding: 5px 10px;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
}
.ctp-contact-form .form-group .form-control {
  height: 100%;
  padding: 40px 20px 20px 20px;
  float: unset;
  border: none;
  font-size: 15px;
  font-family: "Raleway", sans-serif;
  font-weight: 600;
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #E5E5E5;
  border-radius: 5px;
}
.ctp-contact-form .form-group .form-control::-moz-placeholder {
  color: #212529;
  -moz-transition: 0.5s;
  transition: 0.5s;
}
.ctp-contact-form .form-group .form-control::placeholder {
  color: #212529;
  transition: 0.5s;
}
.ctp-contact-form .form-group .form-control:focus {
  border: 1px solid #22418E;
}
.ctp-contact-form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.ctp-contact-form .form-group .form-control:focus::placeholder {
  color: transparent;
}
.ctp-contact-form .form-group textarea.form-control {
  height: 150px !important;
  line-height: normal;
}
.ctp-contact-form .form-group .list-unstyled li {
  color: #22418E;
  font-size: 15px;
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  margin-top: 10px;
}
.ctp-contact-form .btn-primary {
  padding: 20px 30px;
  border-radius: 50px;
  width: 100%;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.ctp-contact-form .btn-primary::before {
  border-radius: 50px;
}
.ctp-contact-form .btn-primary::after {
  border-radius: 50px;
}
.ctp-contact-form .text-danger {
  font-size: 20px;
  font-weight: 600;
  margin-top: 15px;
  margin-bottom: 0;
}

.ctp-contact-information .information-box {
  background-color: #ffffff;
  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  padding: 40px;
  margin-bottom: 25px;
}
.ctp-contact-information .information-box h3 {
  font-size: 20px;
  font-weight: bold;
  color: #212529;
  margin-bottom: 18px;
}
.ctp-contact-information .information-box .contact-info {
  padding: 0;
  margin-bottom: 0;
}
.ctp-contact-information .information-box .contact-info li {
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  color: #57647C;
  list-style-type: none;
  margin-bottom: 15px;
  position: relative;
  padding-left: 85px;
}
.ctp-contact-information .information-box .contact-info li:last-child {
  margin-bottom: 0;
}
.ctp-contact-information .information-box .contact-info li.email {
  padding-left: 65px;
}
.ctp-contact-information .information-box .contact-info li.phone {
  padding-left: 65px;
}
.ctp-contact-information .information-box .contact-info li .sub {
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 600;
  position: absolute;
  left: 0;
  top: 0;
}
.ctp-contact-information .information-box .contact-info li .info {
  margin-bottom: 10px;
}
.ctp-contact-information .information-box .contact-info li .info:last-child {
  margin-bottom: 0;
}
.ctp-contact-information .information-box .contact-info li .info span {
  color: #212529;
  font-size: 16px;
  font-weight: 600;
  display: block;
  margin-bottom: 2px;
  font-family: "Raleway", sans-serif;
}
.ctp-contact-information .information-box .contact-info li .info a {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  font-weight: 400;
}
.ctp-contact-information .information-box .contact-info li .info a:hover {
  color: #22418E;
}
.ctp-contact-information .information-box .time-info {
  padding: 0;
  margin-bottom: 0;
}
.ctp-contact-information .information-box .time-info li {
  list-style-type: none;
  margin-bottom: 15px;
}
.ctp-contact-information .information-box .time-info li:last-child {
  margin-bottom: 0;
}
.ctp-contact-information .information-box .time-info li .color {
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 600;
}
.ctp-contact-information .information-box .time-info li span {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  font-weight: 400;
}
.ctp-contact-information .information-map .map {
  border-radius: 15px;
  width: 100%;
  height: 300px;
  margin-bottom: -8px;
}

/*================================================
Currency Area CSS
=================================================*/
.ctp-currency-image {
  display: inline-block;
  background-image: url(../img/currency-transfer-provider/currency.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  border-radius: 15px;
}

.ctp-currency-content {
  padding-top: 70px;
  padding-bottom: 70px;
  padding-left: 80px;
}
.ctp-currency-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 15px;
}
.ctp-currency-content p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
}
.ctp-currency-content .list {
  padding: 0;
  margin-top: 20px;
  margin-bottom: 0;
}
.ctp-currency-content .list li {
  list-style-type: none;
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  position: relative;
  padding-left: 30px;
}
.ctp-currency-content .list li:last-child {
  margin-bottom: 0;
}
.ctp-currency-content .list li img {
  position: absolute;
  left: 0;
  top: 3.5px;
}
.ctp-currency-content .currency-btn {
  margin-top: 30px;
}
.ctp-currency-content .currency-btn .btn-primary {
  border-radius: 30px;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  font-family: "Raleway", sans-serif;
}
.ctp-currency-content .currency-btn .btn-primary::before {
  border-radius: 30px;
}
.ctp-currency-content .currency-btn .btn-primary::after {
  border-radius: 30px;
}

/*================================================
World Area CSS
=================================================*/
.ctp-world-content {
  padding-top: 100px;
  padding-bottom: 100px;
  padding-right: 100px;
}
.ctp-world-content h3 {
  font-size: 40px;
  color: #212529;
  font-weight: bold;
  margin-bottom: 15px;
}
.ctp-world-content .world-inner-card {
  margin-top: 30px;
}
.ctp-world-content .world-inner-card h4 {
  font-size: 20px;
  color: #212529;
  font-weight: 600;
  margin-bottom: 15px;
}
.ctp-world-content .world-inner-card p {
  color: #57647C;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  margin-bottom: 0;
}

.ctp-world-image {
  display: inline-block;
  background-image: url(../img/currency-transfer-provider/world.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  border-radius: 15px;
}

/*================================================
Footer Area CSS
=================================================*/
.ctp-footer-area {
  background-color: #212529;
}
.ctp-footer-area .copyright-area {
  border-top: 1px solid #414243;
  margin-top: 75px;
  padding-top: 30px;
  padding-bottom: 30px;
}
.ctp-footer-area .copyright-area p {
  color: #ffffff;
  font-size: 16px;
  font-family: "Raleway", sans-serif;
  margin-bottom: 0;
}
.ctp-footer-area .copyright-area p a {
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 600;
}

.ctp-footer-widget {
  margin-bottom: 25px;
}
.ctp-footer-widget .logo {
  margin-bottom: 25px;
}
.ctp-footer-widget .social-links {
  padding: 0;
  margin-bottom: 0;
}
.ctp-footer-widget .social-links span {
  font-size: 16px;
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  display: block;
  margin-bottom: 15px;
}
.ctp-footer-widget .social-links li {
  list-style-type: none;
  display: inline-block;
  margin-right: 8px;
}
.ctp-footer-widget .social-links li:last-child {
  margin-right: 0;
}
.ctp-footer-widget .social-links li a {
  display: inline-block;
  height: 35px;
  width: 35px;
  line-height: 35px;
  background: #57647C;
  text-align: center;
  color: #ffffff;
  border-radius: 50px;
  font-size: 15px;
  transition: 0.5s;
  position: relative;
  z-index: 1;
}
.ctp-footer-widget .social-links li a::before {
  position: absolute;
  content: "";
  display: inline-block;
  height: 35px;
  width: 35px;
  line-height: 35px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.ctp-footer-widget .social-links li a i {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin: auto;
}
.ctp-footer-widget .social-links li a:hover::before {
  opacity: 1;
  visibility: visible;
}
.ctp-footer-widget h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 25px;
  font-family: "Raleway", sans-serif;
}
.ctp-footer-widget .links {
  padding: 0;
  margin-bottom: 0;
}
.ctp-footer-widget .links li {
  list-style-type: none;
  margin-bottom: 15px;
  font-family: "Raleway", sans-serif;
}
.ctp-footer-widget .links li:last-child {
  margin-bottom: 0;
}
.ctp-footer-widget .links li a {
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  font-weight: 400;
  position: relative;
  padding-left: 22px;
}
.ctp-footer-widget .links li a:hover {
  color: #22418E;
}
.ctp-footer-widget .links li a::before {
  position: absolute;
  content: "";
  height: 10px;
  width: 10px;
  border-radius: 50px;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  left: 0;
  top: 3.8px;
}
.ctp-footer-widget .info {
  padding: 0;
  margin-bottom: 0;
}
.ctp-footer-widget .info li {
  list-style-type: none;
  color: #ffffff;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}
.ctp-footer-widget .info li:last-child {
  margin-bottom: 0;
}
.ctp-footer-widget .info li span {
  font-family: "Raleway", sans-serif;
  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 600;
}
.ctp-footer-widget .info li a {
  color: #ffffff;
}
.ctp-footer-widget .info li a:hover {
  color: #22418E;
}

/* End "Currency Transfer Provider Demo" "CSS" *//*# sourceMappingURL=style.css.map */
/* OWL carousel */

.shadow-effect {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  border: 1px solid #ECECEC;
  box-shadow: 0 19px 38px rgba(0,0,0,0.10), 0 15px 12px rgba(0,0,0,0.02);
}
#shadow-effect p {
  font-family: inherit;
  font-size: 17px;
  line-height: 1.5;
  margin: 0 0 17px 0;
  font-weight: 300;
}
.img-circle {
  border-radius: 50%;
  vertical-align: middle;
  max-width: 90px;
  min-height: 90px;
  transform-style: preserve-3d;
  margin: 0 auto 17px;
}
#customer-testimonoals {
  margin-top: 6%;
}
.testimonial-name {
  margin: -17px auto 0;
  display: table;
  width: auto;
  /* background: linear-gradient(100deg, #845EC2, #BE93FD); */
  background: linear-gradient(100deg, #FF8066, #FF918D);
  /* background: linear-gradient(135deg, #ff3e00, #eaa400); */
  padding: 9px 35px;
  border-radius: 12px;
  text-align: center;
  color: #fff;
  box-shadow: 0px 9px 18px rgba(0,0,0,0.12), 0px 5px 7px rgba(0,0,0,0.5);
}
#customer-testimonoals .item {
  text-align: center;
  padding: 50px;
  margin-bottom: 0px;
  opacity: 0.2;
  transform: scale3d(0.8, 0.8, 1);
  transition: all 0.3s ease-in-out;
}
#customer-testimonoals .owl-item.active.center .item {
  opacity: 1;
  transform: scale3d(1.0, 1.0, 1);
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
  background: #56423D;
  transform: translate3d(0px, -50%, 0px) scale(0.8);
}
.owl-carousel .owl-dots {
  display: inline-block;
  width: 100%;
  text-align: center;
}
.owl-carousel .owl-dots .owl-dot{ 
  display: inline-block;
}
.owl-carousel .owl-dots .owl-dot span{ 
  /* background: #eaa400; */
  background: #56423D;
  display: inline-block;
  height: 20px;
  width: 20px;
  margin: 0 2px 5px;
  transform: translate3d(0px, -50%, 0px) scale(0.3);
  transform-origin: 50% 50% 0;
  transition: all 250ms ease-out 0s;
} 
