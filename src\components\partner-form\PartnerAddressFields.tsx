import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { CountryList, getStateList } from "@/data/countries";

interface PartnerAddressFieldsProps {
  address: {
    country: string;
    state: string;
    city: string;
    zip: string;
    street: string;
  };
  setFormData: React.Dispatch<React.SetStateAction<any>>;
}

export function PartnerAddressFields({
  address,
  setFormData,
}: PartnerAddressFieldsProps) {
  const [openCountry, setOpenCountry] = useState(false);
  const countryOptions = CountryList();
  const [openState, setOpenState] = useState(false);
  const stateOptions = getStateList(address.country);

  const handleCountryChange = (value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      address: {
        ...prev.address,
        country: value,
        state: "", // Reset state when country changes
      },
    }));
    setOpenCountry(false);
  };

  const handleStateChange = (label: string) => {
    setFormData((prev: any) => ({
      ...prev,
      address: {
        ...prev.address,
        state: label,
      },
    }));
  };

  // Find the selected country's label for display
  const selectedCountry = countryOptions.find(
    (country) => country.value === address.country
  );

  const selectedState = stateOptions.find(
    (state) => state.label === address.state
  );

  return (
    <div className="space-y-2">
      <Label>Address</Label>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="country">Country *</Label>
          <Popover open={openCountry} onOpenChange={setOpenCountry}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openCountry}
                className="w-full justify-between"
              >
                {selectedCountry && selectedCountry.value !== ""
                  ? selectedCountry.label
                  : "Select a country..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search countries..." />
                <CommandEmpty>No country found.</CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {countryOptions
                    .filter((country) => country.value !== "")
                    .map((country) => (
                      <CommandItem
                        key={country.value}
                        value={country.label}
                        onSelect={() => handleCountryChange(country.value)}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            address.country === country.value
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        {country.label}
                      </CommandItem>
                    ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-2">
          <Label htmlFor="state">State </Label>
          <Popover open={openState} onOpenChange={setOpenState}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openState}
                className="w-full justify-between"
                disabled={!address.country}
              >
                {selectedState && selectedState.value !== ""
                  ? selectedState.label
                  : "Select a state..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search states..." />
                <CommandEmpty>No state found.</CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {stateOptions
                    .filter((state) => state.value !== "")
                    .map((state) => (
                      <CommandItem
                        key={state.value}
                        value={state.label}
                        onSelect={() => {
                          handleStateChange(state.label);
                          setOpenState(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            address.state === state.label
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        {state.label}
                      </CommandItem>
                    ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City *</Label>
          <Input
            id="city"
            value={address.city}
            onChange={(e) =>
              setFormData((prev: any) => ({
                ...prev,
                address: { ...prev.address, city: e.target.value },
              }))
            }
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="zip">Zip *</Label>
          <Input
            id="zip"
            value={address.zip}
            onChange={(e) =>
              setFormData((prev: any) => ({
                ...prev,
                address: { ...prev.address, zip: e.target.value },
              }))
            }
            required
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="street">Street *</Label>
        <Input
          id="street"
          value={address.street}
          onChange={(e) =>
            setFormData((prev: any) => ({
              ...prev,
              address: { ...prev.address, street: e.target.value },
            }))
          }
          required
        />
      </div>
    </div>
  );
}
