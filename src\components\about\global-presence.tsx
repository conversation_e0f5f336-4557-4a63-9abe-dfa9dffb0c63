"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin } from "lucide-react";

export default function GlobalPresence() {
  const regions = [
    {
      region: "North America",
      description:
        "Headquarters in San Francisco with operations centers in New York and Toronto.",
      icon: <MapPin className="size-5" />,
    },
    {
      region: "European Union",
      description:
        "Regional hub in Amsterdam serving UK, Germany, France, and Nordic markets.",
      icon: <MapPin className="size-5" />,
    },
    {
      region: "Pacific Asia",
      description:
        "Singapore office supporting Australia, Japan, and Southeast Asian expansion.",
      icon: <MapPin className="size-5" />,
    },
  ];
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Global Presence
          </h2>
          <p className="text-lg text-muted-foreground">
            Operations and infrastructure spanning North America, EU, and
            Pacific Asia, supporting cross‑regional programs and ISOs.
          </p>
        </motion.div>

        <div className="grid gap-8 md:grid-cols-3">
          {regions.map((region, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg hover:border-primary/20">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-primary/10 text-primary w-fit mx-auto mb-4">
                    {region.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-3">
                    {region.region}
                  </h3>
                  <p className="text-muted-foreground">{region.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
