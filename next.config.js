module.exports = {
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Example: Add an ignore plugin
    config.plugins.push(
         new webpack.IgnorePlugin({
           resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/
      })
    );

    // Important: return the modified config
    return config;
  },
  // Other configurations can go here
  reactStrictMode: true,
};
