"use client";

import { Globe } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import HeroSection from "@/components/hero-section";
import MissionVision from "@/components/about/mission-vission";
import Leadership from "@/components/about/leadership";
import GlobalPresence from "@/components/about/global-presence";
import CTABanner from "@/components/cta-banner";

export default function AboutPage() {
  // Remove mounted state, not needed for SSR/SSG

  return (
    <div className="flex min-h-[100dvh] flex-col bg-background">
      <main className="flex-1">
        <HeroSection
          headline={
            <>
              Empowering the Future of{" "}
              <span className="text-accent-custom">Payments</span>
            </>
          }
          subtext={
            <>
              Built by payments veterans who understand the challenges of legacy
              systems and the potential of modern infrastructure.
            </>
          }
          rightIcon={<Globe className="size-8" />}
          rightTitle="Global Vision"
          rightSubtitle="Transforming payments worldwide"
          rightGradient="from-blue-500 to-green-500"
        />
        <MissionVision />
        <GlobalPresence />
        <CTABanner />
      </main>
    </div>
  );
}
