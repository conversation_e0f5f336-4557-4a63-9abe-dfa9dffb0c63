"use client";

import { Shield } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import HeroSection from "@/components/hero-section";
import Advantages from "@/components/why-ngnair/advantages";
import HowFailoverWorks from "@/components/why-ngnair/how-failover-works";
import PartnerBenefits from "@/components/why-ngnair/partner-benefits";
import CTABanner from "@/components/cta-banner";

export default function WhyNGnairPage() {
  return (
    <div className="flex min-h-[100dvh] flex-col bg-background">
      <main className="flex-1">
        <HeroSection
          badge={
            <Badge
              className="rounded-full px-4 py-1.5 text-sm font-medium"
              variant="secondary"
            >
              Why NGnair
            </Badge>
          }
          headline={
            <>
              The Modern Alternative to{" "}
              <span className="text-accent-custom">Legacy Payment Systems</span>
            </>
          }
          subtext={
            <>
              Built for resilience, scale, and seamless integration into
              software platforms.
            </>
          }
          rightIcon={<Shield className="size-8" />}
          rightTitle="Modern Platform"
          rightSubtitle="Built for today's needs"
          rightGradient="from-blue-500 to-green-500"
        />

        <Advantages />
        <HowFailoverWorks />
        <PartnerBenefits />
        <CTABanner />
      </main>
    </div>
  );
}
