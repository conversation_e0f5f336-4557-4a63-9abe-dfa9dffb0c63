"use client";

import { motion } from "framer-motion";
import { Globe, Target } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function MissionVision() {
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          <motion.div
            initial={{ opacity: 0, x: -40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 rounded-xl bg-primary/10 text-primary">
                <Target className="size-6" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold">
                Mission & Vision
              </h2>
            </div>
            <p className="text-lg text-muted-foreground mb-6">
              We empower ISOs with a modern, resilient payments platform that
              behaves like a CRM/ERP — so teams can onboard faster, operate
              smarter, and grow healthier merchant portfolios across regions.
            </p>
            <p className="text-muted-foreground">
              Our vision is to eliminate the complexity and fragmentation that
              has plagued the payments industry for decades, replacing it with
              unified, intelligent infrastructure that scales with your
              business.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10">
              <CardContent className="p-8">
                <div className="h-64 rounded-lg bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                  <Globe className="size-16 text-primary/60" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
