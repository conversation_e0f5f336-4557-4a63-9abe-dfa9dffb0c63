"use client";

import { Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import dynamic from "next/dynamic";

const PartnerModal = dynamic(() => import("@/components/partner-modal"), {
  ssr: false,
});

export default function WhyChooseNGnair() {
  return (
    <section className="w-full py-20 md:py-32 bg-section">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-custom">
              Why Choose NGnair
            </h2>
            <p className="text-lg text-secondary-custom">
              We're a team of payment professionals dedicated to helping ISOs
              and their merchant customers grow through effective modern
              infrastructure.
            </p>
            <div className="space-y-4">
              {[
                "Personalized payment strategies for your business",
                "Proven track record of hundreds of successful partnerships",
                "Dedicated account manager for your success",
                "Transparent reporting and regular updates",
                "Flexible solutions that scale with your growth",
              ].map((item, i) => (
                <div key={i} className="flex items-start gap-3">
                  <div className="size-5 bg-success rounded-full flex items-center justify-center mt-0.5">
                    <Check className="size-3 text-success" />
                  </div>
                  <span className="text-muted-foreground">{item}</span>
                </div>
              ))}
            </div>
            <PartnerModal>
              <Button className="rounded-full bg-button-primary hover:bg-button-primary-hover text-button px-8">
                Become a Partner
              </Button>
            </PartnerModal>
          </div>
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-500 to-green-500 rounded-2xl p-8 shadow-2xl">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl h-64"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
