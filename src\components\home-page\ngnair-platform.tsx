"use client";

import {
  ArrowRight,
  Shield,
  CreditCard,
  Brain,
  Zap,
  Globe,
  Badge,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function NGnairPlatform() {
  return (
    <section className="w-full py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-6 font-space-grotesk">
            Replace Legacy Tools with a High‑Margin, Full‑Lifecycle Platform
          </h2>
          <p className="text-lg text-muted-foreground mb-8 font-inter">
            NGnair unifies embedded merchant workflows: onboarding & KYC/KYB,
            AI-backed underwriting, flexible payment methods (card, RTP, FedNow
            via your processor), dispute flows, commission logic, and POS device
            integrations—all structured for ISVs, ISOs, banks, and integrated
            platforms.
          </p>
        </div>

        {/*can be put in lib*/}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
          {[
            {
              title: "End-to-end lifecycle",
              description:
                "Application → Onboarding → Payments → Merchant ROI → Support",
              icon: <ArrowRight className="size-5" />,
            },
            {
              title: "AI underwriting",
              description:
                "Configurable decisioning powered by modular risk logic and integrated ML insights.",
              icon: <Brain className="size-5" />,
            },
            {
              title: "Processor-agnostic with failover",
              description:
                "Seamlessly route transactions across processors—even during downtimes.",
              icon: <Shield className="size-5" />,
            },
            {
              title: "RTP & FedNow",
              description:
                "Offer bank-to-bank payment options through RTP and FedNow—letting merchants accept direct bank payments in lieu of card transactions, routed via their existing processor.",
              icon: <Zap className="size-5" />,
            },
            {
              title: "NGnair POS devices",
              description:
                "Complete hardware/software solutions to grow your margins",
              icon: <CreditCard className="size-5" />,
            },
            {
              title: "Global infrastructure",
              description: "North America, EU, Pacific Asia",
              icon: <Globe className="size-5" />,
            },
          ].map((feature, i) => (
            <div key={i}>
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-md">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="size-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-bold mb-2">{feature.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
