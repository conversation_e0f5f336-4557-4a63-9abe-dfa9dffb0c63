// import React, { useState, useEffect } from "react";
// import styles from "./Navbar.module.css";
// import LogoImg from "@/../public/img/logo.png";
// import Image from "next/image";
// import Link from "next/link";
// import useCheckMobileScreen from "@/components/hooks/UseCheckMobileScreen";
// import { usePathname, useRouter } from "next/navigation";
// import { RegisterButton } from "../register-button";
// import { Button } from "@/components/ui/button";
// import { NGNAIR_DOMAIN } from "@/lib/data";

// const MobileNavbar = () => {
//   const [isnavActive, setIsnavActive] = useState<boolean>(false);
//   const [industriesOpen, setIndustriesOpen] = useState<boolean>(false);
//   const [solutionsOpen, setSolutionsOpen] = useState<boolean>(false);

//   const pathname = usePathname();
//   const router = useRouter();

//   const toggleNavbar = () => {
//     setIsnavActive(!isnavActive);
//   };

//   const toggleIndustries = () => {
//     setIndustriesOpen(!industriesOpen);
//     setSolutionsOpen(false);
//   };

//   const toggleSolutions = () => {
//     setSolutionsOpen(!solutionsOpen);
//     setIndustriesOpen(false);
//   };

//   return (
//     <div className="absolute bg-[#f4f7f9] !w-full !mx-auto top-0 !block lg:!hidden">
//       <div className="container">
//         <div className="w-full flex justify-between items-center">
//           <a className="navbar-brand white-logo" href="/">
//             <Image src={LogoImg} alt="logo" height={50} />
//           </a>
//           <div className="flex items-center gap-4">
//             <button
//               id={styles.navIcon}
//               className={isnavActive ? styles.open : ""}
//               onClick={toggleNavbar}
//             >
//               <span></span>
//               <span></span>
//               <span></span>
//             </button>
//           </div>
//         </div>
//         {isnavActive && (
//           <div className="px-4 overscroll-y-auto bg-white drop-shadow-lg transition ease-in-out duration-300 z-10">
//             <div
//               className="navbar-collapse mean-menu"
//               id="navbarSupportedContent"
//             >
//               <ul className="navbar-nav">
//                 <li className="nav-item">
//                   <a href="/" className="nav-link">
//                     Home
//                   </a>
//                 </li>

//                 <li className="nav-item flex justify-between">
//                   <a href="/industries" className="nav-link">
//                     Industries
//                   </a>
//                   <button onClick={toggleIndustries} className="text-[18px]">
//                     +
//                   </button>
//                 </li>
//                 {industriesOpen && (
//                   <ul className="flex flex-col">
//                     <li className="nav-item">
//                       <a href="/industries#isos" className="nav-link">
//                         ISOs (Independent Sales Organizations)
//                       </a>
//                     </li>
//                     <li className="nav-item">
//                       <a href="/industries#isvs" className="nav-link">
//                         ISVs (Independent Software Vendors)
//                       </a>
//                     </li>
//                     <li className="nav-item">
//                       <a
//                         href="/industries#financial-institutions"
//                         className="nav-link"
//                       >
//                         Financial Institutions
//                       </a>
//                     </li>
//                     <li className="nav-item">
//                       <a href="/industries#healthcare" className="nav-link">
//                         Healthcare
//                       </a>
//                     </li>
//                     <li className="nav-item">
//                       <a href="/industries#restaurant" className="nav-link">
//                         Restaurant
//                       </a>
//                     </li>
//                     <li className="nav-item">
//                       <a href="/industries#field-services" className="nav-link">
//                         Field Services
//                       </a>
//                     </li>
//                   </ul>
//                 )}

//                 <li className="nav-item flex justify-between">
//                   <a href="/solution-integrations" className="nav-link">
//                     Solutions
//                   </a>
//                   <button onClick={toggleSolutions} className="text-[18px]">
//                     +
//                   </button>
//                 </li>
//                 {solutionsOpen && (
//                   <ul className="flex flex-col">
//                     <li className="nav-item">
//                       <a
//                         href="/solution-integrations#merchant-services"
//                         className="nav-link"
//                       >
//                         Merchant Services
//                       </a>
//                       <p className="px-[15px] text-sm">
//                         Online Boarding, AI Underwriting, Processor Agnostic
//                         Platform
//                       </p>
//                     </li>
//                     <li className="nav-item">
//                       <a
//                         href="/solution-integrations#payment-options"
//                         className="nav-link"
//                       >
//                         Payment Options
//                       </a>
//                       <p className="px-[15px] text-sm">
//                         In-Person & Mobile, Online & Contactless, Invoicing &
//                         Subscriptions
//                       </p>
//                     </li>
//                     <li className="nav-item">
//                       <a
//                         href="/solution-integrations#business-tools"
//                         className="nav-link"
//                       >
//                         Business Tools
//                       </a>
//                       <p className="px-[15px] text-sm">
//                         Merchant Management, Payouts & Disbursements, App
//                         Marketplace & Integrations
//                       </p>
//                     </li>
//                   </ul>
//                 )}

//                 <li className="nav-item">
//                   <a href="/about-us" className="nav-link">
//                     About Us
//                   </a>
//                 </li>

//                 <li className="nav-item">
//                   <a href="/contact-us" className="nav-link">
//                     Contact Us
//                   </a>
//                 </li>

//                 <li className="nav-item py-2 flex gap-2">
//                   <Button
//                     href={`${NGNAIR_DOMAIN}/login`}
//                     target="_blank"
//                     variant={"primary"}
//                   >
//                     Login
//                   </Button>
//                   {/* <Button
//                     href={`${NGNAIR_DOMAIN}/register`}
//                     target="_blank"
//                     variant={"primary"}
//                   >
//                     Register
//                   </Button> */}
//                 </li>
//               </ul>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default MobileNavbar;
