import React from 'react'
import AD1 from '@/../public/img/mobile-app1.png'
import AD2 from '@/../public/img/mobile-app2.png'
import AD3 from '@/../public/img/main-mobile.png'
import AD4 from '@/../public/img/circle.png'
import Image from 'next/image'


export default function AppDownload() {
  return (
    <>
        <section className="app-download-area">
            <div className="container">
                <div className="row align-items-center">
                    <div className="col-lg-6 col-md-12">
                        <div className="app-image">
                            <div className="main-image">
                                <Image src={AD1} className="wow animate__animated animate__fadeInLeft" alt="image" />
                                <Image src={AD2} className="wow animate__animated animate__fadeInUp" alt="image" />
                            </div>

                            <div className="main-mobile-image">
                                <Image src={AD3} className="wow animate__animated animate__fadeInUp" alt="image" /> 
                            </div>

                            <div className="circle-img">
                                <Image src={AD4} alt="image" />
                            </div>
                        </div>
                    </div>

                    <div className="col-lg-6 col-md-12">
                        <div className="app-download-content">
                            <h2>You can find all the thing you need to payout</h2>
                            <div className="bar"></div>
                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip.</p>

                            <div className="btn-box">
                                <a href="#" className="app-store-btn">
                                    <i className="flaticon-apple"></i>
                                    Download on
                                    <span>App Store</span>
                                </a>

                                <a href="#" className="play-store-btn">
                                    <i className="flaticon-play-store"></i>
                                    Download on
                                    <span>Google play</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </>
    
  )
}
