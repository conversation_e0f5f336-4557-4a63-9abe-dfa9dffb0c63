"use client";
import { preLaunchSignupIframeLink, preLaunchSignupScript } from "@/lib/links";
import React, { useEffect, useRef, useState } from "react";
import Modal from "react-bootstrap/Modal";

export default function CreateAccount() {
  const [show, setShow] = useState(false);

  const handleClose = () => setShow(false);
  const handleShow = () => {
    setShow(true);
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = preLaunchSignupScript;
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        aria-labelledby="contained-modal-title-vcenter"
        className="hidden"
        animation={false}
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body className="!p-0">
          {
            <iframe
              src={preLaunchSignupIframeLink}
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                borderRadius: "0px",
                marginTop: "0px important",
                paddingTop: "0px important",
              }}
              title="Front Page - Sign Up"
            ></iframe>
          }
        </Modal.Body>
        <Modal.Footer className="border-0"></Modal.Footer>
      </Modal>
      <section className="account-create-area">
        <div className="container">
          <div className="account-create-content">
            <h2>Ready to Transform Your Payment Operations?</h2>
            <p>Get your NGnair account today!</p>
            {/* <button className="btn btn-primary" onClick={handleShow}>
              Get Your NGnair Account
            </button> */}
            {/* <a
              href="https://app.ngnair.com/register"
              className="btn btn-primary"
            >
              Get Your NGnair Account
            </a> */}
          </div>
        </div>
      </section>
    </>
  );
}
