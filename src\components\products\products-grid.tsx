"use client";

import { motion } from "framer-motion";
import {
  CreditCard,
  Brain,
  FileText,
  Smartphone,
  BarChart,
  Users,
  Shield,
  Layers,
  Globe,
  Zap,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function ProductsGrid() {
  const products = [
    {
      title: "Payments Hub (Cards, RTP, FedNow)",
      description:
        "Enable multiple payment rails—card and bank-based (RTP, FedNow)—via your chosen processor.",
      icon: <CreditCard className="size-6" />,
      features: [
        "Smart routing with automated failover",
        "Unified settlement & deposits view",
        "Refunds, voids, tokenization",
      ],
    },
    {
      title: "AI Underwriting Engine",
      description:
        "Instant or hybrid decisions with configurable rules and ML signals per processor/bank program.",
      icon: <Brain className="size-6" />,
      features: [
        "Custom scorecards & policy tuning",
        "Document requests, case notes, audit trail",
        "Review queues & escalation workflows",
      ],
    },
    {
      title: "Merchant Onboarding & KYC/KYB",
      description:
        "Automate application collection, identity/business verification, and approvals.",
      icon: <FileText className="size-6" />,
      features: [
        "Dynamic forms & e‑signature",
        "Automated KYC/KYB checks",
        "Team assignments, SLAs, and alerts",
      ],
    },
    {
      title: "NGnair POS Marketplace",
      description:
        "Curated NGnair POS devices and software bundles for your merchant customers.",
      icon: <Smartphone className="size-6" />,
      features: [
        "Device catalog, subscriptions, and inventory controls",
        "Remote provisioning & updates",
        "Comprehensive support flows",
      ],
    },
    {
      title: "Transaction Lifecycle & Reporting",
      description:
        "Trace every payment from authorization to deposit with exporting and APIs.",
      icon: <BarChart className="size-6" />,
      features: [
        "Ledgered events & reconciliation aids",
        "Scheduled reports and webhooks",
        "Disbursement timelines & exceptions",
      ],
    },
    {
      title: "Multi‑Agent Pay Splits",
      description:
        "Assign multiple agents to a merchant with transparent splits and role‑based visibility.",
      icon: <Users className="size-6" />,
      features: [
        "Residual rules by product or processor",
        "Statement‑ready payouts",
        "Agent portals with performance dashboards",
      ],
    },
    {
      title: "Dispute Management",
      description:
        "Centralize chargebacks and retrievals with guided workflows.",
      icon: <Layers className="size-6" />,
      features: [
        "Evidence templates & file capture",
        "Status timelines and deadlines",
        "Outcome analytics by merchant, MCC, and processor",
      ],
    },
    {
      title: "Open Banking Integration",
      description:
        "Instant account verification to reduce NSF and speed onboarding.",
      icon: <Shield className="size-6" />,
      features: [
        "Bank account linking and validation",
        "Risk checks & micro‑deposit alternatives",
        "Secure consent flows",
      ],
    },
    {
      title: "Integrations Marketplace",
      description:
        "Connect finance and revenue ops tools to close the loop on customers and cash.",
      icon: <Globe className="size-6" />,
      features: [
        "Accounting (e.g., QuickBooks, Sage)",
        "CRM (e.g., HubSpot, Salesforce)",
        "ERP & data warehouses",
      ],
    },
    {
      title: "Mobile App Payments",
      description:
        "Accept contactless payments on iOS and Android with unified reporting.",
      icon: <Zap className="size-6" />,
      features: [
        "Tap‑to‑Pay support",
        "Device enrollment & roles",
        "Offline‑tolerant capture with sync",
      ],
    },
  ];
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {products.map((product, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg hover:border-primary/20">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                      {product.icon}
                    </div>
                    <h3 className="font-semibold text-lg">{product.title}</h3>
                  </div>

                  <p className="text-muted-foreground mb-6 flex-grow">
                    {product.description}
                  </p>

                  <div className="space-y-2 mb-6">
                    {product.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center gap-2 text-sm"
                      >
                        <div className="size-1.5 rounded-full bg-primary"></div>
                        <span className="text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
