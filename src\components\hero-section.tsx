import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { ReactNode } from "react";
import PartnerModal from "./partner-modal";

interface HeroSectionProps {
  headline: ReactNode;
  subtext: ReactNode;
  buttonText?: string;
  badge?: ReactNode;
  onButtonClick?: () => void;
  rightIcon?: ReactNode;
  rightTitle?: string;
  rightSubtitle?: string;
  rightGradient?: string;
  buttonIcon?: ReactNode;
  children?: ReactNode; // For custom modal or extra elements
}

export default function HeroSection({
  headline,
  subtext,
  buttonText,
  onButtonClick,
  rightIcon,
  rightTitle,
  rightSubtitle,
  rightGradient = "from-blue-500 to-green-500",
  buttonIcon,
  children,
  badge,
}: HeroSectionProps) {
  return (
    <section className="w-full py-20 md:py-32 bg-hero">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {badge && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                {badge}
              </motion.div>
            )}
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight text-primary-custom"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {headline}
            </motion.h1>
            <motion.p
              className="text-lg md:text-xl text-secondary-custom max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {subtext}
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <PartnerModal>
                {children ? (
                  children
                ) : buttonText ? (
                  <Button
                    size="lg"
                    className="rounded-full bg-button-primary hover:bg-button-primary-hover text-button h-12 px-8"
                  >
                    {buttonText}
                    {buttonIcon || <ArrowRight className="ml-2 size-4" />}
                  </Button>
                ) : null}
              </PartnerModal>
            </motion.div>
          </motion.div>
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          >
            <div
              className={`relative bg-gradient-to-br ${rightGradient} rounded-2xl p-8 shadow-2xl`}
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-xl h-64 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="size-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    {rightIcon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{rightTitle}</h3>
                  <p className="text-white/80">{rightSubtitle}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
