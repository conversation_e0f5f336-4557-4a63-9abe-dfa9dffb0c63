import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import dynamic from "next/dynamic";

const PartnerModal = dynamic(() => import("@/components/partner-modal"), {
  ssr: false,
});

export default function CTABanner() {
  return (
    <section className="w-full py-20 md:py-32 bg-gradient-to-br from-blue-700 to-purple-600 text-white">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Join NGnair’s Partner Network Today
          </h2>
          <p className="text-lg text-white/80 mb-8">
            Unlock exclusive tools, support, and growth opportunities for ISOs
            and merchant services. Don’t miss out—start your partnership now!
          </p>
          <div className="flex justify-center">
            <PartnerModal>
              <Button
                size="lg"
                variant="secondary"
                className="rounded-full h-12 px-8 bg-white text-blue-700 hover:bg-blue-100 transition"
              >
                Become a Partner
                <ArrowRight className="ml-2 size-4" />
              </Button>
            </PartnerModal>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
