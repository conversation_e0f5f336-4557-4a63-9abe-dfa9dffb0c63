export interface ValidationError {
  field: string;
  message: string;
}

export interface PartnerFormData {
  firstName: string;
  lastName: string;
  businessName: string;
  email: string;
  phone: string;
  address: {
    country: string;
    state: string;
    city: string;
    zip: string;
    street: string;
  };
}

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex = /^\+?[\d\s\-\(\)]{6,}$/;

export function validatePartnerForm(
  formData: PartnerFormData,
  selectedIndustries: string[],
  turnstileToken: string
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Security verification
  if (!turnstileToken) {
    errors.push({ field: 'turnstile', message: 'Please complete the security verification.' });
  }

  // Industries
  if (selectedIndustries.length === 0) {
    errors.push({ field: 'industries', message: 'Please select at least one industry.' });
  }

  // Required fields
  if (!formData.firstName.trim()) {
    errors.push({ field: 'firstName', message: 'First name is required.' });
  }

  if (!formData.lastName.trim()) {
    errors.push({ field: 'lastName', message: 'Last name is required.' });
  }

  if (!formData.businessName.trim()) {
    errors.push({ field: 'businessName', message: 'Business name is required.' });
  }

  if (!formData.email.trim()) {
    errors.push({ field: 'email', message: 'Email is required.' });
  } else if (!emailRegex.test(formData.email)) {
    errors.push({ field: 'email', message: 'Please enter a valid email address.' });
  }

  if (!formData.phone.trim()) {
    errors.push({ field: 'phone', message: 'Phone number is required.' });
  } else if (!phoneRegex.test(formData.phone)) {
    errors.push({ field: 'phone', message: 'Please enter a valid phone number (minimum 6 digits).' });
  }

  // Address fields
  if (!formData.address.country.trim()) {
    errors.push({ field: 'country', message: 'Country is required.' });
  }

  // if (!formData.address.state.trim()) {
  //   errors.push({ field: 'state', message: 'State/Province is required.' });
  // }

  if (!formData.address.city.trim()) {
    errors.push({ field: 'city', message: 'City is required.' });
  }

  if (!formData.address.zip.trim()) {
    errors.push({ field: 'zip', message: 'ZIP/Postal code is required.' });
  }

  if (!formData.address.street.trim()) {
    errors.push({ field: 'street', message: 'Street address is required.' });
  }

  return errors;
}

export function getFirstValidationError(
  formData: PartnerFormData,
  selectedIndustries: string[],
  turnstileToken: string
): string | null {
  const errors = validatePartnerForm(formData, selectedIndustries, turnstileToken);
  return errors.length > 0 ? errors[0].message : null;
}