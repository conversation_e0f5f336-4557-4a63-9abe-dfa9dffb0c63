"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowRight, TrendingUp, Building, Code, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";
import { Suspense } from "react";
import CTABanner from "@/components/cta-banner";
import Solutions from "@/components/solutions/solutions-grid";
import HeroSection from "@/components/hero-section";

export default function SolutionsPage() {
  return (
    <div className="flex min-h-[100dvh] flex-col bg-background">
      <main className="flex-1">
        <HeroSection
          badge={
            <Badge
              className="rounded-full px-4 py-1.5 text-sm font-medium"
              variant="secondary"
            >
              Solutions
            </Badge>
          }
          headline={
            <>
              Built for Your{" "}
              <span className="text-accent-custom">Business Model</span>
            </>
          }
          subtext={
            <>
              Whether you're an ISO, bank, or SaaS platform, NGnair provides
              tailored solutions that grow with your business and enhance your
              competitive advantage.
            </>
          }
          rightIcon={<Building className="size-8" />}
          rightTitle="Tailored Solutions"
          rightSubtitle="For every business model"
          rightGradient="from-blue-500 to-green-500"
        />

        {/* Solutions Grid */}
        <Solutions />

        {/* CTA Section */}
        <CTABanner />
      </main>
    </div>
  );
}
