"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";

export default function Leadership() {
  const leadership = [
    {
      name: "<PERSON>",
      role: "Chief Executive Officer",
      bio: "Former VP of Payments at Stripe, led the launch of Stripe Connect. 15+ years in fintech and payments infrastructure.",
      initial: "S",
    },
    {
      name: "<PERSON>",
      role: "Chief Technology Officer",
      bio: "Ex-Principal Engineer at Square, architected payment processing systems handling $100B+ annually. Expert in distributed systems.",
      initial: "M",
    },
    {
      name: "<PERSON>",
      role: "Chief Revenue Officer",
      bio: "Previously Head of Partnerships at Adyen, built relationships with 500+ ISOs and ISVs. Deep expertise in payment ecosystem.",
      initial: "J",
    },
    {
      name: "<PERSON>",
      role: "Chief Risk Officer",
      bio: "Former Director of Risk at PayPal, developed ML-based underwriting models. 20+ years in payment risk and compliance.",
      initial: "D",
    },
  ];

  return (
    <section className="w-full py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Leadership</h2>
          <p className="text-lg text-muted-foreground">
            Our team combines deep payments expertise with modern technology
            leadership.
          </p>
        </motion.div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {leadership.map((leader, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg">
                <CardContent className="p-6 text-center">
                  <div className="size-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {leader.initial}
                  </div>
                  <h3 className="font-semibold text-lg mb-1">{leader.name}</h3>
                  <p className="text-sm text-primary mb-4">{leader.role}</p>
                  <p className="text-sm text-muted-foreground">{leader.bio}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
