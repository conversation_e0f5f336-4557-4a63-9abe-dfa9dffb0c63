
> ngnair-frontend@0.1.0 build
> next build

   ▲ Next.js 14.1.4

   Creating an optimized production build ...
(node:33802) [DEP_WEBPACK_MODULE_UPDATE_HASH] DeprecationWarning: Module.updateHash: Use new ChunkGraph API
(Use `node --trace-deprecation ...` to show where the warning was created)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (265kiB) impacts deserialization performance (consider using <PERSON>uffer instead and decode when needed)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (266kiB) impacts deserialization performance (consider using <PERSON>uffer instead and decode when needed)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (267kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
Failed to compile.

./public/css/style.css
Module not found: Can't resolve '../img/home-six/funfacts-style-bg.jpg'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./public/css/style.css

./public/css/style.css
Module not found: Can't resolve '../img/home-six/subscribe-wrap-bg.jpg'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./public/css/style.css

./public/css/style.css
HookWebpackError: Cannot find module '../img/home-six/funfacts-style-bg.jpg'
    at tryRunOrWebpackError (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:311563)
    at __webpack_require_module__ (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:131195)
    at __nested_webpack_require_153754__ (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:130634)
    at /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:131487
    at symbolIterator (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/neo-async/async.js:1:14444)
    at done (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/neo-async/async.js:1:14824)
    at Hook.eval [as callAsync] (eval at create (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:13:28867), <anonymous>:15:1)
    at /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:130354
    at symbolIterator (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/neo-async/async.js:1:14402)
    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)
-- inner error --
Error: Cannot find module '../img/home-six/funfacts-style-bg.jpg'
    at webpackMissingModule (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!/home/<USER>/web1/web1/Ngnair_marketing_site/public/css/style.css:22:50)
    at Module.<anonymous> (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!/home/<USER>/web1/web1/Ngnair_marketing_site/public/css/style.css:22:162)
    at /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:915116
    at Hook.eval [as call] (eval at create (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:13:28645), <anonymous>:7:1)
    at /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:131228
    at tryRunOrWebpackError (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:311517)
    at __webpack_require_module__ (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:131195)
    at __nested_webpack_require_153754__ (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:130634)
    at /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/webpack/bundle5.js:28:131487
    at symbolIterator (/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/compiled/neo-async/async.js:1:14444)

Generated code for /home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!/home/<USER>/web1/web1/Ngnair_marketing_site/public/css/style.css
  1 | __webpack_require__.r(__webpack_exports__);
  2 | /* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js");
  3 | /* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);
  4 | /* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("/home/<USER>/web1/web1/Ngnair_marketing_site/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js");
  5 | /* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__);
  6 | /* harmony import */ var _img_main_banner1_jpg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/main-banner1.jpg");
  7 | /* harmony import */ var _img_main_banner2_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/main-banner2.jpg");
  8 | /* harmony import */ var _img_shape_bg_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/shape-bg.png");
  9 | /* harmony import */ var _img_banner_image_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/banner-image/banner-bg.jpg");
 10 | /* harmony import */ var _img_main_banner8_jpg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/main-banner8.jpg");
 11 | /* harmony import */ var _img_bg_lines_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/bg_lines.svg");
 12 | /* harmony import */ var _img_page_title_bg1_jpg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/page-title-bg1.jpg");
 13 | /* harmony import */ var _img_page_title_bg2_jpg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/page-title-bg2.jpg");
 14 | /* harmony import */ var _img_page_title_shape_jpg__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/page-title-shape.jpg");
 15 | /* harmony import */ var _img_blog_image_1_jpg__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/blog-image/1.jpg");
 16 | /* harmony import */ var _img_blog_image_2_jpg__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/blog-image/2.jpg");
 17 | /* harmony import */ var _img_blog_image_3_jpg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/blog-image/3.jpg");
 18 | /* harmony import */ var _img_login_bg_jpg__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/login-bg.jpg");
 19 | /* harmony import */ var _img_signup_bg_jpg__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/signup-bg.jpg");
 20 | /* harmony import */ var _img_sun_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/sun.png");
 21 | /* harmony import */ var _img_night_mode_png__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/night-mode.png");
 22 | Object(function webpackMissingModule() { var e = new Error("Cannot find module '../img/home-six/funfacts-style-bg.jpg'"); e.code = 'MODULE_NOT_FOUND'; throw e; }());
 23 | Object(function webpackMissingModule() { var e = new Error("Cannot find module '../img/home-six/subscribe-wrap-bg.jpg'"); e.code = 'MODULE_NOT_FOUND'; throw e; }());
 24 | /* harmony import */ var _img_currency_transfer_provider_banner_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/banner/banner-bg.jpg");
 25 | /* harmony import */ var _img_currency_transfer_provider_banner_circle_png__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/banner/circle.png");
 26 | /* harmony import */ var _img_currency_transfer_provider_choose_jpg__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/choose.jpg");
 27 | /* harmony import */ var _img_currency_transfer_provider_key_features_jpg__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/key-features.jpg");
 28 | /* harmony import */ var _img_currency_transfer_provider_working_people_working_people_jpg__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/working-people/working-people.jpg");
 29 | /* harmony import */ var _img_currency_transfer_provider_app_app_bg_jpg__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/app/app-bg.jpg");
 30 | /* harmony import */ var _img_currency_transfer_provider_arrow_circle_up_svg__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/arrow-circle-up.svg");
 31 | /* harmony import */ var _img_currency_transfer_provider_arrow_circle_down_svg__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/arrow-circle-down.svg");
 32 | /* harmony import */ var _img_currency_transfer_provider_page_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/page-banner-bg.jpg");
 33 | /* harmony import */ var _img_currency_transfer_provider_about_jpg__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/about.jpg");
 34 | /* harmony import */ var _img_currency_transfer_provider_send_money_online_bg_jpg__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/send-money-online-bg.jpg");
 35 | /* harmony import */ var _img_currency_transfer_provider_currency_jpg__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/currency.jpg");
 36 | /* harmony import */ var _img_currency_transfer_provider_world_jpg__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__("asset/resource|/home/<USER>/web1/web1/Ngnair_marketing_site/public/img/currency-transfer-provider/world.jpg");
 37 | // Imports
 38 | 
 39 | 
 40 | 
 41 | 
 42 | 
 43 | 
 44 | 
 45 | 
 46 | 
 47 | 
 48 | 
 49 | 
 50 | 
 51 | 
 52 | 
 53 | 
 54 | 
 55 | 
 56 | 
 57 | 
 58 | 
 59 | 
 60 | 
 61 | 
 62 | 
 63 | 
 64 | 
 65 | 
 66 | 
 67 | 
 68 | 
 69 | 
 70 | 
 71 | var ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(false);
 72 | var ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_main_banner1_jpg__WEBPACK_IMPORTED_MODULE_2__);
 73 | var ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_main_banner2_jpg__WEBPACK_IMPORTED_MODULE_3__);
 74 | var ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_shape_bg_png__WEBPACK_IMPORTED_MODULE_4__);
 75 | var ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_banner_image_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_5__);
 76 | var ___CSS_LOADER_URL_REPLACEMENT_4___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_main_banner8_jpg__WEBPACK_IMPORTED_MODULE_6__);
 77 | var ___CSS_LOADER_URL_REPLACEMENT_5___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_bg_lines_svg__WEBPACK_IMPORTED_MODULE_7__);
 78 | var ___CSS_LOADER_URL_REPLACEMENT_6___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_page_title_bg1_jpg__WEBPACK_IMPORTED_MODULE_8__);
 79 | var ___CSS_LOADER_URL_REPLACEMENT_7___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_page_title_bg2_jpg__WEBPACK_IMPORTED_MODULE_9__);
 80 | var ___CSS_LOADER_URL_REPLACEMENT_8___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_page_title_shape_jpg__WEBPACK_IMPORTED_MODULE_10__);
 81 | var ___CSS_LOADER_URL_REPLACEMENT_9___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_blog_image_1_jpg__WEBPACK_IMPORTED_MODULE_11__);
 82 | var ___CSS_LOADER_URL_REPLACEMENT_10___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_blog_image_2_jpg__WEBPACK_IMPORTED_MODULE_12__);
 83 | var ___CSS_LOADER_URL_REPLACEMENT_11___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_blog_image_3_jpg__WEBPACK_IMPORTED_MODULE_13__);
 84 | var ___CSS_LOADER_URL_REPLACEMENT_12___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_login_bg_jpg__WEBPACK_IMPORTED_MODULE_14__);
 85 | var ___CSS_LOADER_URL_REPLACEMENT_13___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_signup_bg_jpg__WEBPACK_IMPORTED_MODULE_15__);
 86 | var ___CSS_LOADER_URL_REPLACEMENT_14___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_sun_png__WEBPACK_IMPORTED_MODULE_16__);
 87 | var ___CSS_LOADER_URL_REPLACEMENT_15___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_night_mode_png__WEBPACK_IMPORTED_MODULE_17__);
 88 | var ___CSS_LOADER_URL_REPLACEMENT_16___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(Object(function webpackMissingModule() { var e = new Error("Cannot find module '../img/home-six/funfacts-style-bg.jpg'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));
 89 | var ___CSS_LOADER_URL_REPLACEMENT_17___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(Object(function webpackMissingModule() { var e = new Error("Cannot find module '../img/home-six/subscribe-wrap-bg.jpg'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));
 90 | var ___CSS_LOADER_URL_REPLACEMENT_18___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_banner_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_19__);
 91 | var ___CSS_LOADER_URL_REPLACEMENT_19___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_banner_circle_png__WEBPACK_IMPORTED_MODULE_20__);
 92 | var ___CSS_LOADER_URL_REPLACEMENT_20___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_choose_jpg__WEBPACK_IMPORTED_MODULE_21__);
 93 | var ___CSS_LOADER_URL_REPLACEMENT_21___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_key_features_jpg__WEBPACK_IMPORTED_MODULE_22__);
 94 | var ___CSS_LOADER_URL_REPLACEMENT_22___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_working_people_working_people_jpg__WEBPACK_IMPORTED_MODULE_23__);
 95 | var ___CSS_LOADER_URL_REPLACEMENT_23___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_app_app_bg_jpg__WEBPACK_IMPORTED_MODULE_24__);
 96 | var ___CSS_LOADER_URL_REPLACEMENT_24___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_arrow_circle_up_svg__WEBPACK_IMPORTED_MODULE_25__);
 97 | var ___CSS_LOADER_URL_REPLACEMENT_25___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_arrow_circle_down_svg__WEBPACK_IMPORTED_MODULE_26__);
 98 | var ___CSS_LOADER_URL_REPLACEMENT_26___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_page_banner_bg_jpg__WEBPACK_IMPORTED_MODULE_27__);
 99 | var ___CSS_LOADER_URL_REPLACEMENT_27___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_about_jpg__WEBPACK_IMPORTED_MODULE_28__);
100 | var ___CSS_LOADER_URL_REPLACEMENT_28___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_send_money_online_bg_jpg__WEBPACK_IMPORTED_MODULE_29__);
101 | var ___CSS_LOADER_URL_REPLACEMENT_29___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_currency_jpg__WEBPACK_IMPORTED_MODULE_30__);
102 | var ___CSS_LOADER_URL_REPLACEMENT_30___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_img_currency_transfer_provider_world_jpg__WEBPACK_IMPORTED_MODULE_31__);
103 | // Module
104 | ___CSS_LOADER_EXPORT___.push([module.id, "/*\r\n@File: Luvion Template Styles\r\n\r\n* This file contains the styling for the actual template, this\r\nis the file you need to edit to change the look of the\r\ntemplate.\r\n\r\nThis files table contents are outlined below>>>>>\r\n\r\n*******************************************\r\n*******************************************\r\n\r\n** - Default CSS\r\n** - Preloader Area CSS\r\n** - Navbar Area CSS\r\n** - Main Banner Area CSS\r\n** - About Area CSS\r\n** - Featured Boxes Area CSS\r\n** - Services Area CSS\r\n** - Comparisons Area CSS\r\n** - Features Area CSS\r\n** - How It Works Area CSS\r\n** - Team Area CSS\r\n** - Invoicing Area CSS\r\n** - Information Area CSS\r\n** - Pricing Area CSS\r\n** - FunFacts Area CSS\r\n** - Feedback Area CSS\r\n** - Ready To Talk Area CSS\r\n** - Partner Area CSS\r\n** - App Download Area CSS\r\n** - Account Create Area CSS\r\n** - Blog Area CSS\r\n** - Blog Details Area CSS\r\n** - Page Title Area CSS\r\n** - Pagination Area CSS\r\n** - Widget Sidebar Area CSS\r\n** - 404 Error Area CSS\r\n** - FAQ Area CSS\r\n** - Login Area CSS\r\n** - Signup Area CSS\r\n** - Contact Area CSS\r\n** - Footer Area CSS\r\n** - Go Top CSS\r\n*/\r\n/*================================================\r\nDefault CSS\r\n=================================================*/\r\n/* @import url(\"../../../../css\");\r\n@import url(\"../../../../css-1\"); */\r\nbody {\r\n  padding: 0;\r\n  margin: 0;\r\n  font-family: \"Roboto\", sans-serif;\r\n  font-size: 15px;\r\n}\r\n\r\nimg {\r\n  max-width: 100%;\r\n}\r\n\r\n.d-table {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.d-table-cell {\r\n  vertical-align: middle;\r\n}\r\n\r\na {\r\n  color: #0e314c;\r\n  text-decoration: none;\r\n  transition: 0.5s;\r\n  outline: 0 !important;\r\n}\r\na:hover {\r\n  color: #22418E;\r\n  text-decoration: none;\r\n}\r\n\r\nbutton {\r\n  outline: 0 !important;\r\n}\r\n\r\n.ptb-70 {\r\n  padding-top: 70px;\r\n  padding-bottom: 70px;\r\n}\r\n\r\n.bg-f7fafd {\r\n  background-color: #f7fafd;\r\n}\r\n\r\n.bg-f6f4f8 {\r\n  background-color: #f6f4f8;\r\n}\r\n\r\n.bg-f4fcff {\r\n  background-color: #f4fcff;\r\n}\r\n\r\n.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n\r\np {\r\n  line-height: 1.7;\r\n  margin-bottom: 15px;\r\n  color: #6084a4;\r\n  font-weight: 300;\r\n  font-size: 15px;\r\n}\r\np:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mfp-bg {\r\n  position: fixed !important;\r\n}\r\n\r\n.mfp-wrap {\r\n  position: fixed !important;\r\n}\r\n\r\n.mfp-container {\r\n  position: fixed;\r\n}\r\n\r\n/*btn btn-primary*/\r\n.btn {\r\n  font-weight: 700;\r\n  border: none;\r\n  padding: 14.5px 30px;\r\n  text-transform: uppercase;\r\n  font-size: 14px;\r\n  line-height: initial;\r\n  border-radius: 4px;\r\n  transition: 0.5s;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.btn.disabled, .btn:disabled {\r\n  opacity: 1;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: transparent;\r\n  color: #ffffff;\r\n}\r\n.btn-primary::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 4px;\r\n  transition: 0.5s;\r\n}\r\n.btn-primary::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 4px;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.btn-primary:hover, .btn-primary:focus {\r\n  background-color: transparent !important;\r\n  color: #ffffff !important;\r\n  box-shadow: unset !important;\r\n}\r\n.btn-primary:hover::before, .btn-primary:focus::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.btn-primary:hover::after, .btn-primary:focus::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*section-title*/\r\n.section-title {\r\n  text-align: center;\r\n  max-width: 720px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  margin-bottom: 50px;\r\n  margin-top: -9px;\r\n}\r\n.section-title h2 {\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.section-title .bar {\r\n  height: 5px;\r\n  width: 90px;\r\n  background: #cdf1d8;\r\n  margin: 15px auto 20px;\r\n  position: relative;\r\n  border-radius: 30px;\r\n}\r\n.section-title .bar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: -2.6px;\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50%;\r\n  background: #44ce6f;\r\n  animation-duration: 3s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n  animation-name: MOVE-BG;\r\n}\r\n.section-title p {\r\n  max-width: 500px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/*form-control*/\r\n.form-control {\r\n  height: 50px;\r\n  padding: 0 15px;\r\n  font-size: 15px;\r\n  line-height: 50px;\r\n  color: #0e314c;\r\n  background-color: #ffffff;\r\n  border: 1px solid #eeeeee;\r\n  border-radius: 0;\r\n  transition: 0.5s;\r\n}\r\n.form-control:focus {\r\n  box-shadow: unset !important;\r\n  border-color: #22418E;\r\n}\r\n\r\n/*================================================\r\nPreloader Area CSS\r\n=================================================*/\r\n.preloader {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 99999;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.preloader .loader {\r\n  position: absolute;\r\n  top: 43%;\r\n  left: 0;\r\n  right: 0;\r\n  transform: translateY(-43%);\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n.preloader .box {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  animation: animate 0.5s linear infinite;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  border-radius: 3px;\r\n}\r\n.preloader .shadow {\r\n  width: 100%;\r\n  height: 5px;\r\n  background: #000;\r\n  opacity: 0.1;\r\n  position: absolute;\r\n  top: 59px;\r\n  left: 0;\r\n  border-radius: 50%;\r\n  animation: shadow 0.5s linear infinite;\r\n}\r\n\r\n@keyframes loader {\r\n  0% {\r\n    left: -100px;\r\n  }\r\n  100% {\r\n    left: 110%;\r\n  }\r\n}\r\n@keyframes animate {\r\n  17% {\r\n    border-bottom-right-radius: 3px;\r\n  }\r\n  25% {\r\n    transform: translateY(9px) rotate(22.5deg);\r\n  }\r\n  50% {\r\n    transform: translateY(18px) scale(1, 0.9) rotate(45deg);\r\n    border-bottom-right-radius: 40px;\r\n  }\r\n  75% {\r\n    transform: translateY(9px) rotate(67.5deg);\r\n  }\r\n  100% {\r\n    transform: translateY(0) rotate(90deg);\r\n  }\r\n}\r\n@keyframes shadow {\r\n  50% {\r\n    transform: scale(1.2, 1);\r\n  }\r\n}\r\n/*================================================\r\nNavbar Area CSS\r\n=================================================*/\r\n.luvion-responsive-nav {\r\n  display: none;\r\n}\r\n\r\n.navbar-brand {\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n.navbar-brand img {\r\n  transition: 0.5s;\r\n}\r\n.navbar-brand img:nth-child(2) {\r\n  display: none;\r\n}\r\n\r\n.luvion-nav {\r\n  background-color: transparent;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n  padding-right: 0;\r\n  padding-left: 0;\r\n}\r\n.luvion-nav .navbar {\r\n  padding-right: 0;\r\n  padding-top: 0;\r\n  padding-left: 0;\r\n  padding-bottom: 0;\r\n}\r\n.luvion-nav .navbar ul {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-bottom: 0;\r\n}\r\n.luvion-nav .navbar .navbar-nav {\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-left: auto;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item {\r\n  position: relative;\r\n  padding-top: 10px;\r\n  padding-bottom: 10px;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item a {\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n  color: #e5e1e1;\r\n  text-transform: capitalize;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n  margin-left: 15px;\r\n  margin-right: 15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item a:hover, .luvion-nav .navbar .navbar-nav .nav-item a:focus, .luvion-nav .navbar .navbar-nav .nav-item a.active {\r\n  color: #ffffff;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item a i {\r\n  font-size: 10px;\r\n  margin-left: 1px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item:last-child a {\r\n  margin-right: 0;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item:first-child a {\r\n  margin-left: 0;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item:hover a, .luvion-nav .navbar .navbar-nav .nav-item.active a {\r\n  color: #ffffff;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu {\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);\r\n  background: #ffffff;\r\n  position: absolute;\r\n  border: none;\r\n  top: 80px;\r\n  left: 0;\r\n  width: 270px;\r\n  z-index: 99;\r\n  display: block;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  border-radius: 0;\r\n  transition: all 0.3s ease-in-out;\r\n  padding-top: 20px;\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  padding-bottom: 20px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {\r\n  text-transform: capitalize;\r\n  padding: 8px 15px;\r\n  margin: 0;\r\n  color: #0e314c;\r\n  font-size: 15.5px;\r\n  font-weight: 500;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu {\r\n  left: -270px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu {\r\n  left: 220px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {\r\n  left: -270px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {\r\n  left: -270px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {\r\n  left: -270px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {\r\n  left: -270px;\r\n  top: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: -15px;\r\n}\r\n.luvion-nav .navbar .navbar-nav .nav-item:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  top: 100%;\r\n}\r\n.luvion-nav .navbar .others-options {\r\n  margin-left: 25px;\r\n}\r\n.luvion-nav .navbar .others-options .login-btn {\r\n  color: #ffffff;\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.luvion-nav .navbar .others-options .login-btn i {\r\n  margin-right: 2px;\r\n  color: #eae563;\r\n}\r\n.luvion-nav .navbar .others-options .login-btn:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n.navbar-area {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: auto;\r\n  z-index: 999;\r\n  background-color: transparent;\r\n  transition: 0.5s;\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n}\r\n.navbar-area.is-sticky {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.06);\r\n  background-color: #ffffff !important;\r\n  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;\r\n  transition: 0.5s;\r\n}\r\n.navbar-area.is-sticky .navbar-brand img:nth-child(1) {\r\n  display: none;\r\n}\r\n.navbar-area.is-sticky .navbar-brand img:nth-child(2) {\r\n  display: block;\r\n}\r\n.navbar-area.is-sticky .luvion-nav {\r\n  background-color: #ffffff;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn i {\r\n  color: #22418E;\r\n}\r\n.navbar-area.is-sticky .luvion-nav .navbar .others-options .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .navbar-brand img {\r\n  display: block !important;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn i {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-style-two .luvion-nav .others-options .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n\r\n.luvion-responsive-nav .mean-container a.meanmenu-reveal span {\r\n  top: 8px;\r\n  height: 4px;\r\n  margin-top: -6px;\r\n  position: relative;\r\n}\r\n\r\n@media only screen and (max-width: 991px) {\r\n  .luvion-responsive-nav {\r\n    display: block;\r\n  }\r\n  .luvion-responsive-nav .luvion-responsive-menu {\r\n    position: relative;\r\n  }\r\n  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul {\r\n    font-size: 14px;\r\n  }\r\n  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul li a.active {\r\n    color: #22418E;\r\n  }\r\n  .luvion-responsive-nav .luvion-responsive-menu.mean-container .mean-nav ul li li a {\r\n    font-size: 13.5px;\r\n  }\r\n  .luvion-responsive-nav .luvion-responsive-menu.mean-container .navbar-nav {\r\n    overflow-y: scroll;\r\n    height: 350px;\r\n    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n  .luvion-responsive-nav .mean-container a.meanmenu-reveal {\r\n    color: #ffffff;\r\n  }\r\n  .luvion-responsive-nav .mean-container a.meanmenu-reveal span {\r\n    background: #ffffff;\r\n  }\r\n  .luvion-responsive-nav .logo {\r\n    position: relative;\r\n    width: 50%;\r\n    z-index: 999;\r\n  }\r\n  .luvion-responsive-nav .logo a img:nth-child(2) {\r\n    display: none;\r\n  }\r\n  .luvion-responsive-nav .others-options {\r\n    position: absolute;\r\n    right: 52px;\r\n    top: 3px;\r\n  }\r\n  .luvion-responsive-nav .others-options .login-btn {\r\n    color: #ffffff;\r\n    font-size: 15px;\r\n    font-weight: 400;\r\n    font-family: \"Raleway\", sans-serif;\r\n  }\r\n  .luvion-responsive-nav .others-options .login-btn i {\r\n    margin-right: 2px;\r\n    color: #eae563;\r\n  }\r\n  .luvion-responsive-nav .others-options .login-btn:hover {\r\n    color: #22418E;\r\n  }\r\n  .navbar-area {\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.09);\r\n    padding-top: 15px;\r\n    padding-bottom: 15px;\r\n  }\r\n  .navbar-area.is-sticky {\r\n    border-bottom: none;\r\n    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .mean-container a.meanmenu-reveal {\r\n    color: #0e314c;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .mean-container a.meanmenu-reveal span {\r\n    background: #0e314c;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .logo a img:nth-child(1) {\r\n    display: none;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .logo a img:nth-child(2) {\r\n    display: block;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn {\r\n    color: #0e314c;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn i {\r\n    color: #22418E;\r\n  }\r\n  .navbar-area.is-sticky .luvion-responsive-nav .others-options .login-btn:hover {\r\n    color: #22418E;\r\n  }\r\n\r\n  .luvion-nav {\r\n    display: none;\r\n  }\r\n}\r\n/*================================================\r\nMain Banner Area CSS\r\n=================================================*/\r\n.main-banner {\r\n  height: 100vh;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n.main-banner-content {\r\n  margin-top: -20px;\r\n  max-width: 600px;\r\n}\r\n.main-banner-content h1 {\r\n  margin-bottom: 0;\r\n  color: #ffffff;\r\n  font-size: 52px;\r\n  font-weight: 700;\r\n}\r\n.main-banner-content p {\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 18px;\r\n  max-width: 400px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.main-banner-content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n.main-banner-section {\r\n  height: 800px;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n.banner-content {\r\n  margin-top: -50px;\r\n}\r\n.banner-content h1 {\r\n  margin-bottom: 0;\r\n  color: #ffffff;\r\n  font-size: 50px;\r\n  font-weight: 700;\r\n}\r\n.banner-content p {\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 18px;\r\n  max-width: 400px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.banner-content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n.money-transfer-form {\r\n  background-color: #ffffff;\r\n  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);\r\n  position: relative;\r\n  z-index: 1;\r\n  padding: 30px;\r\n  border-radius: 5px;\r\n  margin-top: -50px;\r\n  margin-left: 100px;\r\n}\r\n.money-transfer-form::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  z-index: -1;\r\n  background: #ffffff;\r\n  width: 96%;\r\n  opacity: 0.62;\r\n  height: 50%;\r\n  bottom: -10px;\r\n  left: 0;\r\n  right: 0;\r\n  margin: auto;\r\n  border-radius: 3px;\r\n}\r\n.money-transfer-form::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  z-index: -1;\r\n  background: #ffffff;\r\n  width: 96%;\r\n  opacity: 0.62;\r\n  height: 50%;\r\n  top: -10px;\r\n  left: 0;\r\n  right: 0;\r\n  margin: auto;\r\n  border-radius: 3px;\r\n}\r\n.money-transfer-form form {\r\n  text-align: center;\r\n}\r\n.money-transfer-form form .form-group {\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  padding: 36px 10px 12px 15px;\r\n  border-radius: 5px;\r\n}\r\n.money-transfer-form form .form-group .amount-currency-select {\r\n  right: 0;\r\n  top: 0;\r\n  position: absolute;\r\n  height: 100%;\r\n}\r\n.money-transfer-form form .form-group .nice-select {\r\n  border: none;\r\n  background-color: #0e314c;\r\n  border-radius: 0 5px 5px 0;\r\n  height: 100%;\r\n  padding: 0 35px 0 25px;\r\n  line-height: 72px;\r\n  font-size: 17px;\r\n  font-weight: 700;\r\n}\r\n.money-transfer-form form .form-group .nice-select:after {\r\n  right: 20px;\r\n  border-color: #ffffff;\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n.money-transfer-form form .form-group .nice-select span {\r\n  color: #ffffff;\r\n}\r\n.money-transfer-form form .form-group .nice-select .list {\r\n  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);\r\n  background-color: #ffffff;\r\n  width: 100%;\r\n  padding-top: 20px;\r\n  padding-right: 10px;\r\n  padding-left: 10px;\r\n  padding-bottom: 20px;\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n}\r\n.money-transfer-form form .form-group .nice-select .list .option {\r\n  line-height: initial;\r\n  min-height: auto;\r\n  text-align: center;\r\n  margin-top: 12px;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n.money-transfer-form form .form-group .nice-select .list .option:hover, .money-transfer-form form .form-group .nice-select .list .option:focus, .money-transfer-form form .form-group .nice-select .list .option.focus, .money-transfer-form form .form-group .nice-select .list .option.selected {\r\n  background-color: transparent;\r\n}\r\n.money-transfer-form form .form-group .nice-select .list .option:hover {\r\n  color: #22418E;\r\n}\r\n.money-transfer-form form .form-group .nice-select .list .option:first-child {\r\n  margin-top: 0;\r\n}\r\n.money-transfer-form form .form-group label {\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 10px;\r\n  color: #f9f9f9;\r\n  margin-bottom: 0;\r\n  display: block;\r\n  font-weight: 300;\r\n  font-size: 13.5px;\r\n}\r\n.money-transfer-form form .form-group .form-control {\r\n  background-color: transparent;\r\n  border: none;\r\n  padding-left: 0;\r\n  height: auto;\r\n  line-height: initial;\r\n  padding-right: 95px;\r\n  color: #ffffff;\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n}\r\n.money-transfer-form form .form-group .form-control::-moz-placeholder {\r\n  color: #ffffff;\r\n}\r\n.money-transfer-form form .form-group .form-control::placeholder {\r\n  color: #ffffff;\r\n}\r\n.money-transfer-form form .currency-info {\r\n  margin-bottom: 25px;\r\n  margin-top: 25px;\r\n  text-align: left;\r\n  position: relative;\r\n  padding-top: 5px;\r\n  padding-bottom: 5px;\r\n  padding-left: 20px;\r\n}\r\n.money-transfer-form form .currency-info .bar {\r\n  height: 100%;\r\n  width: 2px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.money-transfer-form form .currency-info .bar::before {\r\n  width: 6px;\r\n  height: 6px;\r\n  border-radius: 50%;\r\n  content: \"\";\r\n  position: absolute;\r\n  top: -1px;\r\n  left: -2px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.money-transfer-form form .currency-info .bar::after {\r\n  width: 6px;\r\n  height: 6px;\r\n  border-radius: 50%;\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: -2px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.money-transfer-form form .currency-info span {\r\n  display: inline-block;\r\n  color: #6084a4;\r\n  font-size: 14px;\r\n}\r\n.money-transfer-form form .currency-info span strong {\r\n  color: #0e314c;\r\n}\r\n.money-transfer-form form .currency-info span:last-child {\r\n  margin-left: 15px;\r\n}\r\n.money-transfer-form form .money-transfer-info {\r\n  color: #6084a4;\r\n  font-size: 14px;\r\n}\r\n.money-transfer-form form .money-transfer-info strong {\r\n  display: block;\r\n  margin-top: 3px;\r\n  color: #0e314c;\r\n  font-size: 16px;\r\n}\r\n.money-transfer-form form button {\r\n  margin-top: 15px;\r\n}\r\n.money-transfer-form form .btn::before {\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n}\r\n.money-transfer-form form .btn::after {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.money-transfer-form form .terms-info {\r\n  margin-top: 15px;\r\n}\r\n.money-transfer-form form .terms-info p {\r\n  font-size: 14px;\r\n}\r\n.money-transfer-form form .terms-info p a {\r\n  display: inline-block;\r\n  color: #0e314c;\r\n}\r\n.money-transfer-form form .terms-info p a:hover {\r\n  color: #22418E;\r\n}\r\n\r\n.banner-section {\r\n  position: relative;\r\n  z-index: 1;\r\n  background: transparent url(" + ___CSS_LOADER_URL_REPLACEMENT_2___ + ") left bottom no-repeat;\r\n  padding-top: 160px;\r\n}\r\n\r\n.hero-content {\r\n  margin-top: -50px;\r\n}\r\n.hero-content h1 {\r\n  margin-bottom: 0;\r\n  font-size: 50px;\r\n  font-weight: 700;\r\n}\r\n.hero-content p {\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 18px;\r\n  max-width: 400px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.hero-content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n.hero-image {\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.hero-image .main-image img:nth-child(2) {\r\n  position: absolute;\r\n  left: -30px;\r\n  bottom: 60px;\r\n  border-radius: 5px;\r\n}\r\n.hero-image .main-image img:nth-child(3) {\r\n  position: absolute;\r\n  right: -80px;\r\n  top: 110px;\r\n}\r\n.hero-image .main-mobile-image {\r\n  display: none;\r\n}\r\n.hero-image .circle-image {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: -15px;\r\n  margin: 0 auto;\r\n  z-index: -1;\r\n}\r\n.hero-image .video-btn {\r\n  display: inline-block;\r\n  position: absolute;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  border-radius: 30px;\r\n  padding: 10px 25px;\r\n  bottom: 55px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  font-size: 15px;\r\n}\r\n.hero-image .video-btn i {\r\n  margin-right: 2px;\r\n}\r\n.hero-image .video-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  z-index: -1;\r\n  border-radius: 30px;\r\n  transition: 0.5s;\r\n}\r\n.hero-image .video-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 30px;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.hero-image .video-btn:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.hero-image .video-btn:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n\r\n.home-area {\r\n  z-index: 1;\r\n  position: relative;\r\n  padding-top: 200px;\r\n  padding-bottom: 250px;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_3___ + ");\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n.home-area::before {\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  content: \"\";\r\n  z-index: -1;\r\n  opacity: 0.85;\r\n  position: absolute;\r\n  background-color: #3059bc;\r\n}\r\n.home-area:hover .home-slides.owl-theme .owl-nav {\r\n  visibility: visible;\r\n  opacity: 1;\r\n}\r\n\r\n.banner-item {\r\n  position: relative;\r\n}\r\n\r\n.banner-item-content {\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n.banner-item-content h1 {\r\n  margin-bottom: 0;\r\n  color: #ffffff;\r\n  font-size: 60px;\r\n  font-weight: 700;\r\n}\r\n.banner-item-content p {\r\n  color: #ffffff;\r\n  max-width: 500px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 18px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.banner-item-content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n.banner-item-image {\r\n  margin-left: -48px;\r\n}\r\n\r\n.home-slides.owl-theme .owl-nav {\r\n  top: 50%;\r\n  opacity: 0;\r\n  right: -25px;\r\n  margin-top: 0;\r\n  position: absolute;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n  transform: translateY(-50%);\r\n}\r\n.home-slides.owl-theme .owl-nav [class*=owl-] {\r\n  padding: 0;\r\n  width: 50px;\r\n  height: 50px;\r\n  margin: 5px 0;\r\n  display: block;\r\n  font-size: 20px;\r\n  border-radius: 0;\r\n  color: #0e314c;\r\n  transition: 0.5s;\r\n  background-color: #ffffff;\r\n}\r\n.home-slides.owl-theme .owl-nav [class*=owl-]:hover {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n\r\n.owl-item.active .banner-item-content {\r\n  overflow: hidden;\r\n}\r\n.owl-item.active .banner-item-content h1 {\r\n  animation-duration: 1s;\r\n  animation-fill-mode: both;\r\n  animation-name: fadeInLeft;\r\n  animation-delay: 0.3s;\r\n}\r\n.owl-item.active .banner-item-content p {\r\n  animation-duration: 1s;\r\n  animation-fill-mode: both;\r\n  animation-name: fadeInLeft;\r\n  animation-delay: 0.7s;\r\n}\r\n.owl-item.active .banner-item-content .btn {\r\n  animation-duration: 1s;\r\n  animation-fill-mode: both;\r\n  animation-name: fadeInLeft;\r\n  animation-delay: 0.9s;\r\n}\r\n.owl-item.active .banner-item-image {\r\n  overflow: hidden;\r\n}\r\n.owl-item.active .banner-item-image img {\r\n  animation-duration: 1s;\r\n  animation-fill-mode: both;\r\n  animation-name: fadeInUp;\r\n  animation-delay: 0.7s;\r\n}\r\n\r\n/* New Demo */\r\n.banner-slider-eight .main-banner {\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n}\r\n.banner-slider-eight .main-banner.bg-8 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_4___ + ");\r\n}\r\n.banner-slider-eight.owl-carousel.owl-theme .owl-nav {\r\n  margin-top: 0;\r\n  transition: 0.5s;\r\n}\r\n.banner-slider-eight.owl-carousel.owl-theme .owl-nav [class*=owl-] {\r\n  margin: 0;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 25px;\r\n  width: 50px;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n  border: 1px solid #ffffff;\r\n  color: #ffffff;\r\n  transform: translateY(-50%);\r\n  transition: all ease 0.5s;\r\n  font-size: 20px;\r\n  overflow: hidden;\r\n}\r\n.banner-slider-eight.owl-carousel.owl-theme .owl-nav [class*=owl-].owl-next {\r\n  left: auto;\r\n  right: 25px;\r\n}\r\n.banner-slider-eight.owl-carousel.owl-theme .owl-nav [class*=owl-]:hover {\r\n  border: 1px solid #22418E;\r\n  background-color: #ffffff;\r\n  color: #22418E;\r\n}\r\n\r\n.banner-video {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.banner-video video {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: auto;\r\n  height: auto;\r\n  min-height: 100%;\r\n  min-width: 100%;\r\n  z-index: -2;\r\n}\r\n.banner-video .main-banner-content {\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 3;\r\n}\r\n.banner-video .main-banner {\r\n  background-image: none;\r\n}\r\n\r\n/*================================================\r\nAbout Area CSS\r\n=================================================*/\r\n.about-content {\r\n  padding-right: 25px;\r\n}\r\n.about-content span {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n  text-transform: uppercase;\r\n  color: #22418E;\r\n  font-size: 12.5px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 700;\r\n}\r\n.about-content h2 {\r\n  font-size: 40px;\r\n  font-weight: 700;\r\n}\r\n.about-content p {\r\n  line-height: 1.8;\r\n  color: #5d7079;\r\n  margin-bottom: 0;\r\n  margin-top: 12px;\r\n}\r\n\r\n.about-image {\r\n  position: relative;\r\n}\r\n.about-image .video-btn {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translateX(-50%) translateY(-50%);\r\n  width: 60px;\r\n  height: 60px;\r\n  line-height: 61px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  color: #ffffff;\r\n  font-size: 23px;\r\n  z-index: 1;\r\n}\r\n.about-image .video-btn::after {\r\n  z-index: -1;\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 60px;\r\n  height: 60px;\r\n  animation: ripple 1.9s ease-out infinite;\r\n  opacity: 0;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.5);\r\n}\r\n.about-image .video-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  z-index: -1;\r\n  border-radius: 50%;\r\n  transition: 0.5s;\r\n}\r\n.about-image .video-btn:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n\r\n@keyframes ripple {\r\n  0%, 35% {\r\n    transform: scale(0);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.5);\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n    transform: scale(2);\r\n  }\r\n}\r\n/*================================================\r\nFeatured Boxes Area CSS\r\n=================================================*/\r\n.featured-boxes-area {\r\n  position: relative;\r\n  z-index: 1;\r\n  margin-top: -100px;\r\n}\r\n\r\n.featured-boxes-inner {\r\n  background-color: #ffffff;\r\n  border-radius: 5px;\r\n  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n.featured-boxes-inner .col-lg-3 {\r\n  border-right: 1px solid #eeeeee;\r\n}\r\n.featured-boxes-inner .col-lg-3:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.single-featured-box {\r\n  text-align: center;\r\n  position: relative;\r\n  padding: 115px 25px 40px 25px;\r\n  overflow: hidden;\r\n}\r\n.single-featured-box .icon {\r\n  transition: 0.4s;\r\n  color: #22418E;\r\n  position: absolute;\r\n  top: 40px;\r\n  left: 50%;\r\n  transform: translate(-50%, 0);\r\n  margin-top: -12px;\r\n}\r\n.single-featured-box .icon i::before {\r\n  font-size: 55px;\r\n}\r\n.single-featured-box .icon.color-fb7756 {\r\n  color: #fb7756;\r\n}\r\n.single-featured-box .icon.color-facd60 {\r\n  color: #facd60;\r\n}\r\n.single-featured-box .icon.color-1ac0c6 {\r\n  color: #1ac0c6;\r\n}\r\n.single-featured-box h3 {\r\n  transition: 0.4s;\r\n  margin-bottom: 0;\r\n  margin-top: 5px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-featured-box p {\r\n  transition: 0.4s;\r\n  font-size: 14px;\r\n  margin-top: 12px;\r\n  margin-bottom: 0;\r\n}\r\n.single-featured-box .read-more-btn {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  display: inline-block;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.6;\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n}\r\n.single-featured-box .read-more-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 1px;\r\n  width: 0;\r\n  transition: 0.5s;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.single-featured-box .read-more-btn:hover::before {\r\n  width: 100%;\r\n}\r\n.single-featured-box:hover .icon {\r\n  top: 23px;\r\n  animation: iconTop 0.4s ease-in-out;\r\n}\r\n.single-featured-box:hover h3 {\r\n  transform: translateY(-20px);\r\n}\r\n.single-featured-box:hover p {\r\n  transform: translateY(-20px);\r\n}\r\n.single-featured-box:hover .read-more-btn {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  bottom: 23px;\r\n}\r\n\r\n@keyframes iconTop {\r\n  0% {\r\n    transform: translate(-50%, 0);\r\n  }\r\n  25% {\r\n    opacity: 0;\r\n    transform: translate(-50%, -70%);\r\n  }\r\n  50% {\r\n    opacity: 0;\r\n    transform: translate(-50%, -40%);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translate(-50%, 0);\r\n  }\r\n}\r\n/*================================================\r\nServices Area CSS\r\n=================================================*/\r\n.services-area {\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.overview-box {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center !important;\r\n}\r\n.overview-box .overview-content {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n}\r\n.overview-box .overview-content .content {\r\n  max-width: 640px;\r\n  padding-left: 50px;\r\n}\r\n.overview-box .overview-content .content .sub-title {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n  text-transform: uppercase;\r\n  color: #22418E;\r\n  font-size: 12.5px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 700;\r\n}\r\n.overview-box .overview-content .content.left-content {\r\n  margin-left: auto;\r\n  padding-right: 50px;\r\n  padding-left: 0;\r\n}\r\n.overview-box .overview-content .content h2 {\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.overview-box .overview-content .content .bar {\r\n  height: 5px;\r\n  width: 90px;\r\n  background: #cdf1d8;\r\n  margin: 20px 0 25px;\r\n  position: relative;\r\n  border-radius: 30px;\r\n}\r\n.overview-box .overview-content .content .bar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: -2.7px;\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50%;\r\n  background: #44ce6f;\r\n  animation-duration: 3s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n  animation-name: MOVE-BG;\r\n}\r\n.overview-box .overview-content .content .services-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center !important;\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-left: -15px;\r\n  margin-right: -15px;\r\n  margin-bottom: 0;\r\n  margin-top: -5px;\r\n}\r\n.overview-box .overview-content .content .services-list li {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n  padding-top: 15px;\r\n  padding-left: 15px;\r\n  padding-right: 15px;\r\n}\r\n.overview-box .overview-content .content .services-list li span {\r\n  display: block;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);\r\n  color: #6084a4;\r\n  z-index: 1;\r\n  border-radius: 5px;\r\n  transition: 0.5s;\r\n  padding-right: 15px;\r\n  padding-top: 10px;\r\n  padding-bottom: 10px;\r\n  padding-left: 35px;\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n}\r\n.overview-box .overview-content .content .services-list li span i {\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #22418E;\r\n  transition: 0.5s;\r\n}\r\n.overview-box .overview-content .content .services-list li span i::before {\r\n  font-size: 12px;\r\n}\r\n.overview-box .overview-content .content .services-list li span::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  width: 0;\r\n  height: 100%;\r\n  border-radius: 5px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.overview-box .overview-content .content .services-list li span:hover {\r\n  color: #ffffff;\r\n}\r\n.overview-box .overview-content .content .services-list li span:hover i {\r\n  color: #ffffff;\r\n}\r\n.overview-box .overview-content .content .services-list li span:hover::before {\r\n  width: 100%;\r\n}\r\n.overview-box .overview-content .content .features-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center !important;\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-left: -15px;\r\n  margin-right: -15px;\r\n  margin-bottom: 0;\r\n  margin-top: -5px;\r\n}\r\n.overview-box .overview-content .content .features-list li {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n  padding-top: 15px;\r\n  padding-left: 15px;\r\n  padding-right: 15px;\r\n}\r\n.overview-box .overview-content .content .features-list li span {\r\n  display: block;\r\n  color: #6084a4;\r\n  position: relative;\r\n  padding-left: 31px;\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n}\r\n.overview-box .overview-content .content .features-list li span i {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #ffffff;\r\n  transition: 0.5s;\r\n  width: 22px;\r\n  height: 20px;\r\n  text-align: center;\r\n  line-height: 18px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 3px;\r\n}\r\n.overview-box .overview-content .content .features-list li span i::before {\r\n  font-size: 10px;\r\n}\r\n.overview-box .overview-content .content .btn {\r\n  margin-top: 30px;\r\n}\r\n.overview-box .overview-image {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n}\r\n.overview-box .overview-image .image {\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.overview-box .overview-image .image .circle-img {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  z-index: -1;\r\n  right: 0;\r\n  margin: 0 auto;\r\n}\r\n.overview-box .overview-image .image .circle-img img {\r\n  animation-name: rotateMe;\r\n  animation-duration: 35s;\r\n  animation-iteration-count: infinite;\r\n  animation-timing-function: linear;\r\n}\r\n\r\n@keyframes MOVE-BG {\r\n  from {\r\n    transform: translateX(0);\r\n  }\r\n  to {\r\n    transform: translateX(88px);\r\n  }\r\n}\r\n@keyframes rotateMe {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n/*================================================\r\nComparisons Area CSS\r\n=================================================*/\r\n.comparisons-table {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px -10px 30px rgba(0, 0, 0, 0.05);\r\n}\r\n.comparisons-table table {\r\n  margin-bottom: 0;\r\n  text-align: center;\r\n}\r\n.comparisons-table table thead tr {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.comparisons-table table thead th {\r\n  vertical-align: middle;\r\n  border: none;\r\n  color: #ffffff;\r\n  padding: 16px 20px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 19px;\r\n  font-weight: 600;\r\n}\r\n.comparisons-table table thead th:nth-child(1) {\r\n  text-align: left;\r\n  padding-left: 50px;\r\n}\r\n.comparisons-table table tbody td {\r\n  vertical-align: middle;\r\n  color: #0e314c;\r\n  padding: 16px 20px;\r\n  border-color: #eeeeee;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n}\r\n.comparisons-table table tbody td:nth-child(1) {\r\n  text-align: left;\r\n  padding-left: 50px;\r\n}\r\n.comparisons-table table tbody td i {\r\n  width: 20px;\r\n  height: 20px;\r\n  line-height: 17px;\r\n  border-radius: 100%;\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  display: block;\r\n  margin: 0 auto;\r\n}\r\n.comparisons-table table tbody td i::before {\r\n  font-size: 8px;\r\n}\r\n.comparisons-table table tbody tr:nth-child(1) td i, .comparisons-table table tbody tr:nth-child(8) td i {\r\n  background-color: #facd60;\r\n}\r\n.comparisons-table table tbody tr:nth-child(2) td i, .comparisons-table table tbody tr:nth-child(9) td i {\r\n  background-color: #44cd6f;\r\n}\r\n.comparisons-table table tbody tr:nth-child(3) td i, .comparisons-table table tbody tr:nth-child(10) td i {\r\n  background-color: #fd6c28;\r\n}\r\n.comparisons-table table tbody tr:nth-child(5) td i, .comparisons-table table tbody tr:nth-child(12) td i {\r\n  background-color: #1ac0c6;\r\n}\r\n.comparisons-table table tbody tr:nth-child(6) td i, .comparisons-table table tbody tr:nth-child(13) td i {\r\n  background-color: #f45271;\r\n}\r\n.comparisons-table table tbody tr:nth-child(7) td i, .comparisons-table table tbody tr:nth-child(14) td i {\r\n  background-color: #fd6d27;\r\n}\r\n\r\n/*================================================\r\nFeatures Area CSS\r\n=================================================*/\r\n.features-area {\r\n  padding-bottom: 50px;\r\n}\r\n\r\n.single-features-box {\r\n  margin-bottom: 30px;\r\n  background-color: #ffffff;\r\n  border-radius: 5px;\r\n  transition: 0.5s;\r\n  position: relative;\r\n  z-index: 1;\r\n  padding: 30px;\r\n}\r\n.single-features-box .icon {\r\n  width: 65px;\r\n  height: 65px;\r\n  text-align: center;\r\n  line-height: 65px;\r\n  background-color: rgba(231, 70, 69, 0.2);\r\n  border-radius: 50%;\r\n  color: #22418E;\r\n  transition: 0.5s;\r\n  margin-bottom: 18px;\r\n}\r\n.single-features-box .icon i::before {\r\n  font-size: 30px;\r\n}\r\n.single-features-box .icon.bg-f78acb {\r\n  background-color: rgba(247, 138, 203, 0.3);\r\n  color: #f78acb;\r\n}\r\n.single-features-box .icon.bg-cdf1d8 {\r\n  background-color: #cdf1d8;\r\n  color: #44ce6f;\r\n}\r\n.single-features-box .icon.bg-c679e3 {\r\n  color: #c679e3;\r\n  background: #edc3fc;\r\n}\r\n.single-features-box .icon.bg-eb6b3d {\r\n  color: #eb6b3d;\r\n  background: rgba(235, 107, 61, 0.3);\r\n}\r\n.single-features-box h3 {\r\n  margin-bottom: 0;\r\n  transition: 0.5s;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-features-box h3 a {\r\n  display: inline-block;\r\n}\r\n.single-features-box p {\r\n  font-size: 14px;\r\n  transition: 0.5s;\r\n  margin-top: 10px;\r\n  margin-bottom: 0;\r\n}\r\n.single-features-box::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  width: 0;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 5px;\r\n  transition: 0.5s;\r\n}\r\n.single-features-box:hover {\r\n  transform: translateY(-10px);\r\n}\r\n.single-features-box:hover::before {\r\n  width: 100%;\r\n}\r\n.single-features-box:hover .icon {\r\n  transform: rotate(360deg);\r\n  color: #22418E;\r\n  background-color: #ffffff;\r\n}\r\n.single-features-box:hover h3 {\r\n  color: #ffffff;\r\n}\r\n.single-features-box:hover h3 a {\r\n  color: #ffffff;\r\n}\r\n.single-features-box:hover p {\r\n  color: #ffffff;\r\n}\r\n\r\n.features-box-list {\r\n  margin-left: auto;\r\n  max-width: 465px;\r\n}\r\n.features-box-list .col-lg-12:last-child .features-box {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.features-box {\r\n  margin-bottom: 20px;\r\n  background-color: #ffffff;\r\n  transition: 0.5s;\r\n  position: relative;\r\n  z-index: 1;\r\n  padding-top: 35px;\r\n  padding-bottom: 35px;\r\n  padding-right: 25px;\r\n  padding-left: 110px;\r\n}\r\n.features-box .icon {\r\n  width: 65px;\r\n  height: 65px;\r\n  text-align: center;\r\n  line-height: 65px;\r\n  background-color: rgba(231, 70, 69, 0.2);\r\n  border-radius: 50%;\r\n  color: #22418E;\r\n  transition: 0.5s;\r\n  position: absolute;\r\n  left: 25px;\r\n  top: 35px;\r\n}\r\n.features-box .icon i::before {\r\n  font-size: 30px;\r\n}\r\n.features-box .icon.bg-f78acb {\r\n  background-color: rgba(247, 138, 203, 0.3);\r\n  color: #f78acb;\r\n}\r\n.features-box .icon.bg-cdf1d8 {\r\n  background-color: #cdf1d8;\r\n  color: #44ce6f;\r\n}\r\n.features-box .icon.bg-c679e3 {\r\n  color: #c679e3;\r\n  background: #edc3fc;\r\n}\r\n.features-box .icon.bg-eb6b3d {\r\n  color: #eb6b3d;\r\n  background: rgba(235, 107, 61, 0.3);\r\n}\r\n.features-box h3 {\r\n  margin-bottom: 0;\r\n  transition: 0.5s;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.features-box h3 a {\r\n  display: inline-block;\r\n}\r\n.features-box p {\r\n  font-size: 14px;\r\n  transition: 0.5s;\r\n  margin-top: 10px;\r\n  margin-bottom: 0;\r\n}\r\n.features-box::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  width: 2px;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.features-box:hover {\r\n  transform: translateY(-10px);\r\n}\r\n.features-box:hover::before {\r\n  width: 100%;\r\n}\r\n.features-box:hover .icon {\r\n  transform: rotate(360deg);\r\n  color: #22418E;\r\n  background-color: #ffffff;\r\n}\r\n.features-box:hover h3 {\r\n  color: #ffffff;\r\n}\r\n.features-box:hover h3 a {\r\n  color: #ffffff;\r\n}\r\n.features-box:hover p {\r\n  color: #ffffff;\r\n}\r\n\r\n.features-image {\r\n  text-align: center;\r\n}\r\n\r\n/*================================================\r\nHow It Works Area CSS\r\n=================================================*/\r\n.how-it-works-area {\r\n  padding-bottom: 40px;\r\n}\r\n\r\n.single-how-it-works {\r\n  text-align: center;\r\n  background-color: #ffffff;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 7px 20px rgba(0, 0, 0, 0.03);\r\n  padding: 36px;\r\n  border-radius: 5px;\r\n  transition: 0.5s;\r\n}\r\n.single-how-it-works img {\r\n  display: inline-block;\r\n  margin-bottom: 30px;\r\n}\r\n.single-how-it-works h3 {\r\n  margin-bottom: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n}\r\n.single-how-it-works p {\r\n  margin-top: 12px;\r\n  margin-bottom: 0;\r\n}\r\n.single-how-it-works:hover {\r\n  transform: translateY(-10px);\r\n  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/*================================================\r\nTeam Area CSS\r\n=================================================*/\r\n.team-area {\r\n  padding-bottom: 40px;\r\n}\r\n\r\n.single-team-member {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center !important;\r\n  margin-right: -10px;\r\n  margin-left: -10px;\r\n  margin-bottom: 30px;\r\n}\r\n.single-team-member .member-image {\r\n  flex: 0 0 33.333333%;\r\n  max-width: 33.333333%;\r\n  position: relative;\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n.single-team-member .member-image img {\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);\r\n  border-radius: 3px;\r\n}\r\n.single-team-member .member-image .social {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  bottom: 0;\r\n  list-style-type: none;\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n}\r\n.single-team-member .member-image .social li {\r\n  display: inline-block;\r\n}\r\n.single-team-member .member-image .social li a {\r\n  margin: 0 2px;\r\n  display: inline-block;\r\n}\r\n.single-team-member .member-image .social li a i {\r\n  display: inline-block;\r\n  width: 30px;\r\n  height: 30px;\r\n  line-height: 30px;\r\n  border-radius: 50%;\r\n  background-color: #44ce6f;\r\n  color: #ffffff;\r\n  font-size: 13px;\r\n  transition: 0.5s;\r\n}\r\n.single-team-member .member-image .social li a i.fa-facebook-f {\r\n  background: #3b5998;\r\n}\r\n.single-team-member .member-image .social li a i.fa-linkedin-in {\r\n  background: #0077b5;\r\n}\r\n.single-team-member .member-image .social li a i:hover {\r\n  background-color: #44ce6f;\r\n  color: #ffffff;\r\n}\r\n.single-team-member .member-content {\r\n  flex: 0 0 66.666667%;\r\n  max-width: 66.666667%;\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n.single-team-member .member-content h3 {\r\n  margin-bottom: 0;\r\n  font-size: 22px;\r\n  font-weight: 700;\r\n}\r\n.single-team-member .member-content span {\r\n  display: block;\r\n  font-size: 13px;\r\n  color: #22418E;\r\n  margin-top: 8px;\r\n}\r\n.single-team-member .member-content p {\r\n  font-size: 14px;\r\n  margin-bottom: 0;\r\n  margin-top: 10px;\r\n}\r\n.single-team-member:hover .member-image .social {\r\n  bottom: 15px;\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nInvoicing Area CSS\r\n=================================================*/\r\n.invoicing-area {\r\n  overflow: hidden;\r\n}\r\n\r\n.invoicing-content {\r\n  margin-left: auto;\r\n  max-width: 625px;\r\n}\r\n.invoicing-content h2 {\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.invoicing-content .bar {\r\n  height: 5px;\r\n  width: 90px;\r\n  background: #cdf1d8;\r\n  margin: 20px 0 25px;\r\n  position: relative;\r\n  border-radius: 30px;\r\n}\r\n.invoicing-content .bar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: -2.7px;\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50%;\r\n  background: #44ce6f;\r\n  animation-duration: 3s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n  animation-name: MOVE-BG;\r\n}\r\n.invoicing-content p {\r\n  margin-bottom: 0;\r\n}\r\n.invoicing-content .btn {\r\n  margin-top: 20px;\r\n}\r\n\r\n.invoicing-image {\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.invoicing-image .main-image img:nth-child(2) {\r\n  position: absolute;\r\n  left: 40px;\r\n  top: 15px;\r\n}\r\n.invoicing-image .main-image img:nth-child(3) {\r\n  position: absolute;\r\n  left: 40px;\r\n  top: 170px;\r\n}\r\n.invoicing-image .main-image img:nth-child(4) {\r\n  position: absolute;\r\n  right: 40px;\r\n  top: 60px;\r\n}\r\n.invoicing-image .main-mobile-image {\r\n  display: none;\r\n}\r\n.invoicing-image .circle-image img {\r\n  position: absolute;\r\n  z-index: -1;\r\n}\r\n.invoicing-image .circle-image img:nth-child(1) {\r\n  top: -30px;\r\n  left: 50px;\r\n  animation: moveLeftBounce 5s linear infinite;\r\n}\r\n.invoicing-image .circle-image img:nth-child(2) {\r\n  right: 50px;\r\n  bottom: -30px;\r\n  animation: moveBounce 5s linear infinite;\r\n}\r\n\r\n@keyframes moveBounce {\r\n  0% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n  100% {\r\n    transform: translateY(0px);\r\n  }\r\n}\r\n@keyframes moveLeftBounce {\r\n  0% {\r\n    transform: translateX(0px);\r\n  }\r\n  50% {\r\n    transform: translateX(20px);\r\n  }\r\n  100% {\r\n    transform: translateX(0px);\r\n  }\r\n}\r\n/*================================================\r\nInformation Area CSS\r\n=================================================*/\r\n.information-area {\r\n  padding-top: 70px;\r\n}\r\n.information-area .col-lg-3:last-child .single-information-box {\r\n  padding-left: 0;\r\n}\r\n\r\n.single-information-box {\r\n  position: relative;\r\n  padding-left: 55px;\r\n}\r\n.single-information-box .icon {\r\n  width: 42px;\r\n  height: 42px;\r\n  line-height: 42px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.single-information-box .icon i::before {\r\n  font-size: 20px;\r\n}\r\n.single-information-box h3 {\r\n  margin-bottom: 0;\r\n  font-size: 17px;\r\n  font-weight: 700;\r\n}\r\n.single-information-box p {\r\n  line-height: initial;\r\n  font-size: 14px;\r\n  margin-top: 5px;\r\n  margin-bottom: 0;\r\n}\r\n.single-information-box .btn-box .app-store-btn {\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 10px 15px 10px 35px;\r\n  font-size: 11px;\r\n}\r\n.single-information-box .btn-box .app-store-btn i {\r\n  position: absolute;\r\n  left: 8px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.single-information-box .btn-box .app-store-btn i::before {\r\n  font-size: 20px;\r\n}\r\n.single-information-box .btn-box .app-store-btn span {\r\n  display: block;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n.single-information-box .btn-box .app-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.single-information-box .btn-box .app-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.single-information-box .btn-box .app-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.single-information-box .btn-box .app-store-btn:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.single-information-box .btn-box .app-store-btn:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.single-information-box .btn-box .play-store-btn {\r\n  margin-left: 5px;\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 10px 15px 10px 35px;\r\n  font-size: 11px;\r\n}\r\n.single-information-box .btn-box .play-store-btn i {\r\n  position: absolute;\r\n  left: 8px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.single-information-box .btn-box .play-store-btn i::before {\r\n  font-size: 20px;\r\n}\r\n.single-information-box .btn-box .play-store-btn span {\r\n  display: block;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n.single-information-box .btn-box .play-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.single-information-box .btn-box .play-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n}\r\n.single-information-box .btn-box .play-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.single-information-box .btn-box .play-store-btn:hover::after {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.single-information-box .btn-box .play-store-btn:hover::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nPricing Area CSS\r\n=================================================*/\r\n.pricing-area {\r\n  padding-bottom: 40px;\r\n}\r\n\r\n.single-pricing-box {\r\n  background-color: #ffffff;\r\n  margin-bottom: 30px;\r\n  box-shadow: 0 11px 60px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 30px;\r\n  transition: 0.5s;\r\n  border-radius: 5px;\r\n}\r\n.single-pricing-box .pricing-header h3 {\r\n  margin-bottom: 0;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-pricing-box .pricing-header p {\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n  margin-bottom: 0;\r\n}\r\n.single-pricing-box .price {\r\n  font-size: 50px;\r\n  font-weight: 400;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 10px;\r\n}\r\n.single-pricing-box .price span {\r\n  display: inline-block;\r\n  margin-left: -10px;\r\n  font-size: 18px;\r\n  font-weight: 300;\r\n}\r\n.single-pricing-box .buy-btn {\r\n  margin-bottom: 25px;\r\n}\r\n.single-pricing-box .buy-btn .btn {\r\n  padding: 13px 30px;\r\n  font-size: 13px;\r\n}\r\n.single-pricing-box .buy-btn .btn-primary {\r\n  border-radius: 30px;\r\n}\r\n.single-pricing-box .buy-btn .btn-primary::after {\r\n  border-radius: 30px;\r\n}\r\n.single-pricing-box .buy-btn .btn-primary::before {\r\n  background: linear-gradient(to right top, #a3a3a3, #9a9a9a, #909090, #878787, #7e7e7e);\r\n  border-radius: 30px;\r\n}\r\n.single-pricing-box .pricing-features {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.single-pricing-box .pricing-features li {\r\n  margin-bottom: 12px;\r\n  color: #6084a4;\r\n  position: relative;\r\n  padding-left: 19px;\r\n  font-weight: 300;\r\n}\r\n.single-pricing-box .pricing-features li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.single-pricing-box .pricing-features li i {\r\n  color: #22418E;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.single-pricing-box .pricing-features li i::before {\r\n  font-size: 12px;\r\n}\r\n.single-pricing-box:hover .buy-btn .btn-primary::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nFunFacts Area CSS\r\n=================================================*/\r\n.funfacts-area {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.funfacts-area .map-bg {\r\n  position: absolute;\r\n  z-index: -1;\r\n  top: 120px;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n}\r\n.funfacts-area .row {\r\n  padding-left: 100px;\r\n  padding-right: 100px;\r\n}\r\n\r\n.funfact {\r\n  text-align: center;\r\n}\r\n.funfact h3 {\r\n  color: #22418E;\r\n  margin-bottom: 2px;\r\n  font-size: 35px;\r\n  font-weight: 600;\r\n}\r\n.funfact h3 .odometer {\r\n  position: relative;\r\n  top: -2px;\r\n}\r\n.funfact p {\r\n  line-height: initial;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.contact-cta-box {\r\n  margin: 70px auto 0;\r\n  max-width: 600px;\r\n  border: 1px dashed #ebebeb;\r\n  padding: 30px 210px 30px 30px;\r\n  border-radius: 5px;\r\n  position: relative;\r\n}\r\n.contact-cta-box h3 {\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 22px;\r\n}\r\n.contact-cta-box p {\r\n  margin-bottom: 0;\r\n  line-height: initial;\r\n}\r\n.contact-cta-box .btn {\r\n  position: absolute;\r\n  right: 30px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n\r\n/*================================================\r\nFeedback Area CSS\r\n=================================================*/\r\n.feedback-slides {\r\n  position: relative;\r\n  max-width: 750px;\r\n  margin: 0 auto;\r\n}\r\n.feedback-slides .client-feedback {\r\n  position: relative;\r\n}\r\n.feedback-slides .client-feedback .single-feedback {\r\n  background: #ffffff;\r\n  border-radius: 5px;\r\n  margin-bottom: 60px;\r\n  position: relative;\r\n  padding-left: 170px;\r\n  padding-right: 40px;\r\n  padding-top: 40px;\r\n  padding-bottom: 40px;\r\n}\r\n.feedback-slides .client-feedback .single-feedback .client-img {\r\n  position: absolute;\r\n  left: 40px;\r\n  top: 40px;\r\n}\r\n.feedback-slides .client-feedback .single-feedback .client-img img {\r\n  border-radius: 50%;\r\n  border: 2px solid #44ce6f;\r\n  padding: 4px;\r\n}\r\n.feedback-slides .client-feedback .single-feedback h3 {\r\n  margin-bottom: 0;\r\n  font-weight: 600;\r\n  font-size: 18px;\r\n}\r\n.feedback-slides .client-feedback .single-feedback span {\r\n  display: block;\r\n  color: #22418E;\r\n  margin-top: 5px;\r\n  margin-bottom: 12px;\r\n  font-weight: 300;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 14px;\r\n}\r\n.feedback-slides .client-feedback .single-feedback::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: -25px;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: #ffffff;\r\n  right: 0;\r\n  margin: 0 auto;\r\n  transform: rotate(45deg);\r\n}\r\n.feedback-slides .client-thumbnails {\r\n  position: relative;\r\n  margin: 0 85px;\r\n}\r\n.feedback-slides .client-thumbnails .item .img-fill {\r\n  cursor: pointer;\r\n  position: relative;\r\n  text-align: center;\r\n}\r\n.feedback-slides .client-thumbnails .item .img-fill img {\r\n  opacity: 0.4;\r\n  transition: 0.5s;\r\n  display: inline-block;\r\n  position: relative;\r\n  border: 2px solid #44ce6f;\r\n  border-radius: 50%;\r\n  padding: 3px;\r\n  width: 85px;\r\n}\r\n.feedback-slides .client-thumbnails .item:hover .img-fill img, .feedback-slides .client-thumbnails .item.slick-center .img-fill img {\r\n  opacity: 1;\r\n}\r\n.feedback-slides .client-thumbnails .next-arrow, .feedback-slides .client-thumbnails .prev-arrow {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background: transparent;\r\n  color: #5e5e5e;\r\n  border: 1px solid #5e5e5e;\r\n  z-index: 1;\r\n  border-radius: 50%;\r\n  line-height: 40px;\r\n  outline: 0 !important;\r\n  transition: 0.5s;\r\n}\r\n.feedback-slides .client-thumbnails .next-arrow::before, .feedback-slides .client-thumbnails .next-arrow::before, .feedback-slides .client-thumbnails .prev-arrow::before, .feedback-slides .client-thumbnails .prev-arrow::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n  border-radius: 50%;\r\n  z-index: -1;\r\n}\r\n.feedback-slides .client-thumbnails .next-arrow:hover, .feedback-slides .client-thumbnails .prev-arrow:hover {\r\n  color: #ffffff;\r\n  border-color: #22418E;\r\n}\r\n.feedback-slides .client-thumbnails .next-arrow:hover::before, .feedback-slides .client-thumbnails .prev-arrow:hover::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.feedback-slides .client-thumbnails .next-arrow {\r\n  right: -20px;\r\n}\r\n.feedback-slides .client-thumbnails .prev-arrow {\r\n  left: -20px;\r\n}\r\n\r\n/*================================================\r\nReady To Talk Area CSS\r\n=================================================*/\r\n.ready-to-talk {\r\n  text-align: center;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  padding-top: 70px;\r\n  padding-bottom: 145px;\r\n}\r\n\r\n.ready-to-talk-content h3 {\r\n  color: #ffffff;\r\n  text-transform: capitalize;\r\n  margin-bottom: 10px;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.ready-to-talk-content p {\r\n  color: #ffffff;\r\n  margin-bottom: 20px;\r\n}\r\n.ready-to-talk-content .btn-primary::after {\r\n  background: #ffffff;\r\n}\r\n.ready-to-talk-content .btn-primary:hover, .ready-to-talk-content .btn-primary:focus {\r\n  color: #22418E !important;\r\n}\r\n.ready-to-talk-content span a {\r\n  margin-left: 15px;\r\n  color: #ffffff;\r\n  text-decoration: underline;\r\n}\r\n\r\n/*================================================\r\nPartner Area CSS\r\n=================================================*/\r\n.partner-area {\r\n  text-align: center;\r\n  position: relative;\r\n  margin-top: -120px;\r\n  background: transparent;\r\n}\r\n.partner-area h3 {\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n\r\n.partner-inner {\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);\r\n  border-radius: 3px;\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  padding-top: 5px;\r\n  padding-left: 30px;\r\n  padding-right: 30px;\r\n  padding-bottom: 30px;\r\n}\r\n.partner-inner a {\r\n  display: block;\r\n  margin-top: 25px;\r\n  position: relative;\r\n}\r\n.partner-inner a img {\r\n  transition: 0.5s;\r\n}\r\n.partner-inner a img:nth-child(2) {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 0;\r\n  right: 0;\r\n  margin: 0 auto;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.partner-inner a:hover img:nth-child(1) {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.partner-inner a:hover img:nth-child(2) {\r\n  opacity: 1;\r\n  top: 0;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nApp Download Area CSS\r\n=================================================*/\r\n.app-download-area {\r\n  padding-top: 95px;\r\n  padding-bottom: 50px;\r\n}\r\n\r\n.app-image {\r\n  position: relative;\r\n  z-index: 1;\r\n  text-align: left;\r\n}\r\n.app-image .main-image img:nth-child(2) {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 0;\r\n}\r\n.app-image .main-mobile-image {\r\n  display: none;\r\n}\r\n.app-image .circle-img {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-55%);\r\n  z-index: -1;\r\n  right: 0;\r\n  margin: 0 auto;\r\n}\r\n.app-image .circle-img img {\r\n  animation-name: rotateMe;\r\n  animation-duration: 35s;\r\n  animation-iteration-count: infinite;\r\n  animation-timing-function: linear;\r\n}\r\n\r\n.app-download-content {\r\n  padding-left: 20px;\r\n}\r\n.app-download-content h2 {\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.app-download-content .bar {\r\n  height: 5px;\r\n  width: 90px;\r\n  background: #cdf1d8;\r\n  margin: 20px 0 25px;\r\n  position: relative;\r\n  border-radius: 30px;\r\n}\r\n.app-download-content .bar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: -2.7px;\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50%;\r\n  background: #44ce6f;\r\n  animation-duration: 3s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n  animation-name: MOVE-BG;\r\n}\r\n.app-download-content p {\r\n  margin-bottom: 0;\r\n}\r\n.app-download-content .btn-box {\r\n  margin-top: 25px;\r\n}\r\n.app-download-content .btn-box .app-store-btn {\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 12px 25px 12px 60px;\r\n  font-size: 12px;\r\n}\r\n.app-download-content .btn-box .app-store-btn i {\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.app-download-content .btn-box .app-store-btn i::before {\r\n  font-size: 35px;\r\n}\r\n.app-download-content .btn-box .app-store-btn span {\r\n  display: block;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n.app-download-content .btn-box .app-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.app-download-content .btn-box .app-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.app-download-content .btn-box .app-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.app-download-content .btn-box .app-store-btn:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.app-download-content .btn-box .app-store-btn:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.app-download-content .btn-box .play-store-btn {\r\n  margin-left: 12px;\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 12px 25px 12px 60px;\r\n  font-size: 12px;\r\n}\r\n.app-download-content .btn-box .play-store-btn i {\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.app-download-content .btn-box .play-store-btn i::before {\r\n  font-size: 35px;\r\n}\r\n.app-download-content .btn-box .play-store-btn span {\r\n  display: block;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n.app-download-content .btn-box .play-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.app-download-content .btn-box .play-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n}\r\n.app-download-content .btn-box .play-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.app-download-content .btn-box .play-store-btn:hover::after {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.app-download-content .btn-box .play-store-btn:hover::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nAccount Create Area CSS\r\n=================================================*/\r\n.account-create-area {\r\n  position: relative;\r\n  z-index: 1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  padding-top: 100px;\r\n  padding-bottom: 100px;\r\n}\r\n.account-create-area::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  opacity: 0.2;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_5___ + ");\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  background-size: 200%;\r\n}\r\n\r\n.account-create-content {\r\n  text-align: center;\r\n}\r\n.account-create-content h2 {\r\n  color: #ffffff;\r\n  max-width: 530px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.account-create-content p {\r\n  color: #ffffff;\r\n  line-height: initial;\r\n  font-size: 18px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.account-create-content .btn-primary {\r\n  margin-top: 30px;\r\n  padding: 16px 30px;\r\n}\r\n.account-create-content .btn-primary::before {\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n}\r\n.account-create-content .btn-primary::after {\r\n  background: #ffffff;\r\n}\r\n.account-create-content .btn-primary:hover, .account-create-content .btn-primary:focus {\r\n  color: #0e314c !important;\r\n}\r\n\r\n/*================================================\r\nBlog Area CSS\r\n=================================================*/\r\n.blog-area {\r\n  padding-bottom: 40px;\r\n}\r\n.blog-area .pagination-area {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.single-blog-post {\r\n  background: #f7fafd;\r\n  position: relative;\r\n  transition: 0.5s;\r\n  margin-bottom: 30px;\r\n}\r\n.single-blog-post .blog-image {\r\n  position: relative;\r\n}\r\n.single-blog-post .blog-image a {\r\n  display: block;\r\n}\r\n.single-blog-post .blog-image .date {\r\n  position: absolute;\r\n  left: 20px;\r\n  bottom: -20px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  color: #ffffff;\r\n  padding: 10px 16px 8px;\r\n  border-radius: 50px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 13px;\r\n}\r\n.single-blog-post .blog-image .date i {\r\n  margin-right: 2px;\r\n}\r\n.single-blog-post .blog-post-content {\r\n  transition: 0.5s;\r\n  padding-top: 40px;\r\n  padding-left: 20px;\r\n  padding-bottom: 20px;\r\n  padding-right: 20px;\r\n}\r\n.single-blog-post .blog-post-content h3 {\r\n  line-height: 30px;\r\n  margin-bottom: 0;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-blog-post .blog-post-content span {\r\n  display: block;\r\n  color: #0e314c;\r\n  font-size: 14.5px;\r\n  margin-top: 13px;\r\n  margin-bottom: 12px;\r\n}\r\n.single-blog-post .blog-post-content span a {\r\n  color: #6084a4;\r\n}\r\n.single-blog-post .blog-post-content span a:hover {\r\n  color: #22418E;\r\n}\r\n.single-blog-post .blog-post-content .read-more-btn {\r\n  font-size: 14.5px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.single-blog-post .blog-post-content .read-more-btn i {\r\n  font-size: 12px;\r\n  margin-left: 2px;\r\n}\r\n.single-blog-post:hover {\r\n  transform: translateY(-7px);\r\n  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);\r\n  background-color: #ffffff;\r\n}\r\n\r\n/*================================================\r\nBlog Details Area CSS\r\n=================================================*/\r\n.blog-details .article-content {\r\n  margin-top: 30px;\r\n}\r\n.blog-details .article-content .entry-meta {\r\n  margin-bottom: 15px;\r\n}\r\n.blog-details .article-content .entry-meta ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.blog-details .article-content .entry-meta ul li {\r\n  position: relative;\r\n  display: inline-block;\r\n  color: #0e314c;\r\n  margin-right: 21px;\r\n  font-weight: 300;\r\n}\r\n.blog-details .article-content .entry-meta ul li span {\r\n  display: inline-block;\r\n  color: #0e314c;\r\n}\r\n.blog-details .article-content .entry-meta ul li a {\r\n  display: inline-block;\r\n  color: #6084a4;\r\n}\r\n.blog-details .article-content .entry-meta ul li a:hover {\r\n  color: #22418E;\r\n}\r\n.blog-details .article-content .entry-meta ul li i {\r\n  color: #22418E;\r\n  margin-right: 2px;\r\n}\r\n.blog-details .article-content .entry-meta ul li::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 11px;\r\n  right: -15px;\r\n  width: 6px;\r\n  height: 1px;\r\n  background: #22418E;\r\n}\r\n.blog-details .article-content .entry-meta ul li:last-child {\r\n  margin-right: 0;\r\n}\r\n.blog-details .article-content .entry-meta ul li:last-child::before {\r\n  display: none;\r\n}\r\n.blog-details .article-content h3 {\r\n  margin-bottom: 15px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.blog-details .article-content p {\r\n  line-height: 1.8;\r\n}\r\n.blog-details .article-content .wp-block-gallery.columns-3 {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-right: -10px;\r\n  margin-left: -10px;\r\n  margin-bottom: 30px;\r\n  margin-top: 30px;\r\n}\r\n.blog-details .article-content .wp-block-gallery.columns-3 li {\r\n  flex: 0 0 33.3333%;\r\n  max-width: 33.3333%;\r\n  padding-right: 10px;\r\n  padding-left: 10px;\r\n}\r\n.blog-details .article-content .wp-block-gallery.columns-3 li figure {\r\n  margin-bottom: 0;\r\n}\r\n.blog-details .article-content .article-features-list {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-bottom: 15px;\r\n  margin-left: 20px;\r\n}\r\n.blog-details .article-content .article-features-list li {\r\n  margin-bottom: 12px;\r\n  color: #6084a4;\r\n  position: relative;\r\n  padding-left: 15px;\r\n  font-weight: 300;\r\n}\r\n.blog-details .article-content .article-features-list li::before {\r\n  background: #22418E;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 50%;\r\n  position: absolute;\r\n}\r\n.blog-details .article-content .article-features-list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.blog-details .article-footer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding-top: 15px;\r\n  padding-bottom: 15px;\r\n  border-top: 1px solid #eeeeee;\r\n  border-bottom: 1px solid #eeeeee;\r\n  margin-top: 30px;\r\n}\r\n.blog-details .article-footer .article-tags {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n}\r\n.blog-details .article-footer .article-tags span {\r\n  display: inline-block;\r\n  color: #0e314c;\r\n  font-size: 20px;\r\n  margin-right: 5px;\r\n  position: relative;\r\n  top: 2px;\r\n}\r\n.blog-details .article-footer .article-tags a {\r\n  display: inline-block;\r\n  color: #6084a4;\r\n  font-weight: 300;\r\n}\r\n.blog-details .article-footer .article-tags a:hover {\r\n  color: #22418E;\r\n}\r\n.blog-details .article-footer .article-share {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n}\r\n.blog-details .article-footer .article-share .social {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  text-align: right;\r\n  margin-bottom: 0;\r\n}\r\n.blog-details .article-footer .article-share .social li {\r\n  display: inline-block;\r\n}\r\n.blog-details .article-footer .article-share .social li a {\r\n  color: #0e314c;\r\n  background-color: #f7f7f7;\r\n  width: 33px;\r\n  height: 33px;\r\n  line-height: 35px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n  font-size: 13px;\r\n  display: inline-block;\r\n}\r\n.blog-details .article-footer .article-share .social li a:hover {\r\n  color: #ffffff;\r\n  background-color: #22418E;\r\n}\r\n\r\nblockquote, .blockquote {\r\n  overflow: hidden;\r\n  background-color: #fafafa;\r\n  padding: 50px !important;\r\n  position: relative;\r\n  text-align: center;\r\n  z-index: 1;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 20px;\r\n  margin-top: 20px;\r\n}\r\nblockquote p, .blockquote p {\r\n  color: #0e314c;\r\n  line-height: 1.6;\r\n  margin-bottom: 0;\r\n  font-style: italic;\r\n  font-weight: 600;\r\n  font-size: 20px !important;\r\n}\r\nblockquote cite, .blockquote cite {\r\n  display: none;\r\n}\r\nblockquote::before, .blockquote::before {\r\n  color: #efefef;\r\n  content: \"\\f10d\";\r\n  position: absolute;\r\n  left: 50px;\r\n  top: -60px;\r\n  z-index: -1;\r\n  font-family: \"Font Awesome 5 Free\";\r\n  font-size: 140px;\r\n  font-weight: 900;\r\n}\r\n\r\n.comments-area {\r\n  margin-top: 28px;\r\n}\r\n.comments-area .comments-title {\r\n  line-height: initial;\r\n  margin-bottom: 25px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.comments-area ol, .comments-area ul {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style-type: none;\r\n}\r\n.comments-area .comment-list {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.comments-area .children {\r\n  margin-left: 20px;\r\n}\r\n.comments-area .comment-body {\r\n  border-bottom: 1px solid #eeeeee;\r\n  padding-left: 65px;\r\n  color: #0e314c;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 20px;\r\n}\r\n.comments-area .comment-body .reply {\r\n  margin-top: 15px;\r\n}\r\n.comments-area .comment-body .reply a {\r\n  border: 1px solid #eeeeee;\r\n  color: #6084a4;\r\n  display: inline-block;\r\n  padding: 5px 20px;\r\n  border-radius: 30px;\r\n  text-transform: uppercase;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.comments-area .comment-body .reply a:hover {\r\n  color: #ffffff;\r\n  background-color: #22418E;\r\n  border-color: #22418E;\r\n}\r\n.comments-area .comment-meta {\r\n  margin-bottom: 0.8em;\r\n}\r\n.comments-area .comment-author {\r\n  font-size: 16px;\r\n  margin-bottom: 0.4em;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.comments-area .comment-author .avatar {\r\n  height: 50px;\r\n  left: -65px;\r\n  position: absolute;\r\n  width: 50px;\r\n}\r\n.comments-area .comment-author .fn {\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.comments-area .comment-author .says {\r\n  display: none;\r\n}\r\n.comments-area .comment-metadata {\r\n  color: #6084a4;\r\n  letter-spacing: 0.08em;\r\n  text-transform: uppercase;\r\n  font-size: 10px;\r\n  font-weight: 400;\r\n}\r\n.comments-area .comment-metadata a {\r\n  color: #6084a4;\r\n}\r\n.comments-area .comment-metadata a:hover {\r\n  color: #22418E;\r\n}\r\n.comments-area .comment-content p {\r\n  font-size: 14px;\r\n}\r\n.comments-area .comment-respond .comment-reply-title {\r\n  margin-bottom: 0;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.comments-area .comment-respond .comment-reply-title #cancel-comment-reply-link {\r\n  font-size: 15px;\r\n  display: inline-block;\r\n}\r\n.comments-area .comment-respond .comment-form {\r\n  overflow: hidden;\r\n}\r\n.comments-area .comment-respond .comment-notes {\r\n  font-size: 14px;\r\n  margin-bottom: 0;\r\n  margin-top: 8px;\r\n}\r\n.comments-area .comment-respond .comment-notes .required {\r\n  color: red;\r\n}\r\n.comments-area .comment-respond .comment-form-comment {\r\n  margin-top: 15px;\r\n  float: left;\r\n  width: 100%;\r\n}\r\n.comments-area .comment-respond label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  color: #0e314c;\r\n  font-weight: 400;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.comments-area .comment-respond input[type=date], .comments-area .comment-respond input[type=time], .comments-area .comment-respond input[type=datetime-local], .comments-area .comment-respond input[type=week], .comments-area .comment-respond input[type=month], .comments-area .comment-respond input[type=text], .comments-area .comment-respond input[type=email], .comments-area .comment-respond input[type=url], .comments-area .comment-respond input[type=password], .comments-area .comment-respond input[type=search], .comments-area .comment-respond input[type=tel], .comments-area .comment-respond input[type=number], .comments-area .comment-respond textarea {\r\n  display: block;\r\n  width: 100%;\r\n  background-color: #ffffff;\r\n  border: 1px solid #eeeeee;\r\n  padding: 0.625em 0.7375em;\r\n  outline: 0;\r\n  transition: 0.5s;\r\n}\r\n.comments-area .comment-respond input[type=date]:focus, .comments-area .comment-respond input[type=time]:focus, .comments-area .comment-respond input[type=datetime-local]:focus, .comments-area .comment-respond input[type=week]:focus, .comments-area .comment-respond input[type=month]:focus, .comments-area .comment-respond input[type=text]:focus, .comments-area .comment-respond input[type=email]:focus, .comments-area .comment-respond input[type=url]:focus, .comments-area .comment-respond input[type=password]:focus, .comments-area .comment-respond input[type=search]:focus, .comments-area .comment-respond input[type=tel]:focus, .comments-area .comment-respond input[type=number]:focus, .comments-area .comment-respond textarea:focus {\r\n  border-color: #22418E;\r\n}\r\n.comments-area .comment-respond .comment-form-author {\r\n  float: left;\r\n  width: 50%;\r\n  padding-right: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n.comments-area .comment-respond .comment-form-email {\r\n  float: left;\r\n  width: 50%;\r\n  padding-left: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n.comments-area .comment-respond .comment-form-url {\r\n  float: left;\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n.comments-area .comment-respond .comment-form-cookies-consent {\r\n  width: 100%;\r\n  float: left;\r\n  position: relative;\r\n  padding-left: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.comments-area .comment-respond .comment-form-cookies-consent input {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 6px;\r\n}\r\n.comments-area .comment-respond .comment-form-cookies-consent label {\r\n  display: inline-block;\r\n  margin: 0;\r\n  color: #6084a4;\r\n  font-weight: normal;\r\n}\r\n.comments-area .comment-respond .form-submit {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n.comments-area .comment-respond .form-submit input {\r\n  background-color: #22418E;\r\n  border: none;\r\n  color: #ffffff;\r\n  padding: 11px 25px 10px;\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  outline: 0;\r\n  border-radius: 0;\r\n  text-transform: uppercase;\r\n  transition: 0.5s;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 700;\r\n  font-size: 14px;\r\n}\r\n.comments-area .comment-respond .form-submit input:hover, .comments-area .comment-respond .form-submit input:focus {\r\n  color: #ffffff;\r\n  background-color: #44ce6f;\r\n}\r\n\r\n/*================================================\r\nPage Title Area CSS\r\n=================================================*/\r\n.page-title-area {\r\n  position: relative;\r\n  z-index: 1;\r\n  background-color: #0e314c;\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  padding-top: 200px;\r\n  padding-bottom: 120px;\r\n}\r\n.page-title-area::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #000000;\r\n  z-index: -1;\r\n  opacity: 0.6;\r\n}\r\n.page-title-area::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  opacity: 0.04;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_5___ + ");\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  background-size: 200%;\r\n}\r\n.page-title-area.item-bg1 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_6___ + ");\r\n}\r\n.page-title-area.item-bg2 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_7___ + ");\r\n}\r\n\r\n.page-title-content {\r\n  text-align: center;\r\n}\r\n.page-title-content h2 {\r\n  color: #ffffff;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 700;\r\n}\r\n.page-title-content p {\r\n  color: #ffffff;\r\n  max-width: 600px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-top: 10px;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.page-title-section {\r\n  background: transparent url(" + ___CSS_LOADER_URL_REPLACEMENT_8___ + ") right top no-repeat;\r\n  padding-top: 200px;\r\n  padding-bottom: 120px;\r\n}\r\n\r\n.page-title-text {\r\n  position: relative;\r\n}\r\n.page-title-text h2 {\r\n  max-width: 700px;\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 700;\r\n}\r\n.page-title-text p {\r\n  max-width: 600px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-top: 8px;\r\n  margin-bottom: 0;\r\n}\r\n.page-title-text ul {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  font-family: \"Raleway\", sans-serif;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  margin-bottom: 0;\r\n}\r\n.page-title-text ul li {\r\n  display: inline-block;\r\n  color: #22418E;\r\n  margin-right: 10px;\r\n  margin-left: 10px;\r\n  position: relative;\r\n  font-weight: 500;\r\n  font-size: 15px;\r\n}\r\n.page-title-text ul li a {\r\n  color: #0e314c;\r\n}\r\n.page-title-text ul li a:hover {\r\n  color: #44ce6f;\r\n}\r\n.page-title-text ul li::before {\r\n  content: \"\\f054\";\r\n  position: absolute;\r\n  right: -15px;\r\n  top: 5px;\r\n  color: #0e314c;\r\n  font-weight: 900;\r\n  font-family: \"Font Awesome 5 Free\";\r\n  font-size: 10px;\r\n}\r\n.page-title-text ul li:last-child::before {\r\n  display: none;\r\n}\r\n.page-title-text ul li:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n/*================================================\r\nPagination Area CSS\r\n=================================================*/\r\n.pagination-area {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n.pagination-area .page-numbers {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin: 0 3px;\r\n  display: inline-block;\r\n  background: #ffffff;\r\n  line-height: 42px;\r\n  color: #0e314c;\r\n  box-shadow: 0 2px 10px 0 #d8dde6;\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n}\r\n.pagination-area .page-numbers::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n}\r\n.pagination-area .page-numbers.current, .pagination-area .page-numbers:hover, .pagination-area .page-numbers:focus {\r\n  color: #ffffff;\r\n}\r\n.pagination-area .page-numbers.current::before, .pagination-area .page-numbers:hover::before, .pagination-area .page-numbers:focus::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nWidget Sidebar Area CSS\r\n=================================================*/\r\n.widget-area .widget {\r\n  margin-top: 30px;\r\n}\r\n.widget-area .widget:first-child {\r\n  margin-top: 0;\r\n}\r\n.widget-area .widget .widget-title {\r\n  border-bottom: 1px solid #eeeeee;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 20px;\r\n  text-transform: capitalize;\r\n  position: relative;\r\n  font-weight: 600;\r\n  font-size: 21px;\r\n}\r\n.widget-area .widget .widget-title::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 50px;\r\n  height: 1px;\r\n}\r\n.widget-area .widget_search {\r\n  box-shadow: 0px 0px 29px 0px rgba(102, 102, 102, 0.1);\r\n  background-color: #ffffff;\r\n  padding: 15px;\r\n}\r\n.widget-area .widget_search form {\r\n  position: relative;\r\n}\r\n.widget-area .widget_search form label {\r\n  display: block;\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_search form .screen-reader-text {\r\n  display: none;\r\n}\r\n.widget-area .widget_search form .search-field {\r\n  background-color: transparent;\r\n  height: 50px;\r\n  padding: 6px 15px;\r\n  border: 1px solid #eeeeee;\r\n  width: 100%;\r\n  display: block;\r\n  outline: 0;\r\n  transition: 0.5s;\r\n}\r\n.widget-area .widget_search form .search-field:focus {\r\n  border-color: #22418E;\r\n}\r\n.widget-area .widget_search form button {\r\n  position: absolute;\r\n  right: 0;\r\n  outline: 0;\r\n  bottom: 0;\r\n  height: 50px;\r\n  width: 50px;\r\n  z-index: 1;\r\n  border: none;\r\n  color: #ffffff;\r\n  background-color: transparent;\r\n}\r\n.widget-area .widget_search form button::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  top: 0;\r\n  transition: 0.5s;\r\n}\r\n.widget-area .widget_search form button::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  top: 0;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.widget-area .widget_search form button:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.widget-area .widget_search form button:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.widget-area .widget_luvion_posts_thumb {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item {\r\n  overflow: hidden;\r\n  margin-bottom: 15px;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .thumb {\r\n  float: left;\r\n  height: 80px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  width: 80px;\r\n  margin-right: 15px;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage {\r\n  width: 80px;\r\n  height: 80px;\r\n  display: inline-block;\r\n  background-size: cover !important;\r\n  background-repeat: no-repeat;\r\n  background-position: center center !important;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg1 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_9___ + ");\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg2 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_10___ + ");\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .thumb .fullimage.bg3 {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_11___ + ");\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .info {\r\n  overflow: hidden;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .info time {\r\n  display: block;\r\n  color: #6084a4;\r\n  text-transform: uppercase;\r\n  margin-top: 5px;\r\n  margin-bottom: 3px;\r\n  font-size: 11px;\r\n}\r\n.widget-area .widget_luvion_posts_thumb .item .info .title {\r\n  margin-bottom: 0;\r\n  line-height: 1.5;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n.widget-area .widget_recent_entries ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.widget-area .widget_recent_entries ul li {\r\n  position: relative;\r\n  margin-bottom: 12px;\r\n  padding-left: 14px;\r\n  line-height: 1.5;\r\n  font-weight: 500;\r\n  font-size: 14.5px;\r\n}\r\n.widget-area .widget_recent_entries ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_recent_entries ul li::before {\r\n  background: #22418E;\r\n  position: absolute;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 7px;\r\n}\r\n.widget-area .widget_recent_entries ul li .post-date {\r\n  display: block;\r\n  font-size: 13px;\r\n  color: #6084a4;\r\n  margin-top: 4px;\r\n}\r\n.widget-area .widget_recent_comments ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.widget-area .widget_recent_comments ul li {\r\n  position: relative;\r\n  margin-bottom: 12px;\r\n  color: #0e314c;\r\n  padding-left: 14px;\r\n  line-height: 1.5;\r\n  font-weight: 500;\r\n  font-size: 14.5px;\r\n}\r\n.widget-area .widget_recent_comments ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_recent_comments ul li::before {\r\n  background: #22418E;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 7px;\r\n  position: absolute;\r\n}\r\n.widget-area .widget_recent_comments ul li span {\r\n  display: inline-block;\r\n}\r\n.widget-area .widget_recent_comments ul li a {\r\n  display: inline-block;\r\n}\r\n.widget-area .widget_archive ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.widget-area .widget_archive ul li {\r\n  position: relative;\r\n  margin-bottom: 12px;\r\n  padding-left: 14px;\r\n  font-weight: 500;\r\n  font-size: 14.5px;\r\n}\r\n.widget-area .widget_archive ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_archive ul li::before {\r\n  background: #22418E;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 7px;\r\n  position: absolute;\r\n}\r\n.widget-area .widget_categories ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.widget-area .widget_categories ul li {\r\n  position: relative;\r\n  margin-bottom: 12px;\r\n  padding-left: 14px;\r\n  font-weight: 500;\r\n  font-size: 14.5px;\r\n}\r\n.widget-area .widget_categories ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_categories ul li::before {\r\n  background: #22418E;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 7px;\r\n  position: absolute;\r\n}\r\n.widget-area .widget_categories ul li .post-count {\r\n  float: right;\r\n}\r\n.widget-area .widget_meta ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.widget-area .widget_meta ul li {\r\n  position: relative;\r\n  margin-bottom: 12px;\r\n  padding-left: 14px;\r\n  font-weight: 500;\r\n  font-size: 14.5px;\r\n}\r\n.widget-area .widget_meta ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.widget-area .widget_meta ul li::before {\r\n  background: #22418E;\r\n  height: 7px;\r\n  width: 7px;\r\n  content: \"\";\r\n  left: 0;\r\n  top: 7px;\r\n  position: absolute;\r\n}\r\n.widget-area .widget_tag_cloud .widget-title {\r\n  margin-bottom: 12px;\r\n}\r\n.widget-area .tagcloud a {\r\n  display: inline-block;\r\n  padding: 6px 13px;\r\n  border: 1px dashed #eeeeee;\r\n  position: relative;\r\n  font-weight: 600;\r\n  font-size: 13.5px !important;\r\n  margin-top: 8px;\r\n  margin-right: 4px;\r\n}\r\n.widget-area .tagcloud a::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  z-index: -1;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n  transform: scale(0.8);\r\n}\r\n.widget-area .tagcloud a:hover, .widget-area .tagcloud a:focus {\r\n  color: #ffffff;\r\n  border-color: #22418E;\r\n}\r\n.widget-area .tagcloud a:hover::before, .widget-area .tagcloud a:focus::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  transform: scale(1);\r\n}\r\n\r\n/*================================================\r\n404 Error Area CSS\r\n=================================================*/\r\n.error-area {\r\n  height: 100vh;\r\n}\r\n\r\n.error-content {\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  max-width: 700px;\r\n}\r\n.error-content h3 {\r\n  font-size: 40px;\r\n  font-weight: 700;\r\n  margin-top: 35px;\r\n  margin-bottom: 15px;\r\n}\r\n.error-content p {\r\n  max-width: 520px;\r\n  margin: 0 auto 20px;\r\n}\r\n\r\n/*================================================\r\nFAQ Area CSS\r\n=================================================*/\r\n.faq-content h2 {\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.faq-content .bar {\r\n  height: 5px;\r\n  width: 90px;\r\n  background: #cdf1d8;\r\n  margin: 20px 0 25px;\r\n  position: relative;\r\n  border-radius: 30px;\r\n}\r\n.faq-content .bar::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: -2.7px;\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50%;\r\n  background: #44ce6f;\r\n  animation-duration: 3s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n  animation-name: MOVE-BG;\r\n}\r\n.faq-content .faq-image {\r\n  margin-top: 20px;\r\n}\r\n\r\n.faq-accordion .accordion {\r\n  list-style-type: none;\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n}\r\n.faq-accordion .accordion .accordion-item {\r\n  display: block;\r\n  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);\r\n  background: #ffffff;\r\n  margin-bottom: 10px;\r\n}\r\n.faq-accordion .accordion .accordion-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.faq-accordion .accordion .accordion-title {\r\n  padding: 12px 20px 12px 51px;\r\n  color: #0e314c;\r\n  position: relative;\r\n  border-bottom: 1px solid transparent;\r\n  margin-bottom: -1px;\r\n  display: block;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.faq-accordion .accordion .accordion-title i {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 40px;\r\n  text-align: center;\r\n  height: 100%;\r\n  background: #22418E;\r\n  color: #ffffff;\r\n}\r\n.faq-accordion .accordion .accordion-title i::before {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  right: 0;\r\n  transform: translateY(-50%);\r\n  font-size: 13px;\r\n}\r\n.faq-accordion .accordion .accordion-title.active {\r\n  border-bottom-color: #eeeeee;\r\n}\r\n.faq-accordion .accordion .accordion-title.active i::before {\r\n  content: \"\\f068\";\r\n}\r\n.faq-accordion .accordion .accordion-content {\r\n  display: none;\r\n  position: relative;\r\n  padding: 15px;\r\n  font-size: 14.5px;\r\n}\r\n.faq-accordion .accordion .accordion-content.show {\r\n  display: block;\r\n}\r\n\r\n.faq-contact {\r\n  margin-top: 70px;\r\n}\r\n\r\n.faq-contact-form {\r\n  max-width: 750px;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n}\r\n.faq-contact-form form .form-group {\r\n  margin-bottom: 15px;\r\n}\r\n.faq-contact-form form .form-control {\r\n  background-color: #ffffff;\r\n  border: none;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;\r\n  height: 50px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 14.5px;\r\n}\r\n.faq-contact-form form .row {\r\n  margin-left: -7px;\r\n  margin-right: -7px;\r\n}\r\n.faq-contact-form form .row .col-lg-12, .faq-contact-form form .row .col-lg-6 {\r\n  padding-left: 7px;\r\n  padding-right: 7px;\r\n}\r\n.faq-contact-form form textarea.form-control {\r\n  height: auto;\r\n  padding-top: 15px;\r\n  line-height: initial;\r\n}\r\n.faq-contact-form form .btn {\r\n  margin-top: 8px;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);\r\n}\r\n.faq-contact-form form .help-block ul {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  text-align: left;\r\n  margin-top: 5px;\r\n  margin-bottom: 0;\r\n}\r\n.faq-contact-form form .help-block ul li {\r\n  color: red;\r\n  font-weight: 300;\r\n}\r\n.faq-contact-form form #msgSubmit {\r\n  margin-bottom: 0;\r\n  text-align: center !important;\r\n}\r\n.faq-contact-form form #msgSubmit.text-danger, .faq-contact-form form #msgSubmit.text-success {\r\n  margin-top: 20px;\r\n  font-size: 22px;\r\n  font-weight: 300;\r\n}\r\n\r\n/*================================================\r\nLogin Area CSS\r\n=================================================*/\r\n.login-image {\r\n  height: 100%;\r\n  width: 100%;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_12___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n}\r\n.login-image img {\r\n  display: none;\r\n}\r\n\r\n.login-content {\r\n  height: 100vh;\r\n}\r\n.login-content .login-form {\r\n  text-align: center;\r\n  max-width: 500px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.login-content .login-form .logo {\r\n  margin-bottom: 35px;\r\n}\r\n.login-content .login-form .logo a {\r\n  display: inline-block;\r\n}\r\n.login-content .login-form h3 {\r\n  margin-bottom: 0;\r\n  font-size: 30px;\r\n  font-weight: 700;\r\n}\r\n.login-content .login-form p {\r\n  margin-top: 7px;\r\n  margin-bottom: 0;\r\n}\r\n.login-content .login-form form {\r\n  margin-top: 35px;\r\n}\r\n.login-content .login-form form .form-group {\r\n  margin-bottom: 15px;\r\n}\r\n.login-content .login-form form .form-control {\r\n  background-color: #ffffff;\r\n  color: #0e314c;\r\n  border: none;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;\r\n  height: 50px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 14px;\r\n}\r\n.login-content .login-form form .btn {\r\n  display: block;\r\n  width: 100%;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);\r\n}\r\n.login-content .login-form form .forgot-password {\r\n  text-align: right;\r\n  margin-top: 15px;\r\n}\r\n.login-content .login-form form .forgot-password a {\r\n  display: inline-block;\r\n  color: #22418E;\r\n  text-decoration: underline;\r\n}\r\n.login-content .login-form form .connect-with-social {\r\n  margin-top: 15px;\r\n}\r\n.login-content .login-form form .connect-with-social button {\r\n  display: block;\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #22418E;\r\n  background-color: transparent;\r\n  transition: 0.5s;\r\n  padding: 11px 30px;\r\n  border-radius: 2px;\r\n  color: #22418E;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n}\r\n.login-content .login-form form .connect-with-social button i {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  left: 15px;\r\n  font-size: 20px;\r\n}\r\n.login-content .login-form form .connect-with-social button.facebook {\r\n  border-color: #3b5998;\r\n  color: #3b5998;\r\n}\r\n.login-content .login-form form .connect-with-social button.facebook:hover {\r\n  background-color: #3b5998;\r\n  color: #ffffff;\r\n  border-color: #3b5998;\r\n}\r\n.login-content .login-form form .connect-with-social button.google {\r\n  margin-top: 10px;\r\n  border-color: #EA4335;\r\n  color: #EA4335;\r\n}\r\n.login-content .login-form form .connect-with-social button.google:hover {\r\n  background-color: #EA4335;\r\n  color: #ffffff;\r\n  border-color: #EA4335;\r\n}\r\n.login-content .login-form form .connect-with-social button:hover {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-color: #22418E;\r\n}\r\n\r\n/*================================================\r\nSignup Area CSS\r\n=================================================*/\r\n.signup-image {\r\n  height: 100%;\r\n  width: 100%;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_13___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n}\r\n.signup-image img {\r\n  display: none;\r\n}\r\n\r\n.signup-content {\r\n  height: 100vh;\r\n}\r\n.signup-content .signup-form {\r\n  text-align: center;\r\n  max-width: 500px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.signup-content .signup-form .logo {\r\n  margin-bottom: 35px;\r\n}\r\n.signup-content .signup-form .logo a {\r\n  display: inline-block;\r\n}\r\n.signup-content .signup-form h3 {\r\n  margin-bottom: 0;\r\n  font-size: 30px;\r\n  font-weight: 700;\r\n}\r\n.signup-content .signup-form p {\r\n  margin-top: 7px;\r\n  margin-bottom: 0;\r\n}\r\n.signup-content .signup-form form {\r\n  margin-top: 35px;\r\n}\r\n.signup-content .signup-form form .form-group {\r\n  margin-bottom: 15px;\r\n}\r\n.signup-content .signup-form form .form-control {\r\n  background-color: #ffffff;\r\n  color: #0e314c;\r\n  border: none;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;\r\n  height: 50px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 14px;\r\n}\r\n.signup-content .signup-form form .btn {\r\n  display: block;\r\n  width: 100%;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);\r\n}\r\n.signup-content .signup-form form .connect-with-social {\r\n  margin-top: 20px;\r\n}\r\n.signup-content .signup-form form .connect-with-social span {\r\n  display: block;\r\n  text-transform: uppercase;\r\n  color: #6084a4;\r\n  margin-bottom: 20px;\r\n}\r\n.signup-content .signup-form form .connect-with-social button {\r\n  display: block;\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #22418E;\r\n  background-color: transparent;\r\n  transition: 0.5s;\r\n  padding: 11px 30px;\r\n  border-radius: 2px;\r\n  color: #22418E;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n}\r\n.signup-content .signup-form form .connect-with-social button i {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  left: 15px;\r\n  font-size: 20px;\r\n}\r\n.signup-content .signup-form form .connect-with-social button.facebook {\r\n  border-color: #3b5998;\r\n  color: #3b5998;\r\n}\r\n.signup-content .signup-form form .connect-with-social button.facebook:hover {\r\n  background-color: #3b5998;\r\n  color: #ffffff;\r\n  border-color: #3b5998;\r\n}\r\n.signup-content .signup-form form .connect-with-social button.google {\r\n  margin-top: 10px;\r\n  border-color: #EA4335;\r\n  color: #EA4335;\r\n}\r\n.signup-content .signup-form form .connect-with-social button.google:hover {\r\n  background-color: #EA4335;\r\n  color: #ffffff;\r\n  border-color: #EA4335;\r\n}\r\n.signup-content .signup-form form .connect-with-social button:hover {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-color: #22418E;\r\n}\r\n\r\n/*================================================\r\nContact Area CSS\r\n=================================================*/\r\n.contact-area {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.contact-form form .form-group {\r\n  margin-bottom: 20px;\r\n}\r\n.contact-form form .form-control {\r\n  background-color: #ffffff;\r\n  border: none;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2) !important;\r\n  height: 50px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 14.5px;\r\n}\r\n.contact-form form .row {\r\n  margin-left: -7px;\r\n  margin-right: -7px;\r\n}\r\n.contact-form form .row .col-lg-12, .contact-form form .row .col-lg-6 {\r\n  padding-left: 7px;\r\n  padding-right: 7px;\r\n}\r\n.contact-form form textarea.form-control {\r\n  height: auto;\r\n  padding-top: 15px;\r\n  line-height: initial;\r\n}\r\n.contact-form form .btn {\r\n  margin-top: 8px;\r\n  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);\r\n}\r\n.contact-form form .help-block ul {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-top: 5px;\r\n  margin-bottom: 0;\r\n}\r\n.contact-form form .help-block ul li {\r\n  color: red;\r\n  font-weight: 300;\r\n}\r\n.contact-form form #msgSubmit {\r\n  margin-bottom: 0;\r\n  text-align: left !important;\r\n}\r\n.contact-form form #msgSubmit.text-danger, .contact-form form #msgSubmit.text-success {\r\n  margin-top: 8px;\r\n  font-size: 22px;\r\n  font-weight: 300;\r\n}\r\n\r\n.contact-info {\r\n  padding-right: 25px;\r\n}\r\n.contact-info ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.contact-info ul li {\r\n  position: relative;\r\n  padding-left: 95px;\r\n  color: #6084a4;\r\n  margin-bottom: 35px;\r\n  font-weight: 300;\r\n  line-height: 1.7;\r\n}\r\n.contact-info ul li .icon {\r\n  border: 1px dashed #22418E;\r\n  width: 75px;\r\n  height: 75px;\r\n  line-height: 75px;\r\n  color: #ffffff;\r\n  border-radius: 50%;\r\n  font-size: 25px;\r\n  text-align: center;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  z-index: 1;\r\n  transition: 0.5s;\r\n}\r\n.contact-info ul li .icon::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 50%;\r\n  margin: 5px;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n}\r\n.contact-info ul li .icon::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 50%;\r\n  margin: 5px;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.contact-info ul li span {\r\n  display: block;\r\n  margin-bottom: 3px;\r\n  color: #0e314c;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.contact-info ul li a {\r\n  display: block;\r\n  color: #6084a4;\r\n}\r\n.contact-info ul li a:hover {\r\n  color: #22418E;\r\n}\r\n.contact-info ul li:hover .icon {\r\n  border-color: #44ce6f;\r\n}\r\n.contact-info ul li:hover .icon::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.contact-info ul li:hover .icon::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.contact-info ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.bg-map {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  z-index: -1;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  opacity: 0.5;\r\n}\r\n\r\n/*================================================\r\nFooter Area CSS\r\n=================================================*/\r\n.footer-area {\r\n  position: relative;\r\n  z-index: 1;\r\n  padding-top: 70px;\r\n  background-color: #f7fafd;\r\n}\r\n\r\n.single-footer-widget {\r\n  margin-bottom: 30px;\r\n}\r\n.single-footer-widget .logo a {\r\n  display: block;\r\n}\r\n.single-footer-widget .logo p {\r\n  font-size: 14.5px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.single-footer-widget h3 {\r\n  margin-bottom: 24px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-footer-widget .list {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.single-footer-widget .list li {\r\n  margin-bottom: 10px;\r\n  font-weight: 400;\r\n  font-size: 14.5px;\r\n}\r\n.single-footer-widget .list li a {\r\n  color: #6084a4;\r\n  display: inline-block;\r\n}\r\n.single-footer-widget .list li a:hover {\r\n  padding-left: 5px;\r\n  color: #22418E;\r\n}\r\n.single-footer-widget .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.single-footer-widget .footer-contact-info {\r\n  margin-bottom: 0;\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n}\r\n.single-footer-widget .footer-contact-info li {\r\n  position: relative;\r\n  color: #6084a4;\r\n  margin-bottom: 8px;\r\n  line-height: 1.7;\r\n  font-weight: 400;\r\n  font-size: 14.5px;\r\n}\r\n.single-footer-widget .footer-contact-info li a {\r\n  color: #6084a4;\r\n  display: inline-block;\r\n}\r\n.single-footer-widget .footer-contact-info li a:hover {\r\n  color: #22418E;\r\n}\r\n.single-footer-widget .footer-contact-info li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.single-footer-widget .footer-contact-info li span {\r\n  display: inline-block;\r\n  font-weight: 500;\r\n}\r\n.single-footer-widget .social-links {\r\n  padding-left: 0;\r\n  list-style-type: none;\r\n  margin-top: 13px;\r\n  margin-bottom: 0;\r\n}\r\n.single-footer-widget .social-links li {\r\n  margin-right: 2px;\r\n  display: inline-block;\r\n}\r\n.single-footer-widget .social-links li a {\r\n  width: 30px;\r\n  text-align: center;\r\n  line-height: 29px;\r\n  height: 30px;\r\n  border: 1px solid #ece9e9;\r\n  border-radius: 50%;\r\n  color: #22418E;\r\n  display: inline-block;\r\n  font-size: 14px;\r\n}\r\n.single-footer-widget .social-links li a:hover {\r\n  color: #ffffff;\r\n  border-color: #22418E;\r\n  background-color: #22418E;\r\n}\r\n\r\n.copyright-area {\r\n  border-top: 1px solid #d8ebfd;\r\n  text-align: center;\r\n  margin-top: 40px;\r\n  padding-top: 25px;\r\n  padding-bottom: 25px;\r\n}\r\n.copyright-area p {\r\n  font-size: 14.5px;\r\n}\r\n.copyright-area p a {\r\n  display: inline-block;\r\n  color: #0e314c;\r\n  font-weight: 400;\r\n}\r\n.copyright-area p a:hover {\r\n  color: #22418E;\r\n}\r\n\r\n.map-image {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  margin: 0 auto;\r\n  text-align: center;\r\n  z-index: -1;\r\n  opacity: 0.7;\r\n}\r\n.map-image img {\r\n  max-width: 40%;\r\n}\r\n\r\n/*================================================\r\nGo Top CSS\r\n=================================================*/\r\n.go-top {\r\n  position: fixed;\r\n  cursor: pointer;\r\n  top: 50%;\r\n  right: 15px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  z-index: 4;\r\n  width: 40px;\r\n  text-align: center;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.9s;\r\n  color: #ffffff;\r\n}\r\n.go-top.active {\r\n  top: 98%;\r\n  transform: translateY(-98%);\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.go-top::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n}\r\n.go-top:hover, .go-top:focus {\r\n  color: #ffffff;\r\n}\r\n.go-top:hover::before, .go-top:focus::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nMain Banner Area CSS\r\n=================================================*/\r\n.banner-wrapper {\r\n  position: relative;\r\n  z-index: 1;\r\n  padding-top: 90px;\r\n}\r\n.banner-wrapper::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  z-index: -1;\r\n  background: linear-gradient(151.59deg, #ff2f2f 10.43%, #000460 92.78%), radial-gradient(100% 246.94% at 100% 0, #fff 0, #020063 100%), linear-gradient(143.18deg, #1400ff 0.45%, #3a0000), linear-gradient(167.55deg, #ff002e, #ff003d 74.04%), linear-gradient(351.18deg, #b25bba 7.52%, #1700a7 77.98%), linear-gradient(202.44deg, #a90000 12.99%, #00ffe0 87.21%), linear-gradient(165.77deg, #b7d500 15.35%, #20a 89.57%);\r\n  background-blend-mode: overlay, color-burn, screen, overlay, difference, difference, normal;\r\n  -webkit-clip-path: polygon(0 0, 100% 0, 100% 70%, 0% 100%);\r\n  clip-path: polygon(0 0, 100% 0, 100% 70%, 0% 100%);\r\n}\r\n.banner-wrapper .container-fluid {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n.banner-wrapper .container-fluid .row {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n}\r\n.banner-wrapper .container-fluid .row .col-lg-6 {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n\r\n.banner-wrapper-content {\r\n  max-width: 640px;\r\n  padding-right: 20px;\r\n  margin-left: auto;\r\n  margin-top: -50px;\r\n}\r\n.banner-wrapper-content h1 {\r\n  margin-bottom: 0;\r\n  color: #ffffff;\r\n  font-size: 50px;\r\n  font-weight: 700;\r\n}\r\n.banner-wrapper-content p {\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 18px;\r\n  max-width: 400px;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.banner-wrapper-content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n.banner-wrapper-image {\r\n  text-align: right;\r\n}\r\n\r\n/*================================================\r\nPartner Area CSS\r\n=================================================*/\r\n.single-partner-item {\r\n  text-align: center;\r\n}\r\n.single-partner-item img {\r\n  display: inline-block !important;\r\n  width: auto !important;\r\n}\r\n\r\n/*================================================\r\nPayment Experience Area CSS\r\n=================================================*/\r\n.payment-experience-content h2 {\r\n  margin-bottom: 18px;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.payment-experience-content .link-btn {\r\n  display: inline-block;\r\n  font-weight: 600;\r\n  color: #22418E;\r\n  position: relative;\r\n  margin-top: 5px;\r\n  line-height: 1;\r\n}\r\n.payment-experience-content .link-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.payment-experience-content .link-btn:hover::before {\r\n  width: 0;\r\n}\r\n\r\n.single-payment-experience-box {\r\n  margin-top: 20px;\r\n}\r\n.single-payment-experience-box .icon {\r\n  margin-bottom: 22px;\r\n  width: 90px;\r\n  height: 90px;\r\n  text-align: center;\r\n  line-height: 97px;\r\n  background-color: #f3c7db;\r\n  border-radius: 50%;\r\n  position: relative;\r\n  color: #ffffff;\r\n  transition: 0.5s;\r\n  font-size: 40px;\r\n  padding-right: 5px;\r\n}\r\n.single-payment-experience-box .icon i {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.single-payment-experience-box .icon::before {\r\n  content: \"\";\r\n  transition: 0.5s;\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  background-color: #f281ac;\r\n  border-radius: 50%;\r\n  margin-top: 5px;\r\n  margin-right: 5px;\r\n}\r\n.single-payment-experience-box h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-payment-experience-box:hover .icon {\r\n  background-color: #0e314c;\r\n}\r\n.single-payment-experience-box:hover .icon::before {\r\n  background-color: #22418E;\r\n}\r\n\r\n.col-lg-3:nth-child(2) .single-payment-experience-box .icon {\r\n  background-color: #c1e9c5;\r\n}\r\n.col-lg-3:nth-child(2) .single-payment-experience-box .icon::before {\r\n  background-color: #7dd179;\r\n}\r\n.col-lg-3:nth-child(2) .single-payment-experience-box:hover .icon {\r\n  background-color: #0e314c;\r\n}\r\n.col-lg-3:nth-child(2) .single-payment-experience-box:hover .icon::before {\r\n  background-color: #22418E;\r\n}\r\n.col-lg-3:nth-child(3) .single-payment-experience-box .icon {\r\n  background-color: #bdc7d4;\r\n}\r\n.col-lg-3:nth-child(3) .single-payment-experience-box .icon::before {\r\n  background-color: #73819c;\r\n}\r\n.col-lg-3:nth-child(3) .single-payment-experience-box:hover .icon {\r\n  background-color: #0e314c;\r\n}\r\n.col-lg-3:nth-child(3) .single-payment-experience-box:hover .icon::before {\r\n  background-color: #22418E;\r\n}\r\n.col-lg-3:nth-child(4) .single-payment-experience-box .icon {\r\n  background-color: #bce5e5;\r\n}\r\n.col-lg-3:nth-child(4) .single-payment-experience-box .icon::before {\r\n  background-color: #72c7c2;\r\n}\r\n.col-lg-3:nth-child(4) .single-payment-experience-box:hover .icon {\r\n  background-color: #0e314c;\r\n}\r\n.col-lg-3:nth-child(4) .single-payment-experience-box:hover .icon::before {\r\n  background-color: #22418E;\r\n}\r\n\r\n/*================================================\r\nSupport Area CSS\r\n=================================================*/\r\n.support-image {\r\n  position: relative;\r\n  padding-right: 130px;\r\n  text-align: left;\r\n}\r\n.support-image img:nth-child(2) {\r\n  position: absolute;\r\n  right: 50px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n\r\n.support-content h2 {\r\n  margin-bottom: 20px;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.support-content .btn {\r\n  margin-top: 10px;\r\n}\r\n\r\n/*================================================\r\nBusiness Area CSS\r\n=================================================*/\r\n.business-area {\r\n  background-color: #051947;\r\n}\r\n\r\n.business-content h2 {\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.business-content .single-business-box {\r\n  margin-top: 35px;\r\n}\r\n.business-content .single-business-box h3 {\r\n  color: #ffffff;\r\n  margin-bottom: 12px;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.business-content .single-business-box p {\r\n  color: #ffffff;\r\n  opacity: 0.9;\r\n}\r\n\r\n.business-image {\r\n  margin-left: 50px;\r\n  text-align: center;\r\n  background: linear-gradient(125deg, #FDFF9C 0%, #0500FF 100%), linear-gradient(180deg, #D3D3D3 0%, #161616 100%), linear-gradient(310deg, #00F0FF 0%, #00F0FF 20%, #0017E3 calc(20% + 1px), #0017E3 40%, #000F8F calc(40% + 1px), #000F8F 70%, #00073F calc(70% + 1px), #00073F 100%), linear-gradient(285deg, #FFB6B9 0%, #FFB6B9 35%, #FAE3D9 calc(35% + 1px), #FAE3D9 45%, #BBDED6 calc(45% + 1px), #BBDED6 65%, #61C0BF calc(65% + 1px), #61C0BF 100%);\r\n  background-blend-mode: overlay, overlay, exclusion, normal;\r\n  padding: 50px;\r\n}\r\n\r\n/*================================================\r\nTestimonials Area CSS\r\n=================================================*/\r\n.testimonials-area {\r\n  padding-bottom: 40px;\r\n}\r\n\r\n.single-testimonials-box {\r\n  box-shadow: 5px 5px #8b98b5;\r\n  margin-bottom: 30px;\r\n  padding: 30px;\r\n  transition: 0.5s;\r\n  border: 1px solid #8b98b5;\r\n}\r\n.single-testimonials-box .rating {\r\n  margin-bottom: 15px;\r\n}\r\n.single-testimonials-box .rating i {\r\n  font-size: 15px;\r\n  color: #ffc107;\r\n}\r\n.single-testimonials-box p {\r\n  margin: 0;\r\n  color: #374d7f;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n.single-testimonials-box h3 {\r\n  color: #0e314c;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n  font-size: 15px;\r\n  font-weight: 700;\r\n}\r\n.single-testimonials-box h3 span {\r\n  font-weight: 400;\r\n  color: #6084a4;\r\n  display: inline-block;\r\n  margin-left: 5px;\r\n}\r\n.single-testimonials-box:hover {\r\n  box-shadow: 5px 5px #0e314c;\r\n}\r\n\r\n/*================================================\r\nGlobal Area CSS\r\n=================================================*/\r\n.global-area {\r\n  background-color: #051947;\r\n}\r\n.global-area .section-title h2 {\r\n  color: #ffffff;\r\n}\r\n.global-area .section-title p {\r\n  color: #ffffff;\r\n}\r\n\r\n.global-content {\r\n  padding-right: 30px;\r\n}\r\n.global-content ul {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style-type: none;\r\n}\r\n.global-content ul li {\r\n  margin-bottom: 12px;\r\n  color: #ffffff;\r\n  background-color: rgba(0, 0, 0, 0.12);\r\n  padding: 30px 40px 28px;\r\n  position: relative;\r\n  border-radius: 2px;\r\n  z-index: 1;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n}\r\n.global-content ul li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.global-content ul li::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 0%;\r\n  border-radius: 2px;\r\n  height: 100%;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.global-content ul li:hover::before {\r\n  width: 100%;\r\n}\r\n\r\n/*================================================\r\nSuccess Story Area CSS\r\n=================================================*/\r\n.success-story-inner {\r\n  background-color: #051947;\r\n  position: relative;\r\n  z-index: 1;\r\n  padding-top: 50px;\r\n  padding-bottom: 50px;\r\n  padding-left: 90px;\r\n  padding-right: 135px;\r\n}\r\n.success-story-inner::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 30%;\r\n  z-index: -1;\r\n  background: linear-gradient(125deg, #00FF57 0%, #010033 40%, #460043 70%, #F0FFC5 100%), linear-gradient(55deg, #0014C9 0%, #410060 100%), linear-gradient(300deg, #FFC700 0%, #001AFF 100%), radial-gradient(135% 215% at 115% 40%, #393939 0%, #393939 40%, #849561 calc(40% + 1px), #849561 60%, #EED690 calc(60% + 1px), #EED690 80%, #ECEFD8 calc(80% + 1px), #ECEFD8 100%), linear-gradient(125deg, #282D4F 0%, #282D4F 40%, #23103A calc(40% + 1px), #23103A 70%, #A0204C calc(70% + 1px), #A0204C 88%, #FF6C00 calc(88% + 1px), #FF6C00 100%);\r\n  background-blend-mode: overlay, screen, overlay, overlay, normal;\r\n}\r\n.success-story-inner .owl-theme .owl-nav {\r\n  margin-top: 0;\r\n  position: absolute;\r\n  right: -90px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.success-story-inner .owl-theme .owl-nav [class*=owl-] {\r\n  display: block;\r\n  width: 45px;\r\n  height: 45px;\r\n  line-height: 48px;\r\n  background-color: #ffffff;\r\n  border-radius: 50%;\r\n  margin: 10px 0;\r\n  font-size: 18px;\r\n  transition: 0.5s;\r\n}\r\n.success-story-inner .owl-theme .owl-nav [class*=owl-]:hover {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n\r\n.single-success-story-box .content {\r\n  padding-right: 40px;\r\n}\r\n.single-success-story-box .content p {\r\n  margin: 0;\r\n  color: #ffffff;\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n}\r\n.single-success-story-box .content h3 {\r\n  color: #e8e8e8;\r\n  margin-top: 25px;\r\n  margin-bottom: 0;\r\n  font-size: 15px;\r\n  font-weight: 700;\r\n}\r\n.single-success-story-box .content h3 span {\r\n  font-weight: 300;\r\n  color: #e7e7e7;\r\n  display: inline-block;\r\n  margin-left: 5px;\r\n}\r\n.single-success-story-box .content .btn {\r\n  margin-top: 30px;\r\n}\r\n\r\n/*================================================\r\nPayment Features Area CSS\r\n=================================================*/\r\n.payment-features-area {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.payment-features-area::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  z-index: -1;\r\n  background-color: #f4fcff;\r\n  -webkit-clip-path: polygon(0 63%, 100% 30%, 100% 100%, 0% 100%);\r\n  clip-path: polygon(0 63%, 100% 30%, 100% 100%, 0% 100%);\r\n}\r\n\r\n.payment-features-overview {\r\n  display: flex;\r\n  align-items: center !important;\r\n  flex-wrap: wrap;\r\n  margin-right: -15px;\r\n  margin-left: -15px;\r\n  margin-top: 70px;\r\n}\r\n.payment-features-overview .payment-features-content {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n  padding-left: 15px;\r\n  padding-right: 15px;\r\n}\r\n.payment-features-overview .payment-features-content .content {\r\n  padding-left: 15px;\r\n}\r\n.payment-features-overview .payment-features-content .content h2 {\r\n  margin-bottom: 18px;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n.payment-features-overview .payment-features-content .content .link-btn {\r\n  display: inline-block;\r\n  font-weight: 600;\r\n  color: #22418E;\r\n  position: relative;\r\n  margin-top: 5px;\r\n  line-height: 1;\r\n}\r\n.payment-features-overview .payment-features-content .content .link-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.payment-features-overview .payment-features-content .content .link-btn:hover::before {\r\n  width: 0;\r\n}\r\n.payment-features-overview .payment-features-image {\r\n  flex: 0 0 50%;\r\n  max-width: 50%;\r\n  padding-left: 15px;\r\n  padding-right: 15px;\r\n}\r\n.payment-features-overview .payment-features-image .image {\r\n  margin-right: 15px;\r\n  text-align: center;\r\n  background: linear-gradient(45deg, #000850 0%, #000320 100%), radial-gradient(100% 225% at 100% 0%, #FF6928 0%, #000000 100%), linear-gradient(225deg, #FF7A00 0%, #000000 100%), linear-gradient(135deg, #CDFFEB 10%, #CDFFEB 35%, #009F9D 35%, #009F9D 60%, #07456F 60%, #07456F 67%, #0F0A3C 67%, #0F0A3C 100%);\r\n  background-blend-mode: screen, overlay, hard-light, normal;\r\n  padding: 50px;\r\n}\r\n.payment-features-overview:first-child {\r\n  margin-top: 0;\r\n}\r\n.payment-features-overview:nth-child(2) .payment-features-content .content, .payment-features-overview:nth-child(4) .payment-features-content .content, .payment-features-overview:nth-child(6) .payment-features-content .content {\r\n  padding-left: 0;\r\n  padding-right: 15px;\r\n}\r\n.payment-features-overview:nth-child(2) .payment-features-image .image, .payment-features-overview:nth-child(4) .payment-features-image .image, .payment-features-overview:nth-child(6) .payment-features-image .image {\r\n  background: linear-gradient(123deg, #FFFFFF 0%, #00B2FF 100%), linear-gradient(236deg, #BAFF99 0%, #005E64 100%), linear-gradient(180deg, #FFFFFF 0%, #002A5A 100%), linear-gradient(225deg, #0094FF 20%, #BFF4ED 45%, #280F34 45%, #280F34 70%, #FF004E 70%, #E41655 85%, #B30753 85%, #B30753 100%), linear-gradient(135deg, #0E0220 15%, #0E0220 35%, #E40475 35%, #E40475 60%, #48E0E4 60%, #48E0E4 68%, #D7FBF6 68%, #D7FBF6 100%);\r\n  background-blend-mode: overlay, overlay, overlay, darken, normal;\r\n  margin-right: 0;\r\n  margin-left: 15px;\r\n}\r\n\r\n/*================================================\r\nGet Started Area CSS\r\n=================================================*/\r\n.get-started-area {\r\n  background: linear-gradient(129.96deg, #FF2F2F 10.43%, #000460 92.78%), radial-gradient(100% 246.94% at 100% 0%, #FFFFFF 0%, #020063 100%), linear-gradient(58.72deg, #2200F2 0%, #530000 100%), linear-gradient(154.03deg, #B70000 0%, #FF003D 74.04%), linear-gradient(341.1deg, #FF0000 7.52%, #0038FF 77.98%), linear-gradient(136.23deg, #00C2FF 11.12%, #FF0000 86.47%), radial-gradient(57.37% 100% at 50% 0%, #B50000 0%, #0034BB 100%);\r\n  background-blend-mode: overlay, color-burn, screen, overlay, difference, difference, normal;\r\n}\r\n\r\n.get-started-title h2 {\r\n  color: #ffffff;\r\n  margin-bottom: 30px;\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n}\r\n\r\n.get-started-content p {\r\n  color: #e9fffe;\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n}\r\n\r\n.dark-version {\r\n  top: 20%;\r\n  left: 20px;\r\n  z-index: 99991;\r\n  position: fixed;\r\n  transform: translateY(-20%);\r\n}\r\n.dark-version .switch {\r\n  width: 60px;\r\n  height: 34px;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n.dark-version .switch input {\r\n  width: 0;\r\n  height: 0;\r\n  opacity: 0;\r\n}\r\n.dark-version .slider {\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  cursor: pointer;\r\n  position: absolute;\r\n  transition: 0.5s;\r\n  background: #22418E;\r\n}\r\n.dark-version .slider:before {\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 30px;\r\n  left: 2.5px;\r\n  content: \"\";\r\n  height: 30px;\r\n  margin: auto 0;\r\n  position: absolute;\r\n  transition: 0.5s;\r\n  box-shadow: 0 0px 15px rgba(32, 32, 32, 0.2392156863);\r\n  background: white url(" + ___CSS_LOADER_URL_REPLACEMENT_14___ + ");\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n}\r\n.dark-version input:checked + .slider {\r\n  background-color: #44ce6f;\r\n}\r\n.dark-version input:checked + .slider:before {\r\n  transform: translateX(24px);\r\n  background: white url(" + ___CSS_LOADER_URL_REPLACEMENT_15___ + ");\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n.dark-version .slider.round {\r\n  border-radius: 50px;\r\n}\r\n.dark-version .slider.round:before {\r\n  border-radius: 50%;\r\n}\r\n\r\n.buy-now-btn {\r\n  z-index: 9;\r\n  right: 35px;\r\n  width: 65px;\r\n  height: 65px;\r\n  bottom: 100px;\r\n  position: fixed;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n  background-color: #81b441;\r\n  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;\r\n  animation-name: tada;\r\n  animation-duration: 1s;\r\n  animation-fill-mode: both;\r\n  animation-iteration-count: infinite;\r\n}\r\n.buy-now-btn img {\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  position: absolute;\r\n  transform: translateY(-50%);\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.buy-now-btn::before {\r\n  top: 0;\r\n  left: 0;\r\n  z-index: -1;\r\n  content: \"\";\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  position: absolute;\r\n  animation-delay: 0.2s;\r\n  background-color: #81b441;\r\n  animation: ripple 1.5s ease-out infinite;\r\n}\r\n\r\n@keyframes ripple {\r\n  from {\r\n    opacity: 1;\r\n    transform: scale(0);\r\n  }\r\n  to {\r\n    opacity: 0;\r\n    transform: scale(2);\r\n  }\r\n}\r\n/*new-css \"Home Demo - 6\" */\r\n.ptb-100 {\r\n  padding-top: 100px;\r\n  padding-bottom: 100px;\r\n}\r\n\r\n.pt-100 {\r\n  padding-top: 100px;\r\n}\r\n\r\n.pb-100 {\r\n  padding-bottom: 100px;\r\n}\r\n\r\n.pb-75 {\r\n  padding-bottom: 75px;\r\n}\r\n\r\n.wrap-section-title {\r\n  max-width: 535px;\r\n  margin: 0 0 50px 0;\r\n}\r\n.wrap-section-title span {\r\n  font-size: 16.5px;\r\n  color: #22418E;\r\n  display: inline-block;\r\n  margin-bottom: 18px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n}\r\n.wrap-section-title h2 {\r\n  font-size: 40px;\r\n  margin-bottom: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n/*================================================\r\nNavbar Area CSS\r\n=================================================*/\r\n.navbar-area.navbar-with-position-relative {\r\n  background-color: #ffffff;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a {\r\n  color: #6084a4;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item:hover a, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li.active a {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn {\r\n  color: #6084a4;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn i {\r\n  margin-right: 2px;\r\n  color: #eae563;\r\n}\r\n.navbar-area.navbar-with-position-relative .luvion-nav .navbar .others-options .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n.navbar-area.navbar-with-position-relative.is-sticky .navbar-brand img:nth-child(1) {\r\n  display: block;\r\n}\r\n.navbar-area.navbar-with-position-relative.is-sticky .navbar-brand img:nth-child(2) {\r\n  display: none;\r\n}\r\n\r\n/*================================================\r\nMain Banner Woman Area CSS\r\n=================================================*/\r\n.main-banner-woman-area {\r\n  background-color: #FFFDEF;\r\n  padding-top: 150px;\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n}\r\n.main-banner-woman-area .container {\r\n  position: relative;\r\n}\r\n.main-banner-woman-area .banner-woman-list {\r\n  left: 15px;\r\n  bottom: 70px;\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  position: absolute;\r\n  list-style-type: none;\r\n}\r\n.main-banner-woman-area .banner-woman-list li {\r\n  font-size: 16px;\r\n  color: #5C5C5C;\r\n  font-weight: 400;\r\n  display: inline-block;\r\n  margin-right: 20px;\r\n  position: relative;\r\n  padding-left: 45px;\r\n}\r\n.main-banner-woman-area .banner-woman-list li i {\r\n  display: inline-block;\r\n  height: 35px;\r\n  width: 35px;\r\n  line-height: 35px;\r\n  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);\r\n  background-color: #ffffff;\r\n  color: #DD2476;\r\n  font-size: 16px;\r\n  text-align: center;\r\n  border-radius: 30px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  transition: 0.5s;\r\n}\r\n.main-banner-woman-area .banner-woman-list li:last-child {\r\n  margin-right: 0;\r\n}\r\n.main-banner-woman-area .banner-woman-list li:hover i {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n\r\n.main-banner-woman-content {\r\n  padding-right: 30px;\r\n  position: relative;\r\n  top: -85px;\r\n}\r\n.main-banner-woman-content h1 {\r\n  font-size: 60px;\r\n  font-weight: 600;\r\n  margin-bottom: 0;\r\n}\r\n.main-banner-woman-content p {\r\n  font-size: 20px;\r\n  font-weight: 400;\r\n  color: #0e314c;\r\n  max-width: 390px;\r\n  margin-top: 18px;\r\n  margin-bottom: 0;\r\n}\r\n.main-banner-woman-content .btn-list {\r\n  padding: 0;\r\n  margin-top: 30px;\r\n  margin-bottom: 0;\r\n}\r\n.main-banner-woman-content .btn-list li {\r\n  list-style-type: none;\r\n  display: inline-block;\r\n  margin-right: 20px;\r\n}\r\n.main-banner-woman-content .btn-list li:last-child {\r\n  margin-right: 0;\r\n}\r\n.main-banner-woman-content .btn-list li .discover-more-btn {\r\n  position: relative;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  color: #0e314c;\r\n  border-bottom: 1px solid #0e314c;\r\n  transition: 0.5s;\r\n}\r\n.main-banner-woman-content .btn-list li .discover-more-btn:hover {\r\n  color: #22418E;\r\n  border-bottom: 1px solid #22418E;\r\n}\r\n\r\n.main-banner-woman-image {\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n.main-banner-woman-image img {\r\n  z-index: 9;\r\n  position: relative;\r\n}\r\n.main-banner-woman-image .woman-shape {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  margin: auto;\r\n  bottom: 0;\r\n}\r\n.main-banner-woman-image .woman-shape img {\r\n  z-index: -1;\r\n}\r\n\r\n.banner-woman-shape {\r\n  position: absolute;\r\n  bottom: -20px;\r\n  right: 1.2%;\r\n  transform: translateX(-1.2%);\r\n}\r\n\r\n/*================================================\r\nPartner Area CSS\r\n=================================================*/\r\n.single-partner-card {\r\n  text-align: center;\r\n  margin-bottom: 25px;\r\n}\r\n.single-partner-card a:nth-child(1) {\r\n  display: block;\r\n}\r\n.single-partner-card a:nth-child(2) {\r\n  display: none;\r\n}\r\n\r\n/*================================================\r\nAbout Us Area CSS\r\n=================================================*/\r\n.about-us-area {\r\n  background-color: #222222;\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.about-us-content span {\r\n  font-size: 16.5px;\r\n  color: #22418E;\r\n  display: inline-block;\r\n  margin-bottom: 18px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n}\r\n.about-us-content h3 {\r\n  font-size: 40px;\r\n  margin-bottom: 20px;\r\n  color: #ffffff;\r\n  line-height: 1.4;\r\n}\r\n.about-us-content p {\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.about-us-content .list {\r\n  padding: 0;\r\n  margin-top: 35px;\r\n  margin-bottom: 0;\r\n}\r\n.about-us-content .list li {\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  font-weight: 400;\r\n  display: inline-block;\r\n  margin-bottom: 35px;\r\n  position: relative;\r\n  padding: 3.8px 0 2px 42px;\r\n}\r\n.about-us-content .list li i {\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 31.8px;\r\n  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);\r\n  background-color: #ffffff;\r\n  color: #DD2476;\r\n  font-size: 10px;\r\n  text-align: center;\r\n  border-radius: 30px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  transition: 0.5s;\r\n}\r\n.about-us-content .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.about-us-content .list li:hover i {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n.about-us-content .about-btn {\r\n  margin-top: 30px;\r\n}\r\n.about-us-content .about-btn .sign-up-btn {\r\n  position: relative;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  color: #22418E;\r\n  border-bottom: 1px solid #22418E;\r\n  transition: 0.5s;\r\n}\r\n.about-us-content .about-btn .sign-up-btn:hover {\r\n  color: #ffffff;\r\n  border-bottom: 1px solid #ffffff;\r\n}\r\n\r\n.about-us-right-content {\r\n  padding-left: 35px;\r\n}\r\n.about-us-right-content p {\r\n  font-size: 20px;\r\n  color: #ffffff;\r\n}\r\n.about-us-right-content .information {\r\n  margin-top: 30px;\r\n}\r\n.about-us-right-content .information .title {\r\n  margin-left: 15px;\r\n}\r\n.about-us-right-content .information .title h3 {\r\n  font-size: 20px;\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.about-us-right-content .information .title span {\r\n  font-size: 12.8px;\r\n  display: inline-block;\r\n  margin-top: 12px;\r\n  color: #ffffff;\r\n}\r\n\r\n.about-us-shape {\r\n  position: absolute;\r\n  right: -100px;\r\n  bottom: -100px;\r\n  z-index: -1;\r\n}\r\n\r\n/*================================================\r\nFlexibility Area CSS\r\n=================================================*/\r\n.flexibility-content {\r\n  padding-left: 30px;\r\n  position: relative;\r\n  top: -30px;\r\n}\r\n.flexibility-content span {\r\n  font-size: 16.5px;\r\n  color: #22418E;\r\n  display: inline-block;\r\n  margin-bottom: 18px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n}\r\n.flexibility-content h3 {\r\n  font-size: 40px;\r\n  margin-bottom: 0;\r\n  line-height: 1.4;\r\n}\r\n.flexibility-content .list {\r\n  padding: 0;\r\n  margin-top: 30px;\r\n  margin-bottom: 0;\r\n}\r\n.flexibility-content .list li {\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  display: inline-block;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  background-color: #FFFDEF;\r\n  color: #5C5C5C;\r\n  padding: 20.5px 20px 20px 62px;\r\n  width: 100%;\r\n  border-radius: 5px;\r\n}\r\n.flexibility-content .list li i {\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 31.8px;\r\n  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);\r\n  background-color: #ffffff;\r\n  color: #DD2476;\r\n  font-size: 10px;\r\n  text-align: center;\r\n  border-radius: 30px;\r\n  position: absolute;\r\n  left: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  transition: 0.5s;\r\n}\r\n.flexibility-content .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.flexibility-content .list li:hover i {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n.flexibility-content p {\r\n  margin-top: 25px;\r\n}\r\n\r\n/*================================================\r\nFun Facts Area CSS\r\n=================================================*/\r\n.funfacts-style-area {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.funfacts-style-area::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 50%;\r\n  width: 100%;\r\n  background: #ffffff;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n}\r\n.funfacts-style-area::after {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 50%;\r\n  width: 100%;\r\n  background: #F5F5F5;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: -1;\r\n}\r\n\r\n.funfacts-style-inner-box {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_16___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  border-radius: 20px;\r\n  padding-left: 50px;\r\n  padding-right: 50px;\r\n}\r\n.funfacts-style-inner-box .funfact h3 {\r\n  color: #ffffff;\r\n}\r\n.funfacts-style-inner-box .funfact p {\r\n  color: #ffffff;\r\n}\r\n.funfacts-style-inner-box .contact-cta-box {\r\n  max-width: 800px;\r\n  border: 1px solid rgba(255, 255, 255, 0.29);\r\n}\r\n.funfacts-style-inner-box .contact-cta-box h3 {\r\n  color: #ffffff;\r\n}\r\n.funfacts-style-inner-box .contact-cta-box p {\r\n  color: #ffffff;\r\n}\r\n.funfacts-style-inner-box .contact-cta-box .btn-primary::before {\r\n  background: #0e314c;\r\n}\r\n\r\n/*================================================\r\nSecurity Services Area CSS\r\n=================================================*/\r\n.security-services-area {\r\n  background-color: #F5F5F5;\r\n}\r\n\r\n.security-services-card {\r\n  padding: 30px;\r\n  border-left: 1px solid #22418E;\r\n  margin-bottom: 25px;\r\n  transition: 0.5s;\r\n}\r\n.security-services-card .icon {\r\n  margin-bottom: 30px;\r\n  line-height: 1;\r\n}\r\n.security-services-card .icon i {\r\n  color: #22418E;\r\n}\r\n.security-services-card .icon i::before {\r\n  font-size: 50px;\r\n}\r\n.security-services-card h3 {\r\n  font-size: 22px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n}\r\n.security-services-card p {\r\n  margin-bottom: 18px;\r\n}\r\n.security-services-card .read-more-btn {\r\n  position: relative;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  color: #22418E;\r\n  border-bottom: 1px solid #22418E;\r\n  transition: 0.5s;\r\n}\r\n.security-services-card .read-more-btn:hover {\r\n  color: #0e314c;\r\n  border-bottom: 1px solid #0e314c;\r\n}\r\n.security-services-card:hover {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/*================================================\r\nAwesome Features Area CSS\r\n=================================================*/\r\n.awesome-features-area {\r\n  overflow: hidden;\r\n}\r\n\r\n.awesome-features-content {\r\n  padding-right: 30px;\r\n  position: relative;\r\n  top: -30px;\r\n}\r\n.awesome-features-content span {\r\n  font-size: 16.5px;\r\n  color: #22418E;\r\n  display: inline-block;\r\n  margin-bottom: 18px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n}\r\n.awesome-features-content h3 {\r\n  font-size: 40px;\r\n  margin-bottom: 0;\r\n  line-height: 1.4;\r\n}\r\n.awesome-features-content .list {\r\n  padding: 0;\r\n  margin-top: 30px;\r\n  margin-bottom: 0;\r\n}\r\n.awesome-features-content .list li {\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  display: inline-block;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  background-color: #FFFDEF;\r\n  color: #5C5C5C;\r\n  padding: 20.5px 20px 20px 62px;\r\n  width: 100%;\r\n  border-radius: 5px;\r\n}\r\n.awesome-features-content .list li i {\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 31.8px;\r\n  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.13);\r\n  background-color: #ffffff;\r\n  color: #DD2476;\r\n  font-size: 10px;\r\n  text-align: center;\r\n  border-radius: 30px;\r\n  position: absolute;\r\n  left: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  transition: 0.5s;\r\n}\r\n.awesome-features-content .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.awesome-features-content .list li:hover i {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n.awesome-features-content p {\r\n  margin-top: 25px;\r\n}\r\n\r\n.awesome-features-image {\r\n  position: relative;\r\n  top: 1px;\r\n}\r\n\r\n/*================================================\r\nTestimonials Wrap Area CSS\r\n=================================================*/\r\n.testimonials-wrap-area {\r\n  background-color: #222222;\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.testimonials-card {\r\n  max-width: 750px;\r\n  margin: auto;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n.testimonials-card p {\r\n  font-size: 20px;\r\n  color: #ffffff;\r\n}\r\n.testimonials-card .information {\r\n  margin-top: 30px;\r\n}\r\n.testimonials-card .information img {\r\n  width: auto;\r\n  display: inline-block;\r\n}\r\n.testimonials-card .information .title {\r\n  margin-left: 15px;\r\n  text-align: left;\r\n}\r\n.testimonials-card .information .title h3 {\r\n  font-size: 20px;\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.testimonials-card .information .title span {\r\n  font-size: 12.8px;\r\n  display: inline-block;\r\n  margin-top: 12px;\r\n  color: #ffffff;\r\n}\r\n.testimonials-card .vector-icon-image {\r\n  position: absolute;\r\n  left: -162px;\r\n  top: 0;\r\n}\r\n\r\n.testimonials-wrap-slides.owl-theme .owl-nav {\r\n  margin-top: 0;\r\n}\r\n.testimonials-wrap-slides.owl-theme .owl-nav [class*=owl-] {\r\n  padding: 0;\r\n  width: 50px;\r\n  height: 50px;\r\n  margin: 0;\r\n  font-size: 30px;\r\n  border-radius: 0;\r\n  background-color: transparent;\r\n  color: #ffffff;\r\n  top: 45%;\r\n  transform: translateY(-45%);\r\n  left: 0;\r\n  position: absolute;\r\n  transition: 0.5s;\r\n}\r\n.testimonials-wrap-slides.owl-theme .owl-nav [class*=owl-].owl-next {\r\n  right: 0;\r\n  left: auto;\r\n}\r\n.testimonials-wrap-slides.owl-theme .owl-nav [class*=owl-]:hover {\r\n  color: #22418E;\r\n}\r\n\r\n.testimonials-wrap-shape {\r\n  position: absolute;\r\n  left: -100px;\r\n  bottom: -100px;\r\n  z-index: -1;\r\n}\r\n\r\n/*================================================\r\nBlog Area CSS\r\n=================================================*/\r\n.single-blog-card {\r\n  margin-bottom: 25px;\r\n}\r\n.single-blog-card .image {\r\n  position: relative;\r\n  padding-right: 50px;\r\n  border-radius: 15px;\r\n}\r\n.single-blog-card .image img {\r\n  border-radius: 5px;\r\n}\r\n.single-blog-card .image .date {\r\n  color: #22418E;\r\n  transform: rotate(90deg);\r\n  transform-origin: 0;\r\n  position: absolute;\r\n  padding-left: 60px;\r\n  right: -38%;\r\n  top: 25px;\r\n}\r\n.single-blog-card .image .date::before {\r\n  left: 0;\r\n  top: 50%;\r\n  width: 50px;\r\n  height: 1px;\r\n  content: \"\";\r\n  position: absolute;\r\n  transform: translateY(-50%);\r\n  background-color: #22418E;\r\n}\r\n.single-blog-card .content {\r\n  background-color: #ffffff;\r\n  padding: 25px 25px 0 25px;\r\n  position: relative;\r\n  margin-top: -50px;\r\n  z-index: 1;\r\n  margin-left: 30px;\r\n  border-radius: 5px;\r\n}\r\n.single-blog-card .content h3 {\r\n  font-size: 22px;\r\n  margin-bottom: 12px;\r\n  line-height: 1.5;\r\n}\r\n.single-blog-card .content p {\r\n  margin-bottom: 18px;\r\n}\r\n.single-blog-card .content .read-more-btn {\r\n  position: relative;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  color: #22418E;\r\n  border-bottom: 1px solid #22418E;\r\n  transition: 0.5s;\r\n}\r\n.single-blog-card .content .read-more-btn:hover {\r\n  color: #0e314c;\r\n  border-bottom: 1px solid #0e314c;\r\n}\r\n\r\n/*================================================\r\nSubscribe Wrap Area CSS\r\n=================================================*/\r\n.subscribe-wrap-inner-box {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_17___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  padding: 80px 50px;\r\n  border-radius: 20px;\r\n}\r\n\r\n.subscribe-wrap-box-content {\r\n  padding: 50px;\r\n  border: 1px solid rgba(255, 255, 255, 0.29);\r\n  border-radius: 20px;\r\n}\r\n.subscribe-wrap-box-content .subscribe-content h3 {\r\n  font-size: 34px;\r\n  color: #ffffff;\r\n  margin-bottom: 15px;\r\n}\r\n.subscribe-wrap-box-content .subscribe-content p {\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form {\r\n  position: relative;\r\n  padding-right: 180px;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .input-newsletter {\r\n  display: block;\r\n  width: 100%;\r\n  border: none;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  color: #ffffff;\r\n  height: 50px;\r\n  padding-left: 18px;\r\n  border-radius: 5px;\r\n  outline: 0;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .input-newsletter::-moz-placeholder {\r\n  color: #ffffff;\r\n  -moz-transition: 0.5s;\r\n  transition: 0.5s;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .input-newsletter::placeholder {\r\n  color: #ffffff;\r\n  transition: 0.5s;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .input-newsletter:focus::-moz-placeholder {\r\n  color: transparent;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .input-newsletter:focus::placeholder {\r\n  color: transparent;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .validation-danger {\r\n  color: #ffffff;\r\n  margin-top: 10px;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .validation-success {\r\n  margin-top: 10px;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .btn-primary {\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  border-radius: 5px;\r\n  height: 50px;\r\n}\r\n.subscribe-wrap-box-content .newsletter-form .btn-primary::before {\r\n  background: #0e314c;\r\n}\r\n\r\n/*================================================\r\nFooter Area CSS\r\n=================================================*/\r\n.footer-area.footer-style-wrap {\r\n  background-color: #222222;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .logo a {\r\n  display: block;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .logo p {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .social-links li a {\r\n  background-color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .social-links li a:hover {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget h3 {\r\n  color: #ffffff;\r\n  border-bottom: 1px solid #505050;\r\n  padding-bottom: 18px;\r\n  position: relative;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget h3::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 1px;\r\n  width: 100px;\r\n  background-color: #22418E;\r\n  left: 0;\r\n  bottom: -1px;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .list li {\r\n  position: relative;\r\n  padding-left: 15px;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .list li a {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .list li a:hover {\r\n  color: #22418E;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .list li::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 8px;\r\n  width: 8px;\r\n  background-color: #22418E;\r\n  border-radius: 50px;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li a {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li a:hover {\r\n  color: #22418E;\r\n}\r\n.footer-area.footer-style-wrap .single-footer-widget .footer-contact-info li span {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .copyright-area {\r\n  border-top: 1px solid #3D3D3D;\r\n}\r\n.footer-area.footer-style-wrap .copyright-area p {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .copyright-area p a {\r\n  color: #ffffff;\r\n}\r\n.footer-area.footer-style-wrap .copyright-area p a:hover {\r\n  color: #22418E;\r\n}\r\n\r\n/* Start \"Currency Transfer Provider Demo\" \"CSS\" */\r\n.ptb-100 {\r\n  padding-top: 100px;\r\n  padding-bottom: 100px;\r\n}\r\n\r\n.pt-100 {\r\n  padding-top: 100px;\r\n}\r\n\r\n.pb-100 {\r\n  padding-bottom: 100px;\r\n}\r\n\r\n.pb-75 {\r\n  padding-bottom: 75px;\r\n}\r\n\r\n.currency-transfer-provider-with-background-color {\r\n  background-color: #F4F7F9;\r\n}\r\n\r\n.section-title.ctp-title {\r\n  max-width: 1050px;\r\n  margin-top: -10px;\r\n  margin-bottom: 38px;\r\n}\r\n.section-title.ctp-title h2 {\r\n  color: #212529;\r\n  margin-bottom: 0;\r\n  font-weight: bold;\r\n}\r\n\r\n/*================================================\r\nNavbar Area CSS\r\n=================================================*/\r\n.currency-transfer-provider-navbar {\r\n  background: rgba(0, 0, 0, 0.3);\r\n}\r\n.currency-transfer-provider-navbar.is-sticky {\r\n  background-color: #ffffff !important;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav {\r\n  background-color: transparent;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .navbar-list ul li a {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .navbar-list ul li a:hover {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon i {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon i {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .login-btn {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar.is-sticky .luvion-nav .navbar .others-options .options-item .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .container-fluid {\r\n  padding-left: 35px;\r\n  padding-right: 35px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list {\r\n  padding: 0px 30px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li {\r\n  display: inline-block;\r\n  margin-right: 35px;\r\n  position: relative;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: -20px;\r\n  top: 4px;\r\n  height: 15px;\r\n  width: 2px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li:last-child {\r\n  margin-right: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li:last-child::before {\r\n  display: none;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li a {\r\n  color: #ffffff;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-list ul li a:hover {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a {\r\n  color: #ffffff;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  transition: 0.5s;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a:hover, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a:focus, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item a.active {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item:hover a, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item.active a {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a {\r\n  color: #0e314c;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .currency-transfer-provider-navbar .luvion-nav .navbar .navbar-nav .nav-item .dropdown-menu li a.active {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options {\r\n  margin-left: 30px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item {\r\n  margin-right: 25px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item:last-child {\r\n  margin-right: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .login-btn {\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option {\r\n  padding: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button {\r\n  padding: 0;\r\n  background-color: transparent;\r\n  border: none;\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  position: relative;\r\n  padding-left: 22px;\r\n  padding-right: 20px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .globe-icon i {\r\n  font-size: 15px;\r\n  color: #ffffff;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon {\r\n  position: absolute;\r\n  right: 0;\r\n  top: -2px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button .chevron-down-icon i {\r\n  font-size: 12px;\r\n  color: #ffffff;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option button::after {\r\n  display: none;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu {\r\n  border-radius: 0;\r\n  border: 0;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);\r\n  padding: 0;\r\n  margin-top: 12px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #6084a4;\r\n  font-size: 14px;\r\n  padding: 10px 20px;\r\n  border-bottom: 1px solid #eeeeee;\r\n  border-radius: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:last-child {\r\n  border-bottom: none;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item img {\r\n  width: 25px;\r\n  margin-right: 10px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item.selected {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-radius: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:active {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-radius: 0;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary {\r\n  border-radius: 30px;\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary::before {\r\n  border-radius: 30px;\r\n}\r\n.currency-transfer-provider-navbar .luvion-nav .navbar .others-options .options-item .btn-primary::after {\r\n  border-radius: 30px;\r\n}\r\n\r\n.others-option-for-responsive {\r\n  display: none;\r\n}\r\n.others-option-for-responsive .dot-menu {\r\n  padding: 0 10px;\r\n  height: 30px;\r\n  cursor: pointer;\r\n  z-index: 9991;\r\n  position: absolute;\r\n  right: 52px;\r\n  top: -32px;\r\n  font-weight: 400;\r\n}\r\n.others-option-for-responsive .dot-menu .inner {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 30px;\r\n}\r\n.others-option-for-responsive .dot-menu .inner .circle {\r\n  height: 5px;\r\n  width: 5px;\r\n  border-radius: 100%;\r\n  margin: 0 2px;\r\n  transition: 0.5s;\r\n  background-color: #ffffff;\r\n}\r\n.others-option-for-responsive .dot-menu:hover .inner .circle {\r\n  background-color: #22418E;\r\n}\r\n.others-option-for-responsive .container {\r\n  position: relative;\r\n}\r\n.others-option-for-responsive .container .container {\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 10px;\r\n  max-width: 180px;\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);\r\n  margin-left: auto;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.5s;\r\n  transform: scaleX(0);\r\n  z-index: 2;\r\n  padding: 15px;\r\n  border-radius: 0;\r\n}\r\n.others-option-for-responsive .container .container.active {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  transform: scaleX(1);\r\n}\r\n.others-option-for-responsive .option-inner .others-options {\r\n  margin-left: 0;\r\n  display: block !important;\r\n  text-align: center;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item {\r\n  margin-right: 0;\r\n  margin-bottom: 20px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .login-btn {\r\n  color: #6084a4;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .login-btn:hover {\r\n  color: #22418E;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button {\r\n  padding: 0;\r\n  background-color: transparent;\r\n  border: none;\r\n  color: #6084a4;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  position: relative;\r\n  padding-left: 22px;\r\n  padding-right: 20px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button .globe-icon {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button .globe-icon i {\r\n  font-size: 15px;\r\n  color: #6084a4;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button .chevron-down-icon {\r\n  position: absolute;\r\n  right: 0;\r\n  top: -2px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button .chevron-down-icon i {\r\n  font-size: 12px;\r\n  color: #6084a4;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option button::after {\r\n  display: none;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu {\r\n  border-radius: 0;\r\n  border: 0;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);\r\n  padding: 0;\r\n  margin-top: 12px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #6084a4;\r\n  font-size: 14px;\r\n  padding: 10px 20px;\r\n  border-bottom: 1px solid #eeeeee;\r\n  border-radius: 0;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:last-child {\r\n  border-bottom: none;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item img {\r\n  width: 25px;\r\n  margin-right: 10px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item.selected {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-radius: 0;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .language-option .language-dropdown-menu .dropdown-item:active {\r\n  background-color: #22418E;\r\n  color: #ffffff;\r\n  border-radius: 0;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .btn-primary {\r\n  border-radius: 30px;\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .btn-primary::before {\r\n  border-radius: 30px;\r\n}\r\n.others-option-for-responsive .option-inner .others-options .options-item .btn-primary::after {\r\n  border-radius: 30px;\r\n}\r\n\r\n/*================================================\r\nCurrency Transfer Provider Banner Area CSS\r\n=================================================*/\r\n.ctp-banner-area {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_18___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  padding-top: 190px;\r\n  padding-bottom: 100px;\r\n}\r\n.ctp-banner-area .container-fluid {\r\n  padding-left: 35px;\r\n  padding-right: 35px;\r\n}\r\n.ctp-banner-area::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 100%;\r\n  background: linear-gradient(90.09deg, #000000 0.07%, #000000 56.23%, rgba(0, 0, 0, 0) 91.07%);\r\n  opacity: 0.5;\r\n  z-index: -1;\r\n}\r\n\r\n.ctp-banner-content {\r\n  position: relative;\r\n  top: -10px;\r\n}\r\n.ctp-banner-content h1 {\r\n  font-size: 75px;\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-content h1 span {\r\n  position: relative;\r\n  z-index: 1;\r\n  display: inline-block;\r\n}\r\n.ctp-banner-content h1 span::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  left: 0;\r\n  bottom: 0;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_19___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  width: 318px;\r\n  height: 97px;\r\n  z-index: -1;\r\n}\r\n.ctp-banner-content .ctp-list {\r\n  padding: 0;\r\n  margin-top: 25px;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-content .ctp-list li {\r\n  list-style-type: none;\r\n  color: #ffffff;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n.ctp-banner-content .ctp-list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-content .ctp-list li img {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 2px;\r\n}\r\n.ctp-banner-content .video-view {\r\n  margin-top: 30px;\r\n}\r\n.ctp-banner-content .video-view .video-btn i {\r\n  display: inline-block;\r\n  height: 65px;\r\n  width: 65px;\r\n  line-height: 65px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  color: #ffffff;\r\n  font-size: 30px;\r\n  text-align: center;\r\n  border-radius: 50px;\r\n}\r\n.ctp-banner-content .video-view .video-btn span {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #ffffff;\r\n  margin-left: 10px;\r\n}\r\n\r\n.ctp-banner-form {\r\n  max-width: 550px;\r\n  margin-left: auto;\r\n}\r\n.ctp-banner-form .form-header {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  padding: 25px;\r\n  border-radius: 15px;\r\n  text-align: center;\r\n}\r\n.ctp-banner-form .form-header span {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #ffffff;\r\n  display: inline-block;\r\n  margin-bottom: 12px;\r\n}\r\n.ctp-banner-form .form-header h3 {\r\n  font-size: 30px;\r\n  color: #ffffff;\r\n  font-weight: 600;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-form .form-content {\r\n  background-color: #ffffff;\r\n  padding: 50px 30px 40px;\r\n  border-radius: 0 0 15px 15px;\r\n  max-width: 490px;\r\n  margin: auto;\r\n}\r\n.ctp-banner-form .form-content .form-group {\r\n  position: relative;\r\n  margin-bottom: 35px;\r\n}\r\n.ctp-banner-form .form-content .form-group.zero {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-form .form-content .form-group label {\r\n  display: block;\r\n  background-color: #ffffff;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n  position: absolute;\r\n  left: 20px;\r\n  top: -10px;\r\n  z-index: 1;\r\n  padding: 5px 10px;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select {\r\n  height: auto;\r\n  line-height: normal;\r\n  padding: 40px 50px 15px 20px;\r\n  float: unset;\r\n  border: none;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  background-color: #ffffff;\r\n  color: #212529;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select:after {\r\n  right: 20px;\r\n  border-bottom: 2px solid #212529;\r\n  border-right: 2px solid #212529;\r\n  width: 8px;\r\n  height: 8px;\r\n  margin-top: 8px;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select .list {\r\n  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);\r\n  background-color: #ffffff;\r\n  width: 100%;\r\n  padding-top: 20px;\r\n  padding-right: 10px;\r\n  padding-left: 10px;\r\n  padding-bottom: 20px;\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select .list .option {\r\n  line-height: initial;\r\n  min-height: auto;\r\n  margin-top: 12px;\r\n  padding-left: 20px;\r\n  padding-right: 20px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  transition: 0.5s;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select .list .option:hover, .ctp-banner-form .form-content .form-group .nice-select .list .option:focus, .ctp-banner-form .form-content .form-group .nice-select .list .option.focus, .ctp-banner-form .form-content .form-group .nice-select .list .option.selected {\r\n  background-color: transparent;\r\n  font-weight: 500;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select .list .option:hover {\r\n  color: #22418E;\r\n}\r\n.ctp-banner-form .form-content .form-group .nice-select .list .option:first-child {\r\n  margin-top: 0;\r\n}\r\n.ctp-banner-form .form-content .form-group .form-control {\r\n  height: 100%;\r\n  padding: 40px 100px 15px 20px;\r\n  float: unset;\r\n  border: none;\r\n  font-size: 16.5px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  background-color: #ffffff;\r\n  color: #212529;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n}\r\n.ctp-banner-form .form-content .form-group .form-control::-moz-placeholder {\r\n  color: #212529;\r\n}\r\n.ctp-banner-form .form-content .form-group .form-control::placeholder {\r\n  color: #212529;\r\n}\r\n.ctp-banner-form .form-content .form-group .amount-currency-select {\r\n  position: absolute;\r\n  right: 20px;\r\n  bottom: 15px;\r\n}\r\n.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select {\r\n  padding: 2px 25px 0 0;\r\n  display: inline-block;\r\n  border: none;\r\n  line-height: normal;\r\n  background-color: transparent;\r\n}\r\n.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select:after {\r\n  right: 5px;\r\n  margin-top: -5px;\r\n}\r\n.ctp-banner-form .form-content .form-group .amount-currency-select .nice-select .list {\r\n  width: auto;\r\n}\r\n.ctp-banner-form .form-content .info {\r\n  margin-top: 18px;\r\n  margin-bottom: 20px;\r\n}\r\n.ctp-banner-form .form-content .info p {\r\n  font-size: 16px;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-banner-form .form-content .info p span {\r\n  color: #000000;\r\n  font-weight: 600;\r\n}\r\n.ctp-banner-form .form-content .btn-primary {\r\n  padding: 20px 30px;\r\n  border-radius: 50px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  display: block;\r\n  width: 100%;\r\n  transition: 0.5s;\r\n}\r\n.ctp-banner-form .form-content .btn-primary::before {\r\n  border-radius: 50px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n}\r\n.ctp-banner-form .form-content .btn-primary::after {\r\n  border-radius: 50px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n\r\n/*================================================\r\nCountries Area CSS\r\n=================================================*/\r\n.ctp-countries-card {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 100px;\r\n  padding: 10px 20px 10px 10px;\r\n  margin-bottom: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: 0.5s;\r\n}\r\n.ctp-countries-card img {\r\n  margin-right: 10px;\r\n  max-width: 40px;\r\n}\r\n.ctp-countries-card span {\r\n  font-size: 16px;\r\n  color: #212529;\r\n  font-weight: 400;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-countries-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/*================================================\r\nMoney Transfer Area CSS\r\n=================================================*/\r\n.ctp-money-transfer-card {\r\n  max-width: 385px;\r\n  margin: 0 auto 25px;\r\n  text-align: center;\r\n  transition: 0.5s;\r\n}\r\n.ctp-money-transfer-card h3 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n  color: #212529;\r\n}\r\n.ctp-money-transfer-card .image {\r\n  display: inline-block;\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 50%;\r\n  height: 150px;\r\n  width: 150px;\r\n  line-height: 150px;\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n.ctp-money-transfer-card .image .number {\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 30px;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  color: #ffffff;\r\n  border-radius: 50px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 10px;\r\n  transition: 0.5s;\r\n  z-index: 1;\r\n}\r\n.ctp-money-transfer-card .image .number::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 30px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-money-transfer-card p {\r\n  margin-bottom: 0;\r\n  color: #57647C;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-money-transfer-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n.ctp-money-transfer-card:hover .image .number::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nChoose Area CSS\r\n=================================================*/\r\n.ctp-choose-area {\r\n  overflow: hidden;\r\n}\r\n.ctp-choose-area .container-fluid {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n\r\n.ctp-choose-image {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_20___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n.ctp-choose-image.with-border-radius {\r\n  border-radius: 15px;\r\n}\r\n\r\n.ctp-choose-content {\r\n  padding: 85px 90px;\r\n}\r\n.ctp-choose-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-choose-content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n}\r\n.ctp-choose-content .choose-inner-card {\r\n  margin-top: 25px;\r\n}\r\n.ctp-choose-content .choose-inner-card h4 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  position: relative;\r\n  margin-bottom: 16.5px;\r\n  padding-left: 40px;\r\n}\r\n.ctp-choose-content .choose-inner-card h4 .icon {\r\n  display: inline-block;\r\n  height: 25px;\r\n  width: 25px;\r\n  line-height: 25px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  text-align: center;\r\n  color: #ffffff;\r\n  border-radius: 50px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 15px;\r\n  transition: 0.5s;\r\n  z-index: 1;\r\n}\r\n.ctp-choose-content .choose-inner-card h4 .icon::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 25px;\r\n  width: 25px;\r\n  line-height: 25px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-choose-content .choose-inner-card h4:hover .icon::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-choose-content.without-padding {\r\n  padding-top: 30px;\r\n  padding-bottom: 30px;\r\n  padding-left: 0;\r\n  padding-right: 50px;\r\n}\r\n\r\n/*================================================\r\nServices Area CSS\r\n=================================================*/\r\n.ctp-services-card {\r\n  margin-bottom: 25px;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  background-color: #ffffff;\r\n  border-radius: 15px;\r\n  padding: 25px;\r\n  transition: 0.5s;\r\n}\r\n.ctp-services-card h3 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  position: relative;\r\n  margin-bottom: 16.5px;\r\n  padding: 10.5px 0 10.5px 65px;\r\n}\r\n.ctp-services-card h3 .icon {\r\n  display: inline-block;\r\n  height: 50px;\r\n  width: 50px;\r\n  line-height: 50px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  text-align: center;\r\n  color: #ffffff;\r\n  border-radius: 50px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 15px;\r\n  transition: 0.5s;\r\n  z-index: 1;\r\n}\r\n.ctp-services-card h3 .icon::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 50px;\r\n  width: 50px;\r\n  line-height: 50px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-services-card h3 .icon img {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  margin: auto;\r\n}\r\n.ctp-services-card h3:hover .icon::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-services-card p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n}\r\n.ctp-services-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/*================================================\r\nKey Features Area CSS\r\n=================================================*/\r\n.ctp-key-features-area {\r\n  background-color: #E5F6F0;\r\n}\r\n\r\n.ctp-key-features-tabs .nav {\r\n  padding: 0;\r\n  list-style-type: none;\r\n  border: none;\r\n  text-align: center;\r\n  justify-content: center;\r\n  border-radius: 0;\r\n}\r\n.ctp-key-features-tabs .nav .nav-item {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  border: none;\r\n  font-family: \"Raleway\", sans-serif;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.ctp-key-features-tabs .nav .nav-item .nav-link {\r\n  color: #ffffff;\r\n  border: none;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 30px 128.5px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: 18px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  border-radius: 0;\r\n}\r\n.ctp-key-features-tabs .nav .nav-item .nav-link::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n  z-index: -1;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-key-features-tabs .nav .nav-item .nav-link:hover::before, .ctp-key-features-tabs .nav .nav-item .nav-link.active::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane {\r\n  background-color: #ffffff;\r\n  padding: 80px 100px;\r\n  border-radius: 0 0 15px 15px;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-image {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_21___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n  border-radius: 15px;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content {\r\n  padding-top: 5px;\r\n  padding-bottom: 5px;\r\n  padding-left: 100px;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li {\r\n  list-style-type: none;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 400;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding-left: 28px;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-key-features-tabs .tab-content .tab-pane .ctp-key-features-content .list li i {\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 18px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n\r\n/*================================================\r\nWorking People Area CSS\r\n=================================================*/\r\n.ctp-working-people-area {\r\n  overflow: hidden;\r\n}\r\n.ctp-working-people-area .container-fluid {\r\n  padding-left: 35px;\r\n  padding-right: 0;\r\n}\r\n\r\n.ctp-working-people-content {\r\n  padding-top: 70px;\r\n  padding-bottom: 70px;\r\n  padding-right: 70px;\r\n}\r\n.ctp-working-people-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-working-people-content .working-people-inner-card {\r\n  position: relative;\r\n  margin-top: 30px;\r\n  padding-left: 125px;\r\n}\r\n.ctp-working-people-content .working-people-inner-card .icon {\r\n  display: inline-block;\r\n  height: 115px;\r\n  width: 100px;\r\n  line-height: 115px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  text-align: center;\r\n  color: #ffffff;\r\n  border-radius: 10px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 15px;\r\n  transition: 0.5s;\r\n  z-index: 1;\r\n}\r\n.ctp-working-people-content .working-people-inner-card .icon::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 115px;\r\n  width: 100px;\r\n  line-height: 115px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  border-radius: 10px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-working-people-content .working-people-inner-card h4 {\r\n  font-size: 20px;\r\n  color: #212529;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-working-people-content .working-people-inner-card p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n}\r\n.ctp-working-people-content .working-people-inner-card:hover .icon::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n.ctp-working-people-image {\r\n  display: inline-block;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_22___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n/*================================================\r\nProtec Area CSS\r\n=================================================*/\r\n.ctp-protec-card {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  padding: 35px 30px 35px 0;\r\n  margin-bottom: 25px;\r\n  transition: 0.5s;\r\n}\r\n.ctp-protec-card .content {\r\n  position: relative;\r\n  padding-left: 110px;\r\n}\r\n.ctp-protec-card .content .number {\r\n  display: inline-block;\r\n  width: 85px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n  background: #E5F6F0;\r\n  text-align: center;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.ctp-protec-card .content .number span {\r\n  display: inline-block;\r\n  font-size: 40px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: bold;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  transform: rotate(-90deg);\r\n}\r\n.ctp-protec-card .content .number.color-two {\r\n  background: #F8F5EE;\r\n}\r\n.ctp-protec-card .content h3 {\r\n  font-size: 20px;\r\n  color: #212529;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-protec-card .content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n}\r\n.ctp-protec-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/*================================================\r\nApp Area CSS\r\n=================================================*/\r\n.ctp-app-area {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_23___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  overflow: hidden;\r\n  padding-top: 50px;\r\n}\r\n.ctp-app-area .container-fluid {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n\r\n.ctp-app-content {\r\n  max-width: 630px;\r\n  margin-left: auto;\r\n  padding-right: 70px;\r\n  position: relative;\r\n  top: -25px;\r\n}\r\n.ctp-app-content .sub {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n  display: inline-block;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-app-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-app-content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n}\r\n.ctp-app-content .btn-box {\r\n  margin-top: 30px;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn {\r\n  border-radius: 50px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 11px 30px 10px 68px;\r\n  font-size: 12px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn i {\r\n  position: absolute;\r\n  left: 30px;\r\n  top: 11px;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn i::before {\r\n  font-size: 25px;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn span {\r\n  display: block;\r\n  margin-top: 5px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-app-content .btn-box .app-store-btn:hover::before {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn {\r\n  margin-left: 12px;\r\n  border-radius: 50px;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  color: #ffffff;\r\n  padding: 11px 30px 10px 68px;\r\n  font-size: 12px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn i {\r\n  position: absolute;\r\n  left: 30px;\r\n  top: 12px;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn i::before {\r\n  font-size: 25px;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn span {\r\n  display: block;\r\n  margin-top: 5px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50px;\r\n  z-index: -1;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50px;\r\n  z-index: -1;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn:hover {\r\n  color: #ffffff;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn:hover::after {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-app-content .btn-box .play-store-btn:hover::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-app-content .info {\r\n  margin-top: 30px;\r\n}\r\n.ctp-app-content .info span {\r\n  color: #212529;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  font-size: 20px;\r\n}\r\n\r\n.ctp-app-image {\r\n  text-align: end;\r\n}\r\n\r\n/*================================================\r\nReviews Area CSS\r\n=================================================*/\r\n.ctp-reviews-box {\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  background-color: #ffffff;\r\n  padding: 30px 35px;\r\n  transition: 0.5s;\r\n  margin-bottom: 25px;\r\n}\r\n.ctp-reviews-box .rating {\r\n  padding: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n.ctp-reviews-box .rating li {\r\n  list-style-type: none;\r\n  display: inline-block;\r\n  margin-right: 1px;\r\n}\r\n.ctp-reviews-box .rating li:last-child {\r\n  margin-right: 0;\r\n}\r\n.ctp-reviews-box .rating li span {\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-left: 10px;\r\n  color: #57647C;\r\n}\r\n.ctp-reviews-box h3 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin-bottom: 12px;\r\n}\r\n.ctp-reviews-box p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 12px;\r\n  font-size: 16px;\r\n}\r\n.ctp-reviews-box h4 {\r\n  margin-bottom: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #0A2049;\r\n}\r\n.ctp-reviews-box h4 span {\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #57647C;\r\n}\r\n.ctp-reviews-box:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/*================================================\r\nFAQ Area CSS\r\n=================================================*/\r\n.ctp-faq-accordion {\r\n  max-width: 1095px;\r\n  margin: auto;\r\n}\r\n.ctp-faq-accordion .accordion-item {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  margin-bottom: 15px;\r\n  transition: 0.5s;\r\n  border: none;\r\n}\r\n.ctp-faq-accordion .accordion-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-faq-accordion .accordion-item .accordion-button {\r\n  color: #0A2049;\r\n  position: relative;\r\n  box-shadow: unset;\r\n  margin-bottom: 0;\r\n  display: block;\r\n  border: none;\r\n  width: 100%;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  padding: 20px 75px 20px 30px;\r\n  transition: 0.5s;\r\n  background-color: transparent;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-faq-accordion .accordion-item .accordion-button::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  right: 30px;\r\n  top: 25px;\r\n  display: inline-block;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_24___ + ");\r\n  background-position: center center;\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n.ctp-faq-accordion .accordion-item .accordion-button.collapsed::before {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_25___ + ");\r\n}\r\n.ctp-faq-accordion .accordion-item .accordion-body {\r\n  padding: 0 30px 30px 30px;\r\n}\r\n.ctp-faq-accordion .accordion-item .accordion-body p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n}\r\n\r\n/*================================================\r\nPage Banner Area CSS\r\n=================================================*/\r\n.ctp-page-banner-area {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_26___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n  z-index: 1;\r\n  padding-top: 185px;\r\n  padding-bottom: 115px;\r\n}\r\n.ctp-page-banner-area::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 100%;\r\n  background: linear-gradient(90.09deg, #000000 0.07%, #000000 56.23%, rgba(0, 0, 0, 0) 91.07%);\r\n  opacity: 0.5;\r\n  z-index: -1;\r\n}\r\n\r\n.ctp-page-banner-content h3 {\r\n  font-size: 80px;\r\n  color: #ffffff;\r\n  font-family: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  margin-bottom: 15px;\r\n  font-weight: bold;\r\n}\r\n.ctp-page-banner-content .list {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-page-banner-content .list li {\r\n  display: inline-block;\r\n  list-style-type: none;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #ffffff;\r\n  position: relative;\r\n  margin-left: 35px;\r\n}\r\n.ctp-page-banner-content .list li::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: -16.5px;\r\n  top: 8px;\r\n  height: 15px;\r\n  width: 1px;\r\n  background: #ffffff;\r\n  transform: rotate(20deg);\r\n}\r\n.ctp-page-banner-content .list li:first-child {\r\n  margin-left: 0;\r\n}\r\n.ctp-page-banner-content .list li:first-child::before {\r\n  display: none;\r\n}\r\n.ctp-page-banner-content .list li a {\r\n  display: block;\r\n  color: #ffffff;\r\n}\r\n.ctp-page-banner-content .list li a:hover {\r\n  color: #22418E;\r\n}\r\n\r\n/*================================================\r\nAbout Area CSS\r\n=================================================*/\r\n.ctp-about-image {\r\n  display: inline-block;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_27___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n  border-radius: 15px;\r\n}\r\n\r\n.ctp-about-content {\r\n  padding-top: 30px;\r\n  padding-bottom: 30px;\r\n  padding-left: 100px;\r\n}\r\n.ctp-about-content span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: inline-block;\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-about-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-about-content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n}\r\n.ctp-about-content h4 {\r\n  font-size: 20px;\r\n  color: #212529;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/*================================================\r\nFun Facts Area CSS\r\n=================================================*/\r\n.ctp-funfacts-inner-box {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  padding-left: 50px;\r\n  padding-right: 50px;\r\n}\r\n.ctp-funfacts-inner-box .col-lg-3 {\r\n  border-right: 1px solid #e0e0e0;\r\n}\r\n.ctp-funfacts-inner-box .col-lg-3:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.ctp-funfact-card {\r\n  margin-bottom: 25px;\r\n  text-align: center;\r\n}\r\n.ctp-funfact-card h3 {\r\n  font-size: 50px;\r\n  font-weight: bold;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  margin-bottom: 8px;\r\n}\r\n.ctp-funfact-card h3 .odometer {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n.ctp-funfact-card h3 .odometer-value {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n.ctp-funfact-card p {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/*================================================\r\nTeam Area CSS\r\n=================================================*/\r\n.ctp-team-card {\r\n  margin-bottom: 25px;\r\n  transition: 0.5s;\r\n}\r\n.ctp-team-card .team-image {\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n}\r\n.ctp-team-card .team-image img {\r\n  transition: 0.5s;\r\n  border-radius: 15px;\r\n}\r\n.ctp-team-card .team-content {\r\n  position: relative;\r\n  margin-top: 25px;\r\n  padding-right: 55px;\r\n}\r\n.ctp-team-card .team-content h3 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-team-card .team-content span {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  display: inline-block;\r\n  margin-top: 10px;\r\n}\r\n.ctp-team-card .team-content .icon {\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n.ctp-team-card .team-content .icon a {\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 30px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  text-align: center;\r\n  color: #ffffff;\r\n  border-radius: 50px;\r\n  font-size: 15px;\r\n  transition: 0.5s;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.ctp-team-card .team-content .icon a::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 30px;\r\n  width: 30px;\r\n  line-height: 30px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-team-card .team-content .icon a i {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  margin: auto;\r\n}\r\n.ctp-team-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n.ctp-team-card:hover .team-image img {\r\n  transform: scale(1.1);\r\n}\r\n.ctp-team-card:hover .team-content .icon a::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/*================================================\r\nInvestors Area CSS\r\n=================================================*/\r\n.ctp-investors-card {\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 100px;\r\n  background-color: #ffffff;\r\n  margin-bottom: 25px;\r\n  padding: 15px 10px;\r\n  transition: 0.5s;\r\n  text-align: center;\r\n}\r\n.ctp-investors-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/*================================================\r\nSend Money Online Area CSS\r\n=================================================*/\r\n.ctp-send-money-online-area {\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_28___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  margin-left: 35px;\r\n  margin-right: 35px;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.ctp-send-money-online-area::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: -1;\r\n}\r\n\r\n.ctp-send-money-online-content h1 {\r\n  font-size: 40px;\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-content .ctp-list {\r\n  padding: 0;\r\n  margin-top: 25px;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-content .ctp-list li {\r\n  list-style-type: none;\r\n  color: #ffffff;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n.ctp-send-money-online-content .ctp-list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-content .ctp-list li img {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 2px;\r\n}\r\n.ctp-send-money-online-content .video-view {\r\n  margin-top: 30px;\r\n}\r\n.ctp-send-money-online-content .video-view .video-btn i {\r\n  display: inline-block;\r\n  height: 65px;\r\n  width: 65px;\r\n  line-height: 65px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  color: #ffffff;\r\n  font-size: 30px;\r\n  text-align: center;\r\n  border-radius: 50px;\r\n}\r\n.ctp-send-money-online-content .video-view .video-btn span {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #ffffff;\r\n  margin-left: 10px;\r\n}\r\n\r\n.ctp-send-money-online-form {\r\n  max-width: 550px;\r\n  margin-left: auto;\r\n}\r\n.ctp-send-money-online-form .form-header {\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  padding: 25px;\r\n  border-radius: 15px;\r\n  text-align: center;\r\n}\r\n.ctp-send-money-online-form .form-header span {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #ffffff;\r\n  display: inline-block;\r\n  margin-bottom: 12px;\r\n}\r\n.ctp-send-money-online-form .form-header h3 {\r\n  font-size: 30px;\r\n  color: #ffffff;\r\n  font-weight: 600;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-form .form-content {\r\n  background-color: #ffffff;\r\n  padding: 50px 30px 40px;\r\n  border-radius: 0 0 15px 15px;\r\n  max-width: 490px;\r\n  margin: auto;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group {\r\n  position: relative;\r\n  margin-bottom: 35px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group.zero {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group label {\r\n  display: block;\r\n  background-color: #ffffff;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n  position: absolute;\r\n  left: 20px;\r\n  top: -10px;\r\n  z-index: 1;\r\n  padding: 5px 10px;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select {\r\n  height: auto;\r\n  line-height: normal;\r\n  padding: 40px 50px 15px 20px;\r\n  float: unset;\r\n  border: none;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  background-color: #ffffff;\r\n  color: #212529;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select:after {\r\n  right: 20px;\r\n  border-bottom: 2px solid #212529;\r\n  border-right: 2px solid #212529;\r\n  width: 8px;\r\n  height: 8px;\r\n  margin-top: 8px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select .list {\r\n  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.1);\r\n  background-color: #ffffff;\r\n  width: 100%;\r\n  padding-top: 20px;\r\n  padding-right: 10px;\r\n  padding-left: 10px;\r\n  padding-bottom: 20px;\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select .list .option {\r\n  line-height: initial;\r\n  min-height: auto;\r\n  margin-top: 12px;\r\n  padding-left: 20px;\r\n  padding-right: 20px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  transition: 0.5s;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:hover, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option:focus, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option.focus, .ctp-send-money-online-form .form-content .form-group .nice-select .list .option.selected {\r\n  background-color: transparent;\r\n  font-weight: 500;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:hover {\r\n  color: #22418E;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .nice-select .list .option:first-child {\r\n  margin-top: 0;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .form-control {\r\n  height: 100%;\r\n  padding: 40px 100px 15px 20px;\r\n  float: unset;\r\n  border: none;\r\n  font-size: 16.5px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  background-color: #ffffff;\r\n  color: #212529;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .form-control::-moz-placeholder {\r\n  color: #212529;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .form-control::placeholder {\r\n  color: #212529;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .amount-currency-select {\r\n  position: absolute;\r\n  right: 20px;\r\n  bottom: 15px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select {\r\n  padding: 2px 25px 0 0;\r\n  display: inline-block;\r\n  border: none;\r\n  line-height: normal;\r\n  background-color: transparent;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select:after {\r\n  right: 5px;\r\n  margin-top: -5px;\r\n}\r\n.ctp-send-money-online-form .form-content .form-group .amount-currency-select .nice-select .list {\r\n  width: auto;\r\n}\r\n.ctp-send-money-online-form .form-content .info {\r\n  margin-top: 18px;\r\n  margin-bottom: 20px;\r\n}\r\n.ctp-send-money-online-form .form-content .info p {\r\n  font-size: 16px;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-send-money-online-form .form-content .info p span {\r\n  color: #000000;\r\n  font-weight: 600;\r\n}\r\n.ctp-send-money-online-form .form-content .btn-primary {\r\n  padding: 20px 30px;\r\n  border-radius: 50px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n  display: block;\r\n  width: 100%;\r\n  transition: 0.5s;\r\n}\r\n.ctp-send-money-online-form .form-content .btn-primary::before {\r\n  border-radius: 50px;\r\n  background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%);\r\n  transition: 0.5s;\r\n}\r\n.ctp-send-money-online-form .form-content .btn-primary::after {\r\n  border-radius: 50px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n}\r\n\r\n/*================================================\r\nContact Area CSS\r\n=================================================*/\r\n.ctp-contact-form {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  padding: 40px;\r\n}\r\n.ctp-contact-form h3 {\r\n  font-size: 40px;\r\n  font-weight: bold;\r\n  color: #212529;\r\n  margin-bottom: 40px;\r\n}\r\n.ctp-contact-form .form-group {\r\n  position: relative;\r\n  margin-bottom: 35px;\r\n}\r\n.ctp-contact-form .form-group label {\r\n  display: block;\r\n  background-color: #ffffff;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n  position: absolute;\r\n  left: 20px;\r\n  top: -10px;\r\n  z-index: 1;\r\n  padding: 5px 10px;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n}\r\n.ctp-contact-form .form-group .form-control {\r\n  height: 100%;\r\n  padding: 40px 20px 20px 20px;\r\n  float: unset;\r\n  border: none;\r\n  font-size: 15px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 600;\r\n  background-color: #ffffff;\r\n  color: #212529;\r\n  border: 1px solid #E5E5E5;\r\n  border-radius: 5px;\r\n}\r\n.ctp-contact-form .form-group .form-control::-moz-placeholder {\r\n  color: #212529;\r\n  -moz-transition: 0.5s;\r\n  transition: 0.5s;\r\n}\r\n.ctp-contact-form .form-group .form-control::placeholder {\r\n  color: #212529;\r\n  transition: 0.5s;\r\n}\r\n.ctp-contact-form .form-group .form-control:focus {\r\n  border: 1px solid #22418E;\r\n}\r\n.ctp-contact-form .form-group .form-control:focus::-moz-placeholder {\r\n  color: transparent;\r\n}\r\n.ctp-contact-form .form-group .form-control:focus::placeholder {\r\n  color: transparent;\r\n}\r\n.ctp-contact-form .form-group textarea.form-control {\r\n  height: 150px !important;\r\n  line-height: normal;\r\n}\r\n.ctp-contact-form .form-group .list-unstyled li {\r\n  color: #22418E;\r\n  font-size: 15px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-weight: 500;\r\n  margin-top: 10px;\r\n}\r\n.ctp-contact-form .btn-primary {\r\n  padding: 20px 30px;\r\n  border-radius: 50px;\r\n  width: 100%;\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-contact-form .btn-primary::before {\r\n  border-radius: 50px;\r\n}\r\n.ctp-contact-form .btn-primary::after {\r\n  border-radius: 50px;\r\n}\r\n.ctp-contact-form .text-danger {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.ctp-contact-information .information-box {\r\n  background-color: #ffffff;\r\n  box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.05);\r\n  border-radius: 15px;\r\n  padding: 40px;\r\n  margin-bottom: 25px;\r\n}\r\n.ctp-contact-information .information-box h3 {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #212529;\r\n  margin-bottom: 18px;\r\n}\r\n.ctp-contact-information .information-box .contact-info {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-contact-information .information-box .contact-info li {\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  color: #57647C;\r\n  list-style-type: none;\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n  padding-left: 85px;\r\n}\r\n.ctp-contact-information .information-box .contact-info li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-contact-information .information-box .contact-info li.email {\r\n  padding-left: 65px;\r\n}\r\n.ctp-contact-information .information-box .contact-info li.phone {\r\n  padding-left: 65px;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .sub {\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .info {\r\n  margin-bottom: 10px;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .info:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .info span {\r\n  color: #212529;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: block;\r\n  margin-bottom: 2px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .info a {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n}\r\n.ctp-contact-information .information-box .contact-info li .info a:hover {\r\n  color: #22418E;\r\n}\r\n.ctp-contact-information .information-box .time-info {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-contact-information .information-box .time-info li {\r\n  list-style-type: none;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-contact-information .information-box .time-info li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-contact-information .information-box .time-info li .color {\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n.ctp-contact-information .information-box .time-info li span {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n}\r\n.ctp-contact-information .information-map .map {\r\n  border-radius: 15px;\r\n  width: 100%;\r\n  height: 300px;\r\n  margin-bottom: -8px;\r\n}\r\n\r\n/*================================================\r\nCurrency Area CSS\r\n=================================================*/\r\n.ctp-currency-image {\r\n  display: inline-block;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_29___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n  border-radius: 15px;\r\n}\r\n\r\n.ctp-currency-content {\r\n  padding-top: 70px;\r\n  padding-bottom: 70px;\r\n  padding-left: 80px;\r\n}\r\n.ctp-currency-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-currency-content p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n}\r\n.ctp-currency-content .list {\r\n  padding: 0;\r\n  margin-top: 20px;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-currency-content .list li {\r\n  list-style-type: none;\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n.ctp-currency-content .list li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-currency-content .list li img {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 3.5px;\r\n}\r\n.ctp-currency-content .currency-btn {\r\n  margin-top: 30px;\r\n}\r\n.ctp-currency-content .currency-btn .btn-primary {\r\n  border-radius: 30px;\r\n  text-transform: capitalize;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-currency-content .currency-btn .btn-primary::before {\r\n  border-radius: 30px;\r\n}\r\n.ctp-currency-content .currency-btn .btn-primary::after {\r\n  border-radius: 30px;\r\n}\r\n\r\n/*================================================\r\nWorld Area CSS\r\n=================================================*/\r\n.ctp-world-content {\r\n  padding-top: 100px;\r\n  padding-bottom: 100px;\r\n  padding-right: 100px;\r\n}\r\n.ctp-world-content h3 {\r\n  font-size: 40px;\r\n  color: #212529;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-world-content .world-inner-card {\r\n  margin-top: 30px;\r\n}\r\n.ctp-world-content .world-inner-card h4 {\r\n  font-size: 20px;\r\n  color: #212529;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-world-content .world-inner-card p {\r\n  color: #57647C;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.ctp-world-image {\r\n  display: inline-block;\r\n  background-image: url(" + ___CSS_LOADER_URL_REPLACEMENT_30___ + ");\r\n  background-position: center center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  height: 100%;\r\n  width: 100%;\r\n  border-radius: 15px;\r\n}\r\n\r\n/*================================================\r\nFooter Area CSS\r\n=================================================*/\r\n.ctp-footer-area {\r\n  background-color: #212529;\r\n}\r\n.ctp-footer-area .copyright-area {\r\n  border-top: 1px solid #414243;\r\n  margin-top: 75px;\r\n  padding-top: 30px;\r\n  padding-bottom: 30px;\r\n}\r\n.ctp-footer-area .copyright-area p {\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  font-family: \"Raleway\", sans-serif;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-area .copyright-area p a {\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.ctp-footer-widget {\r\n  margin-bottom: 25px;\r\n}\r\n.ctp-footer-widget .logo {\r\n  margin-bottom: 25px;\r\n}\r\n.ctp-footer-widget .social-links {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-widget .social-links span {\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  display: block;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-footer-widget .social-links li {\r\n  list-style-type: none;\r\n  display: inline-block;\r\n  margin-right: 8px;\r\n}\r\n.ctp-footer-widget .social-links li:last-child {\r\n  margin-right: 0;\r\n}\r\n.ctp-footer-widget .social-links li a {\r\n  display: inline-block;\r\n  height: 35px;\r\n  width: 35px;\r\n  line-height: 35px;\r\n  background: #57647C;\r\n  text-align: center;\r\n  color: #ffffff;\r\n  border-radius: 50px;\r\n  font-size: 15px;\r\n  transition: 0.5s;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.ctp-footer-widget .social-links li a::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 35px;\r\n  width: 35px;\r\n  line-height: 35px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  border-radius: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: -1;\r\n  transition: 0.5s;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n}\r\n.ctp-footer-widget .social-links li a i {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  margin: auto;\r\n}\r\n.ctp-footer-widget .social-links li a:hover::before {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.ctp-footer-widget h3 {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 25px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-footer-widget .links {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-widget .links li {\r\n  list-style-type: none;\r\n  margin-bottom: 15px;\r\n  font-family: \"Raleway\", sans-serif;\r\n}\r\n.ctp-footer-widget .links li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-widget .links li a {\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  position: relative;\r\n  padding-left: 22px;\r\n}\r\n.ctp-footer-widget .links li a:hover {\r\n  color: #22418E;\r\n}\r\n.ctp-footer-widget .links li a::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 10px;\r\n  width: 10px;\r\n  border-radius: 50px;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  left: 0;\r\n  top: 3.8px;\r\n}\r\n.ctp-footer-widget .info {\r\n  padding: 0;\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-widget .info li {\r\n  list-style-type: none;\r\n  color: #ffffff;\r\n  font-family: \"Raleway\", sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 400;\r\n  margin-bottom: 15px;\r\n}\r\n.ctp-footer-widget .info li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.ctp-footer-widget .info li span {\r\n  font-family: \"Raleway\", sans-serif;\r\n  background: linear-gradient(90deg, rgb(34, 65, 142) 0%, rgb(85, 147, 65) 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n.ctp-footer-widget .info li a {\r\n  color: #ffffff;\r\n}\r\n.ctp-footer-widget .info li a:hover {\r\n  color: #22418E;\r\n}\r\n\r\n/* End \"Currency Transfer Provider Demo\" \"CSS\" */\r\n\r\n", ""]);
105 | // Exports
106 | /* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);
107 | 

Import trace for requested module:
./public/css/style.css


> Build failed because of webpack errors
