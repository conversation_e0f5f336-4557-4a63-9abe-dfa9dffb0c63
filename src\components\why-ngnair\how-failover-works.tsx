"use client";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  Shield,
  TrendingUp,
  <PERSON><PERSON>,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function HowFailoverWorks() {
  return (
    <section className="w-full py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            How Failover Works
          </h2>
          <p className="text-lg text-muted-foreground">
            When a primary processor is down or degraded, NGnair automatically
            reroutes eligible traffic to a secondary processor according to your
            rules — preserving authorizations, uptime, and customer trust.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="max-w-4xl mx-auto mb-12"
        >
          <Card className="overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10">
            <CardContent className="p-8">
              <div className="grid gap-8 md:grid-cols-3 items-center">
                {/* Step 1 */}
                <div className="text-center">
                  <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 w-fit mx-auto mb-4">
                    <CheckCircle className="size-8" />
                  </div>
                  <h3 className="font-semibold mb-2">Primary Processor</h3>
                  <p className="text-sm text-muted-foreground">
                    Normal transaction flow through primary processor
                  </p>
                </div>

                {/* Arrow */}
                <div className="flex justify-center">
                  <ArrowRight className="size-6 text-muted-foreground" />
                </div>

                {/* Step 2 */}
                <div className="text-center">
                  <div className="p-4 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 w-fit mx-auto mb-4">
                    <AlertTriangle className="size-8" />
                  </div>
                  <h3 className="font-semibold mb-2">Outage Detected</h3>
                  <p className="text-sm text-muted-foreground">
                    System detects processor downtime or degradation
                  </p>
                </div>
              </div>

              <div className="flex justify-center my-8">
                <div className="h-px w-full bg-border max-w-md"></div>
              </div>

              <div className="grid gap-8 md:grid-cols-3 items-center">
                {/* Step 3 */}
                <div className="text-center">
                  <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 w-fit mx-auto mb-4">
                    <RefreshCw className="size-8" />
                  </div>
                  <h3 className="font-semibold mb-2">Auto Rerouting</h3>
                  <p className="text-sm text-muted-foreground">
                    Traffic automatically switches to backup processor
                  </p>
                </div>

                {/* Arrow */}
                <div className="flex justify-center">
                  <ArrowRight className="size-6 text-muted-foreground" />
                </div>

                {/* Step 4 */}
                <div className="text-center">
                  <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 w-fit mx-auto mb-4">
                    <Shield className="size-8" />
                  </div>
                  <h3 className="font-semibold mb-2">Uptime Preserved</h3>
                  <p className="text-sm text-muted-foreground">
                    Revenue continues flowing without interruption
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
