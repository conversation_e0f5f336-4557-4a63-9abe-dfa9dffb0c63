"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, TrendingUp, Building, Code, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function SolutionsGrid() {
  const solutions = [
    {
      title: "For ISOs & ISVs",
      subtitle:
        "Empower your platform with embedded payments—your UI, your processor.",
      icon: <TrendingUp className="size-8" />,
      features: [
        "Embed merchant onboarding & KYC/KYB directly within your software experience, with white-label flexibility.",
        "Shorten approval time with AI underwriting and automated workflows",
        "Processor-agnostic architecture ensures uptime through automatic failover—without changing merchants.",
        "Give agents visibility and multi‑agent pay splits for transparent commissions",
        "Centralize servicing: changes, disputes, and reporting in one pane",
      ],
      outcome:
        "Faster integration, improved merchant satisfaction, and new monetization—without processing disruptions.",
      cta: "Book a partner demo",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      title: "For Banks & Financial Institutions",
      subtitle: "Modernize merchant services without rebuilding your stack.",
      icon: <Building className="size-8" />,
      features: [
        "Add RTP & FedNow as additional bank-payment choices, enabling direct-bank purchases—not instant settlement (settlement remains in the processor’s control)",
        "Apply tailored risk models per program or processor",
        "Secure account linkage via Open Banking, reducing fraud and onboarding friction.",
        "Replace legacy portals with a unified CRM/ERP‑style merchant management console",
        "Use failover processing for business continuity and reputational protection",
      ],
      outcome: "Modern capabilities, lower risk, higher partner satisfaction.",
      cta: "Speak with solutions engineering",
      gradient: "from-green-500 to-emerald-500",
    },
    {
      title: "For SaaS Platforms",
      subtitle:
        "Embed payments and merchant operations that scale with your product.",
      icon: <Code className="size-8" />,
      features: [
        "Native onboarding/KYC, payout visibility, and dispute handling",
        "Processor-neutral setup avoids vendor lock-in and simplifies scale",
        "POS marketplace for hardware/software bundles and new revenue streams",
        "Integrations for accounting, CRM, and ERP to sync orders, invoices, and customers",
      ],
      outcome:
        "Faster time‑to‑market, new monetization, and better customer retention.",
      cta: "Explore embedded payments with NGnair",
      gradient: "from-purple-500 to-pink-500",
    },
  ];
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid gap-12 lg:gap-16">
          {solutions.map((solution, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className={`${
                index % 2 === 1 ? "lg:flex-row-reverse" : ""
              } flex flex-col lg:flex-row gap-8 lg:gap-12 items-center`}
            >
              <div className="flex-1 space-y-6">
                <div className="flex items-center gap-4">
                  <div
                    className={`p-3 rounded-xl bg-gradient-to-br ${solution.gradient} text-white`}
                  >
                    {solution.icon}
                  </div>
                  <div>
                    <h2 className="text-2xl md:text-3xl font-bold">
                      {solution.title}
                    </h2>
                    <p className="text-muted-foreground mt-1">
                      {solution.subtitle}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  {solution.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <Check className="size-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-muted-foreground">{feature}</p>
                    </div>
                  ))}
                </div>

                <div className="p-4 bg-muted/50 rounded-lg border-l-4 border-primary">
                  <p className="font-medium text-sm text-muted-foreground mb-1">
                    OUTCOME
                  </p>
                  <p className="font-medium">{solution.outcome}</p>
                </div>
                {/* 
                <Button className="rounded-full">
                  {solution.cta}
                  <ArrowRight className="ml-2 size-4" />
                </Button> */}
              </div>

              <div className="flex-1">
                <Card className="overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10">
                  <CardContent className="p-8">
                    <div
                      className={`h-64 rounded-lg bg-gradient-to-br ${solution.gradient} opacity-20`}
                    ></div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
