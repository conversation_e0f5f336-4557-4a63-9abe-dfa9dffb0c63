"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  ArrowRight,
  CreditCard,
  Brain,
  FileText,
  Smartphone,
  BarChart,
  Users,
  Shield,
  Layers,
  Globe,
  Zap,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";
import { Suspense } from "react";
import HeroSection from "@/components/hero-section";
import ProductsGrid from "@/components/products/products-grid";
import CTABanner from "@/components/cta-banner";
const PartnerModal = dynamic(() => import("@/components/partner-modal"), {
  ssr: false,
});

export default function ProductsPage() {
  // Remove mounted state, not needed for SSR/SSG

  return (
    <div className="flex min-h-[100dvh] flex-col bg-background">
      <main className="flex-1">
        <HeroSection
          headline={
            <>
              Complete Payment{" "}
              <span className="text-accent-custom">Infrastructure</span>
            </>
          }
          subtext={
            <>
              From merchant onboarding to settlement, NGnair provides every tool
              you need to run a modern payment operation.
            </>
          }
          rightIcon={<CreditCard className="size-8" />}
          rightTitle="Complete Platform"
          rightSubtitle="All payment tools in one place"
          rightGradient="from-blue-500 to-green-500"
        />

        {/* Products Grid */}
        <ProductsGrid />

        {/* CTA Section */}
        <CTABanner />
      </main>
    </div>
  );
}
