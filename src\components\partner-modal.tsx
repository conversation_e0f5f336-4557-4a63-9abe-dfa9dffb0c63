"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import { PartnerBasicInfoFields } from "@/components/partner-form/PartnerBasicInfoFields";
import { PartnerAddressFields } from "@/components/partner-form/PartnerAddressFields";
import { PartnerIndustriesSelect } from "@/components/partner-form/PartnerIndustriesSelect";
import { PartnerFormSubmit } from "@/components/partner-form/PartnerFormSubmit";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { getFirstValidationError, type PartnerFormData } from "@/lib/validation";

interface PartnerModalProps {
  children: React.ReactNode;
}

declare global {
  interface Window {
    turnstile: {
      render: (
        element: string | HTMLElement,
        options: {
          sitekey: string;
          callback?: (token: string) => void;
          "error-callback"?: () => void;
          "expired-callback"?: () => void;
        }
      ) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
    };
  }
}

const industries = [
  "Retail",
  "Restaurant/Food Service",
  "Healthcare",
  "Professional Services",
  "E-commerce",
  "Manufacturing",
  "Real Estate",
  "Automotive",
  "Education",
  "Non-profit",
  "Technology",
  "Other",
];

export default function PartnerModal({ children }: PartnerModalProps) {
  const [open, setOpen] = useState(false);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [turnstileToken, setTurnstileToken] = useState<string>("");
  const turnstileRef = useRef<HTMLDivElement>(null);
  const widgetId = useRef<string>("");
  const [formData, setFormData] = useState<PartnerFormData>({
    firstName: "",
    lastName: "",
    businessName: "",
    email: "",
    phone: "",
    address: {
      country: "",
      state: "",
      city: "",
      zip: "",
      street: "",
    },
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      // Load Turnstile script if not already loaded
      if (!document.querySelector('script[src*="turnstile"]')) {
        const script = document.createElement("script");
        script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);

        script.onload = () => {
          renderTurnstile();
        };
      } else if (window.turnstile) {
        renderTurnstile();
      }
    }

    return () => {
      if (widgetId.current && window.turnstile) {
        window.turnstile.remove(widgetId.current);
        widgetId.current = "";
        setTurnstileToken("");
      }
    };
  }, [open]);

  const renderTurnstile = () => {
    if (turnstileRef.current && window.turnstile) {
      widgetId.current = window.turnstile.render(turnstileRef.current, {
        sitekey: "0x4AAAAAABtDxNjfePwCoEJj",
        callback: (token: string) => {
          setTurnstileToken(token);
        },
        "error-callback": () => {
          setTurnstileToken("");
        },
        "expired-callback": () => {
          setTurnstileToken("");
        },
      });
    }
  };

  const handleIndustryChange = (industry: string, checked: boolean) => {
    if (checked) {
      setSelectedIndustries([...selectedIndustries, industry]);
    } else {
      setSelectedIndustries(selectedIndustries.filter((i) => i !== industry));
    }
  };

  const apiUrl = process.env.NEXT_PUBLIC_PARTNER_API_URL;
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Validate form using centralized validation
    const validationError = getFirstValidationError(formData, selectedIndustries, turnstileToken);
    if (validationError) {
      toast.warning(validationError);
      setLoading(false);
      return;
    }
    try {
      const formDataToSend = new FormData();
      formDataToSend.append("partner[business_name]", formData.businessName);
      formDataToSend.append("partner[email]", formData.email);
      formDataToSend.append("partner[phone]", formData.phone);
      formDataToSend.append("partner[first_name]", formData.firstName);
      formDataToSend.append("partner[last_name]", formData.lastName);
      formDataToSend.append(
        "partner[address][country]",
        formData.address.country
      );
      formDataToSend.append("partner[address][state]", formData.address.state);
      formDataToSend.append("partner[address][city]", formData.address.city);
      formDataToSend.append("partner[address][zip]", formData.address.zip);
      formDataToSend.append(
        "partner[address][street]",
        formData.address.street
      );
      selectedIndustries.forEach((industry) => {
        formDataToSend.append("partner[industries][]", industry);
      });
      formDataToSend.append("cf-turnstile-response", turnstileToken);

      const response = await fetch(`${apiUrl}/api/v1/partners`, {
        method: "POST",
        body: formDataToSend,
      });
      if (!response.ok) {
        const errorText = await response.text();
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(
            errorJson.message ||
              errorJson.error ||
              "Failed to submit application"
          );
        } catch (e) {
          throw new Error(`Failed to submit application: ${errorText}`);
        }
      }
      toast.success("Your application has been submitted successfully!");
      setOpen(false);
      
      // Reset form
      setFormData({
        firstName: "",
        lastName: "",
        businessName: "",
        email: "",
        phone: "",
        address: {
          country: "",
          state: "",
          city: "",
          zip: "",
          street: "",
        },
      });
      setSelectedIndustries([]);
      setTurnstileToken("");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Error: ${error.message}`);
      } else {
        toast.error(
          "There was an error submitting your application. Please try again."
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4 pb-6">
          <DialogTitle className="text-3xl font-bold text-center text-foreground">
            Become a Partner
          </DialogTitle>
          <DialogDescription className="text-lg text-center text-muted-foreground leading-relaxed max-w-md mx-auto">
            Join our network grow their business portfolio with NGnair's comprehensive payment platform.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <PartnerBasicInfoFields
            formData={formData}
            setFormData={setFormData}
          />
          <PartnerAddressFields
            address={formData.address}
            setFormData={setFormData}
          />
          <PartnerIndustriesSelect
            industries={industries}
            selectedIndustries={selectedIndustries}
            handleIndustryChange={handleIndustryChange}
          />
          <div className="space-y-2">
            <Label>Security Verification *</Label>
            <div ref={turnstileRef} className="flex justify-center"></div>
          </div>
          <PartnerFormSubmit
            onCancel={() => setOpen(false)}
            loading={loading}
            disabled={selectedIndustries.length === 0 || !turnstileToken}
          />
        </form>
      </DialogContent>
    </Dialog>
  );
}

export { PartnerModal };
