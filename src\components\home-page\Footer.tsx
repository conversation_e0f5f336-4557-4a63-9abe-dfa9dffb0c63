"use client";
import LogoImg from "@/../public/img/logo.png";
import MapImg from "@/../public/img/map.png";
import Image from "next/image";
import Link from "next/link";

export default function Footer() {
  return (
    <>
      <footer className="footer-area relative z-1 bg-light pt-5">
        <div className="container">
          <div className="grid grid-cols-2 md:grid-cols-4 grid-rows-[repeat(4,min-content)] md:grid-rows-[repeat(2,min-content)] auto-rows-auto place-content-start gap-y-6 md:gap-4">
            {/* Logo Section */}
            <div className="row-span-1 col-span-2 md:row-span-3 pr-4">
              <Link href="/" className="w-full">
                <Image src={LogoImg} alt="logo" />
              </Link>
              <p className="max-w-[500px]">
                Elevated payment processing, enhanced customer satisfaction, and
                maintain a competitive edge in the fast-paced market.
              </p>
            </div>
            <div className="grid grid-rows-[repeat(2,minmax(fit-content))] gap-4">
              {/* Menu Section */}
              <div className="single-footer-widget mb-0">
                <h5 className="font-bold">Industries</h5>
                <ul className="list">
                  <li>
                    <Link href="/industries#isos">ISOs</Link>
                  </li>
                  <li>
                    <Link href="/industries#isvs">ISVs</Link>
                  </li>
                  <li>
                    <Link href="/industries#financial-institutions">
                      Financial Institutions
                    </Link>
                  </li>
                  <li>
                    <Link href="/industries#healthcare">Healthcare</Link>
                  </li>
                  <li>
                    <Link href="/industries#restaurant">Restaurant</Link>
                  </li>
                  <li>
                    <Link href="/industries#field-services">
                      Field Services
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="single-footer-widget mb-0">
                <h5 className="font-bold">Resources</h5>
                <ul className="list">
                  {/* <li>
                    <Link href="/api-documentation">API Documentation</Link>
                  </li> */}
                  <li>
                    <Link href="/privacy-policy">Privacy Policy</Link>
                  </li>
                  <li>
                    <Link href="/terms-conditions">Terms & Conditions</Link>
                  </li>
                  {/* <li>
                    <Link href="/partner-resources">Partner Resources</Link>
                  </li> */}
                </ul>
              </div>
            </div>
            {/* Solutions Section */}
            <div className="grid grid-rows-[repeat(2,minmax(fit-content))] gap-4 h-fit ">
              <div className="single-footer-widget mb-0">
                <h5 className="font-bold">Solutions</h5>
                <ul className="list">
                  <li>
                    <Link href="/solution-integrations#merchant-services">
                      Merchant Services
                    </Link>
                  </li>
                  <li>
                    <Link href="/solution-integrations#payment-options">
                      Payment Options
                    </Link>
                  </li>
                  <li>
                    <Link href="/solution-integrations#business-tools">
                      Business Tools
                    </Link>
                  </li>
                </ul>
              </div>
              {/* Support Section */}
              <div className="single-footer-widget mb-0">
                <h5 className="font-bold">Contact Information</h5>
                <ul className="list">
                  <li>
                    <a href="mailto:<EMAIL>">Email: <EMAIL></a>
                  </li>
                  <li>
                    <Link href="/contact-us">Contact Us</Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Copyright Section */}
          <div className="row mt-4">
            <div className="col text-center copyright-area">
              <p className="text-muted">
                Copyright @ {new Date().getFullYear()} NGnair Payments. All
                Rights Reserved.
              </p>
            </div>
          </div>

          {/* Map Image */}
          <div className="map-image">
            <div className="col text-center">
              <Image src={MapImg} alt="map" className="img-fluid" />
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}
