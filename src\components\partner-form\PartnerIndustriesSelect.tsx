import React, { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface PartnerIndustriesSelectProps {
  industries: string[];
  selectedIndustries: string[];
  handleIndustryChange: (industry: string, checked: boolean) => void;
}

export function PartnerIndustriesSelect({
  industries,
  selectedIndustries,
  handleIndustryChange,
}: PartnerIndustriesSelectProps) {
  const [showOtherInput, setShowOtherInput] = useState(false);
  const [otherIndustry, setOtherIndustry] = useState("");

  const handleIndustryChangeInternal = (industry: string, checked: boolean) => {
    if (industry === "Other") {
      setShowOtherInput(checked);
      if (!checked) {
        setOtherIndustry("");
        // Remove "Other" and all custom industries when unchecking
        const customIndustries = selectedIndustries.filter(
          (item) => !industries.includes(item)
        );
        customIndustries.forEach((customIndustry) =>
          handleIndustryChange(customIndustry, false)
        );
        // Remove "Other" from the array if it exists
        if (selectedIndustries.includes("Other")) {
          handleIndustryChange("Other", false);
        }
      }
      // Don't add "Other" to selectedIndustries - we only want custom entries
    } else {
      handleIndustryChange(industry, checked);
    }
  };
  const handleOtherIndustrySubmit = () => {
    if (
      otherIndustry.trim() &&
      !selectedIndustries.includes(otherIndustry.trim())
    ) {
      handleIndustryChange(otherIndustry.trim(), true);
      setOtherIndustry("");
    }
  };

  return (
    <div className="space-y-3">
      <Label>Industries You Service (select all that apply) *</Label>
      <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
        {industries.map((industry) => (
          <div key={industry} className="flex items-center space-x-2">
            <Checkbox
              id={industry}
              checked={
                industry === "Other"
                  ? showOtherInput
                  : selectedIndustries.includes(industry)
              }
              onCheckedChange={(checked) =>
                handleIndustryChangeInternal(industry, checked as boolean)
              }
            />
            <Label htmlFor={industry} className="text-sm font-normal">
              {industry}
            </Label>
          </div>
        ))}
      </div>

      {showOtherInput && (
        <div className="flex gap-2">
          <Input
            placeholder="Enter your industry"
            value={otherIndustry}
            onChange={(e) => setOtherIndustry(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleOtherIndustrySubmit();
              }
            }}
          />
          <Button
            type="button"
            onClick={handleOtherIndustrySubmit}
            disabled={!otherIndustry.trim()}
            size="sm"
          >
            Add
          </Button>
        </div>
      )}

      {selectedIndustries.filter((industry) => !industries.includes(industry))
        .length > 0 && (
        <div className="text-sm text-muted-foreground">
          Custom:{" "}
          {selectedIndustries
            .filter((industry) => !industries.includes(industry))
            .join(", ")}
        </div>
      )}
    </div>
  );
}
