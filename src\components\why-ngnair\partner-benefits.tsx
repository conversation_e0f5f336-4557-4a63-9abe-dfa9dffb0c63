"use client";

import { motion } from "framer-motion";
import { ArrowRight, Shield, TrendingUp, Layers } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import dynamic from "next/dynamic";
import { Suspense } from "react";
const PartnerModal = dynamic(() => import("@/components/partner-modal"), {
  ssr: false,
});

export default function PartnerBenefits() {
  const partnerBenefits = [
    {
      title: "Higher margins",
      description: "with branded POS and premium features",
      icon: <TrendingUp className="size-5" />,
    },
    {
      title: "Lower churn",
      description: "via uptime and modern UX",
      icon: <Shield className="size-5" />,
    },
    {
      title: "Operational efficiency",
      description: "with unified CRM/ERP‑style workflows",
      icon: <Layers className="size-5" />,
    },
  ];
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Partner Benefits
          </h2>
          <p className="text-lg text-muted-foreground">
            Join our partner ecosystem and unlock new revenue opportunities
            while delivering superior merchant experiences.
          </p>
        </motion.div>

        <div className="grid gap-8 md:grid-cols-3 max-w-4xl mx-auto">
          {partnerBenefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg hover:border-primary/20">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-primary/10 text-primary w-fit mx-auto mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Suspense
            fallback={
              <div className="flex justify-center items-center h-12">
                <span className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></span>{" "}
                Loading...
              </div>
            }
          >
            <PartnerModal>
              <Button className="rounded-full">
                Become a Partner
                <ArrowRight className="ml-2 size-4" />
              </Button>
            </PartnerModal>
          </Suspense>
        </div>
      </div>
    </section>
  );
}
