"use client";

import { motion } from "framer-motion";
import {
  Shield,
  Zap,
  TrendingUp,
  Globe,
  Layers,
  RefreshCw,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function Advantages() {
  const advantages = [
    {
      title: "Legacy replacement",
      description: "Consolidate fragmented tools into one platform.",
      icon: <Layers className="size-6" />,
    },
    {
      title: "Resilience by design",
      description:
        "Processor‑agnostic with automated failover to protect revenue.",
      icon: <Shield className="size-6" />,
    },
    {
      title: "Faster growth",
      description: "Branded POS marketplace and embedded payments options.",
      icon: <TrendingUp className="size-6" />,
    },
    {
      title: "Real‑time rails",
      description: "RTP & FedNow support for modern customer expectations.",
      icon: <Zap className="size-6" />,
    },
    {
      title: "Full lifecycle",
      description:
        "From application to servicing for both partner and merchant teams.",
      icon: <RefreshCw className="size-6" />,
    },
    {
      title: "Global infrastructure",
      description: "North America, EU, Pacific Asia operations and support.",
      icon: <Globe className="size-6" />,
    },
  ];
  return (
    <section className="w-full py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Competitive Advantage
          </h2>
          <p className="text-lg text-muted-foreground">
            NGnair delivers what legacy systems can't: modern architecture,
            intelligent failover, and unified operations.
          </p>
        </motion.div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {advantages.map((advantage, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full overflow-hidden border-border/40 bg-gradient-to-b from-background to-muted/10 backdrop-blur transition-all hover:shadow-lg hover:border-primary/20">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-primary/10 text-primary w-fit mx-auto mb-4">
                    {advantage.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-3">
                    {advantage.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {advantage.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
