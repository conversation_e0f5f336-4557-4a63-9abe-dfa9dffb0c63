"use client";

import { useState, useEffect } from "react";
import { ArrowRight } from "lucide-react";
import { Suspense } from "react";
import CTABanner from "@/components/cta-banner";
import {
  NGnairPlatform,
  OurServices,
  WhyChooseNGnair,
} from "@/components/home-page";
import HeroSection from "@/components/hero-section";

export default function LandingPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="flex min-h-[100dvh] flex-col bg-page">
        <main className="flex-1">
          <HeroSection
            headline={
              <>
                Give merchants everything they need while managing your{" "}
                <span className="text-accent-custom">
                  complete ISO operation
                </span>
              </>
            }
            subtext={
              <>
                Full payment tools, agent splits, and complete transaction
                visibility at your finger tips. Modern infrastructure that
                scales with your business and enhances merchant satisfaction.
              </>
            }
            buttonText="Become a Partner"
            buttonIcon={<ArrowRight className="ml-2 size-4" />}
          />

          <NGnairPlatform />
          <OurServices />
          <WhyChooseNGnair />
          <CTABanner />
        </main>
      </div>
    </Suspense>
  );
}
