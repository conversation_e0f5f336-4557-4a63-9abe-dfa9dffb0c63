import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Brain,
  NetworkIcon as Connection,
  ShoppingBag,
  DollarSign,
  Clock,
  Globe,
  ArrowRight,
  ChevronRight,
} from "lucide-react";
import { NGNAIR_DOMAIN } from "@/lib/data";
import Image from "next/image";

const Features = () => {
  const features = [
    {
      icon: <Brain className="h-6 w-6 text-slate-800" />,
      title: "AI-Powered Underwriting",
      description:
        "Our advanced AI algorithms analyze merchant risk profiles in real-time, enabling faster approvals while minimizing fraud.",
    },
    {
      icon: <Connection className="h-6 w-6 text-slate-800" />,
      title: "Multi-Processor Integration",
      description:
        "Connect with multiple payment processors through a single API integration. Optimize routing, redundancy, and costs.",
    },
    {
      icon: <ShoppingBag className="h-6 w-6 text-slate-800" />,
      title: "ISO/ISV Marketplace",
      description:
        "Access our curated marketplace of ISOs and ISVs to expand your payment capabilities and find the right partners.",
    },
    {
      icon: <DollarSign className="h-6 w-6 text-slate-800" />,
      title: "Transparent Pricing Engine",
      description:
        "Our pricing engine provides complete visibility into transaction costs. Set custom pricing rules and optimize interchange.",
      reverse: true,
    },
    {
      icon: <Clock className="h-6 w-6 text-slate-800" />,
      title: "Real-Time Settlements",
      description:
        "Accelerate your cash flow with real-time settlement capabilities. Funds are available instantly for improved liquidity.",
    },
    {
      icon: <Globe className="h-6 w-6 text-slate-800" />,
      title: "Global Payment Capabilities",
      description:
        "Expand your business globally with international payment processing. Support multiple currencies and comply with regulations.",
      reverse: true,
    },
  ];

  return (
    <div>
      <section className="w-full py-12 md:py-24 bg-white">
        <div className="container px-4 md:px-6 space-y-20">
          {/* Header */}
          <div className="section-title text-center space-y-4">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
              Comprehensive Payment Solutions
            </h2>
            <div className="bar"></div>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl">
              Our platform offers end-to-end payment processing capabilities
              designed for modern businesses.
            </p>
          </div>

          {/* Features */}
          <div className="grid gap-16 gap-x-24 lg:grid-cols-2">
            {features.map((feature, index) => (
              <Feature
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                reverse={feature.reverse}
              />
            ))}
          </div>
          <div className="pt-24 flex justify-center">
            <Image
              src={"/img/features-img1.png"}
              alt="image"
              width={500}
              height={500}
            />
          </div>
          {/* CTA */}
        </div>
      </section>
    </div>
  );
};

interface FeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight?: {
    title: string;
    subtitle: string;
  };
  reverse?: boolean;
}

const Feature: React.FC<FeatureProps> = ({
  icon,
  title,
  description,
  highlight,
  reverse,
}) => (
  <div
    className={`grid gap-6 lg:grid-cols-2 lg:gap-12 items-center${
      reverse ? "lg:flex-row-reverse" : ""
    }`}
  >
    <div className="space-y-4 col-span-2">
      <div className="inline-block rounded-lg bg-slate-100 p-2">{icon}</div>
      <h2 className="text-2xl font-bold tracking-tighter">{title}</h2>
      <p className="text-gray-500 md:text-lg">{description}</p>
    </div>
    {highlight && (
      <div className="rounded-lg bg-slate-800 p-6 lg:p-8 aspect-video flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-sm uppercase tracking-wider mb-2">
            {highlight.title}
          </p>
          <p className="text-2xl font-bold">{highlight.subtitle}</p>
        </div>
      </div>
    )}
  </div>
);

export default Features;
