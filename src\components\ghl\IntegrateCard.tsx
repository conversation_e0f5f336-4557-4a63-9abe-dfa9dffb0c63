import Image from "next/image"
import { ReactNode } from "react"

type Props = {
  title: string
  children: ReactNode
  href?: any
}

const IntegrateCard = ({ title, children, href }: Props) => {
  return (
    <div
      className="flex flex-col flex-1 shrink p-7 rounded-lg shadow basis-0 bg-[color:var(--white,#FFF)] gap-[var(--4,16px)] pb-[var(--6,] pt-[var(--6,] max-md:px-5"
    >
      <div
        className="flex justify-center items-center w-12 h-12 bg-sky-100 rounded-lg min-h-[48px]"
      >
        <Image
          src={href}
          alt={`${title}`}
        />
        {/* <Image
          image="https://cdn.builder.io/api/v1/image/assets/TEMP/844627ade450ad1e31c313fbb4f3777ee4265e84d65f424579549bdd4648fa3d?placeholderIfAbsent=true&apiKey=fc110779904845969da1d4c2962116a7"
          backgroundSize="contain"
          className="flex relative self-stretch my-auto w-6"
          aspectRatio={1}
          noWebp={true}
        /> */}
      </div>
      <div className="flex flex-col mt-2 w-full">
        <div
          className="text-lg not-italic font-semibold leading-tight text-[color:var(--gray-900,#111928)]"
        >
          {title}
        </div>
        <div
          className="mt-2 text-base not-italic leading-6 text-[color:var(--gray-500,#6B7280)]"
        >
          {children}
        </div>
      </div>
    </div>
  )
}

export default IntegrateCard
