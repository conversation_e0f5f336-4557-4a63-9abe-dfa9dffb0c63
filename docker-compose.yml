services:
  website:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    image: website
    container_name: website-1
    environment:
      - NODE_ENV=production
    command: npm run start
    networks:
      website_network:
        ipv4_address: ***********
    ports:
      - "3005:3000"

networks:
  website_network:
    external: true
    ipam:
      config:
        - subnet: **********/16

#  nginx:
 #   build:
 #     context: ./nginx
 #   networks:
 #     - website_network
 #   depends_on:
 #     - app
