{"name": "ngnair-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint ."}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/is-prop-valid": "latest", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/resolvers": "^3.9.1", "@mui/material-nextjs": "^5.15.11", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "animate.css": "^4.1.1", "autoprefixer": "^10.4.21", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "geist": "^1.3.1", "input-otp": "1.4.1", "jquery": "^3.7.1", "lucide-react": "^0.503.0", "next": "^14.2.3", "next-themes": "^0.4.6", "owl.carousel": "^2.3.4", "react": "^18", "react-bootstrap": "^2.10.2", "react-day-picker": "9.8.0", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-owl-carousel": "^2.3.3", "react-resizable-panels": "^2.1.7", "react-slick": "^0.30.2", "recharts": "2.15.0", "sharp": "^0.33.3", "slick-carousel": "^1.8.1", "sonner": "^1.7.4", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "eslint": "^9.34.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}