"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import { CheckCircle } from "lucide-react";

type Solution = {
  id: string;
  title: string;
  content: string;
  features: string[];
};

export function SolutionsTabs({ data }: { data: Solution[] }) {
  const [activeTab, setActiveTab] = useState(data[0].id);

  return (
    <div className="w-full">
      {/* Tab Navigation */}
      <div className="flex flex-wrap justify-center mb-8 gap-2">
        {data.map((solution) => (
          <button
            key={solution.id}
            onClick={() => setActiveTab(solution.id)}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-md transition-colors",
              activeTab === solution.id
                ? "bg-slate-800 text-white"
                : "bg-slate-100 text-slate-700 hover:bg-slate-200"
            )}
          >
            {solution.title}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {data.map((solution) => (
          <div
            key={solution.id}
            className={cn(
              "transition-opacity duration-300",
              activeTab === solution.id
                ? "block opacity-100"
                : "hidden opacity-0"
            )}
          >
            <div className="p-6 md:p-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-4">
                {solution.title}
              </h3>
              <p className="text-slate-600 mb-6">{solution.content}</p>

              <div className="bg-slate-50 p-6 rounded-lg">
                <h4 className="text-lg font-semibold mb-4">Key Features</h4>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {solution.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
