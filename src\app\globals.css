@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Updated to corporate blue and green theme */
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;
    --card: 210 40% 98%;
    --card-foreground: 215 25% 27%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 142 76% 36%;
    --secondary-foreground: 0 0% 98%;
    --muted: 210 40% 98%;
    --muted-foreground: 215 25% 27%;
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 210 40% 98%;
    --ring: 142 76% 36%;
    --chart-1: 221 83% 53%;
    --chart-2: 142 76% 36%;
    --chart-3: 215 25% 27%;
    --chart-4: 220 13% 91%;
    --chart-5: 0 0% 100%;
    --radius: 0.5rem;
    --sidebar-background: 210 40% 98%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 142 76% 36%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 142 76% 36%;

    /* Added semantic color variables for consistent theming */
    --page-background: 0 0% 100%;
    --hero-background: 210 40% 98%;
    --section-background: 0 0% 100%;
    --primary-text: 215 25% 27%;
    --secondary-text: 215 10% 40%;
    --accent-text: 221 83% 53%;
    --button-primary: 221 83% 53%;
    --button-primary-hover: 221 83% 45%;
    --button-text: 0 0% 98%;
    --success-background: 142 76% 90%;
    --success-text: 142 76% 36%;
    --overlay-light: 255 255 255 / 0.1;
    --overlay-medium: 255 255 255 / 0.2;
    --overlay-text: 255 255 255 / 0.8;
    --modal-backdrop: 0 0 0 / 0.8;
  }

  .dark {
    /* Updated dark mode colors to maintain corporate theme */
    --background: 215 28% 17%;
    --foreground: 210 40% 98%;
    --card: 215 28% 17%;
    --card-foreground: 210 40% 98%;
    --popover: 215 28% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 215 28% 17%;
    --secondary: 142 69% 58%;
    --secondary-foreground: 215 28% 17%;
    --muted: 215 32% 27%;
    --muted-foreground: 217 10% 64%;
    --accent: 142 69% 58%;
    --accent-foreground: 215 28% 17%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 32% 27%;
    --input: 215 32% 27%;
    --ring: 142 69% 58%;
    --chart-1: 217 91% 60%;
    --chart-2: 142 69% 58%;
    --chart-3: 217 10% 64%;
    --chart-4: 215 32% 27%;
    --chart-5: 215 28% 17%;
    --sidebar-background: 215 32% 27%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 215 28% 17%;
    --sidebar-accent: 142 69% 58%;
    --sidebar-accent-foreground: 215 28% 17%;
    --sidebar-border: 215 32% 27%;
    --sidebar-ring: 142 69% 58%;

    /* Dark mode semantic color variables */
    --page-background: 215 28% 17%;
    --hero-background: 215 32% 27%;
    --section-background: 215 28% 17%;
    --primary-text: 210 40% 98%;
    --secondary-text: 217 10% 64%;
    --accent-text: 217 91% 60%;
    --button-primary: 217 91% 60%;
    --button-primary-hover: 217 91% 65%;
    --button-text: 215 28% 17%;
    --success-background: 142 69% 20%;
    --success-text: 142 69% 58%;
    --overlay-light: 255 255 255 / 0.1;
    --overlay-medium: 255 255 255 / 0.2;
    --overlay-text: 255 255 255 / 0.8;
    --modal-backdrop: 0 0 0 / 0.8;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added utility classes for semantic colors */
@layer utilities {
  .bg-page {
    @apply bg-[hsl(var(--page-background))];
  }
  .bg-hero {
    @apply bg-[hsl(var(--hero-background))];
  }
  .bg-section {
    @apply bg-[hsl(var(--section-background))];
  }
  .text-primary-custom {
    @apply text-[hsl(var(--primary-text))];
  }
  .text-secondary-custom {
    @apply text-[hsl(var(--secondary-text))];
  }
  .text-accent-custom {
    @apply text-[hsl(var(--accent-text))];
  }
  .bg-button-primary {
    @apply bg-[hsl(var(--button-primary))];
  }
  .bg-button-primary-hover {
    @apply bg-[hsl(var(--button-primary-hover))];
  }
  .text-button {
    @apply text-[hsl(var(--button-text))];
  }
  .bg-success {
    @apply bg-[hsl(var(--success-background))];
  }
  .text-success {
    @apply text-[hsl(var(--success-text))];
  }
  .bg-overlay-light {
    @apply bg-[var(--overlay-light)];
  }
  .bg-overlay-medium {
    @apply bg-[var(--overlay-medium)];
  }
  .text-overlay {
    @apply text-[var(--overlay-text)];
  }
  .bg-modal-backdrop {
    @apply bg-[var(--modal-backdrop)];
  }
}
