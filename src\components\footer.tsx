"use client";

import Link from "next/link";

export default function Footer() {
  return (
    <footer className="w-full bg-muted border-t">
      <div className="container mx-auto px-4 py-12 md:px-6">
        <div className="grid gap-8 md:grid-cols-4">
          <div className="space-y-4">
            <div className="flex items-center gap-2 font-bold">
              <div className="size-8 rounded-lg bg-primary flex items-center justify-center text-primary-foreground font-bold">
                N
              </div>
              <span className="text-foreground">NGnair</span>
            </div>
            <p className="text-muted-foreground text-sm">
              Helping small and medium businesses grow through proven digital
              payment strategies.
            </p>
            <div className="text-sm text-muted-foreground">
              <p>📧 <EMAIL></p>
              <p>📞 +1 (555) 123-4567</p>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Services</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  Payment Processing
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  POS Solutions
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  AI Underwriting
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  Multi-Agent Splits
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Company</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link
                  href="/about"
                  className="hover:text-foreground transition-colors"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/why-ngnair"
                  className="hover:text-foreground transition-colors"
                >
                  Why NGnair
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="hover:text-foreground transition-colors"
                >
                  Contact
                </Link>
              </li>
              <li>
                <Link
                  href="/solutions"
                  className="hover:text-foreground transition-colors"
                >
                  Solutions
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Resources</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link
                  href="/privacy-policy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms-and-conditions"
                  className="hover:text-foreground transition-colors"
                >
                  Terms & Conditions
                </Link>
              </li>
            </ul>
          </div>
          {/* <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Marketing Tips</h4>
            <p className="text-sm text-muted-foreground">
              Get free payment tips and strategies delivered to your inbox.
            </p>
          </div> */}
        </div>
        <div className="border-t border-border mt-8 pt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Copyright © {new Date().getFullYear()} NGnair Payments. All rights
            reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
