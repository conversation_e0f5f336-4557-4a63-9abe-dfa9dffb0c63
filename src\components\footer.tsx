"use client";

import Link from "next/link";
import Image from "next/image";

export default function Footer() {
  return (
    <footer className="w-full bg-muted border-t">
      <div className="container mx-auto px-4 py-12 md:px-6">
        <div className="grid gap-8 md:grid-cols-3">
          <div className="space-y-4">
            <div className="flex items-center gap-2 font-bold">
              <Image
                src="/ngnair-logo.webp"
                alt="NGnair Logo"
                width={120}
                height={32}
                className="h-8 w-auto"
              />
            </div>
            <p className="text-muted-foreground text-sm">
              Helping small and medium businesses grow through proven digital
              payment strategies.
            </p>
            <div className="text-sm text-muted-foreground">
              <p>📧 <EMAIL></p>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Services</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  Payment Processing
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  POS Solutions
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  AI Underwriting
                </Link>
              </li>
              <li>
                <Link
                  href="/products"
                  className="hover:text-foreground transition-colors"
                >
                  Multi-Agent Splits
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Company</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link
                  href="/about"
                  className="hover:text-foreground transition-colors"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/why-ngnair"
                  className="hover:text-foreground transition-colors"
                >
                  Why NGnair
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="hover:text-foreground transition-colors"
                >
                  Contact
                </Link>
              </li>
              <li>
                <Link
                  href="/solutions"
                  className="hover:text-foreground transition-colors"
                >
                  Solutions
                </Link>
              </li>
            </ul>
          </div>

          {/* <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Marketing Tips</h4>
            <p className="text-sm text-muted-foreground">
              Get free payment tips and strategies delivered to your inbox.
            </p>
          </div> */}
        </div>
        <div className="border-t border-border mt-8 pt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Copyright © {new Date().getFullYear()} NGnair Payments. All rights
            reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
