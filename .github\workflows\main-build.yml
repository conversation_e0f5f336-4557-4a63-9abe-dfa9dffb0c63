name: Deploy to Production

env:
  CONTEXT_DIR: "./"
  IMAGE_NAME: ${{ github.repository }}/main
  DOCKERFILE: Dockerfile
  DOCKER_REGISTRY: docker.io
  OWNER: ${{ secrets.DOCKER_USERNAME }}
  DOCKER_REPOSITORY: ${{ secrets.DOCKER_REPOSITORY }}

on:
  push:
    branches:
      - main

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v1

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.OWNER }}/${{ env.DOCKER_REPOSITORY }}
          tags: | 
           type=sha,prefix=,format=short
            
      - name: Log into Dockerhub
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Build and push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
