"use client";
import React, { useEffect, useRef, useState } from "react";
import Modal from "react-bootstrap/Modal";
import IA1 from "@/../public/img/invoicing-image/1.png";
import IA2 from "@/../public/img/invoicing-image/2.png";
import IA3 from "@/../public/img/invoicing-image/3.png";
import IA4 from "@/../public/img/invoicing-image/4.png";
import IA5 from "@/../public/img/invoicing-image/main-pic.png";
import IA6 from "@/../public/img/invoicing-image/circle1.png";
import IA7 from "@/../public/img/invoicing-image/circle2.png";

import IA8 from "@/../public/img/3.png";
import IA9 from "@/../public/img/circle.png";

import Image from "next/image";
import { preLaunchSignupIframeLink, preLaunchSignupScript } from "@/lib/links";

export default function InvoicingArea() {
  const [show, setShow] = useState(false);

  const handleClose = () => setShow(false);
  const handleShow = () => {
    setShow(true);
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = preLaunchSignupScript;
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <>
      <Modal
        show={show}
        onHide={handleClose}
        aria-labelledby="contained-modal-title-vcenter"
        className="hidden"
        animation={false}
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body className="!p-0">
          {
            <iframe
              src={preLaunchSignupIframeLink}
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                borderRadius: "0px",
                marginTop: "0px important",
                paddingTop: "0px important",
              }}
              title="Front Page - Sign Up"
            ></iframe>
          }
        </Modal.Body>
        <Modal.Footer className="border-0"></Modal.Footer>
      </Modal>
      <section className="services-area ptb-70">
        <div className="container-fluid p-0">
          <div className="overview-box">
            <div className="overview-content">
              <div className="content left-content">
                <h2>Large or Enterprise level Businesses</h2>
                <div className="bar"></div>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>

                <ul className="services-list">
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Corporate Cards
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> International
                      Payments
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Automated
                      accounting
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Request Features
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Premium Support
                    </span>
                  </li>
                  <li>
                    <span>
                      <i className="flaticon-check-mark"></i> Direct Debit
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="overview-image">
              <div className="image">
                <Image src={IA8} alt="image" />

                <div className="circle-img">
                  <Image src={IA9} alt="image" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="invoicing-area ptb-70">
        <div className="container-fluid">
          <div className="row align-items-center">
            <div className="col-lg-6 col-md-12">
              <div className="invoicing-content">
                <h2>
                  Easy Payment to borrow free get a better filling at home
                </h2>
                <div className="bar"></div>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  Ut enim ad minim veniam, quis nostrud exercitation ullamco
                  laboris nisi ut aliquip.
                </p>
                <button className="btn btn-primary" onClick={handleShow}>
                  Get Started
                </button>
              </div>
            </div>

            <div className="col-lg-6 col-md-12">
              <div className="invoicing-image">
                <div className="main-image">
                  <Image
                    src={IA1}
                    className="wow animate__animated animate__zoomIn"
                    alt="image"
                  />
                  <Image
                    src={IA2}
                    className="wow animate__animated animate__fadeInLeft"
                    alt="image"
                  />
                  <Image
                    src={IA3}
                    className="wow animate__animated animate__fadeInLeft"
                    alt="image"
                  />
                  <Image
                    src={IA4}
                    className="wow animate__animated animate__fadeInRight"
                    alt="image"
                  />
                </div>

                <div className="main-mobile-image">
                  <Image
                    src={IA5}
                    className="wow animate__animated animate__zoomIn"
                    alt="image"
                  />
                </div>

                <div className="circle-image">
                  <Image src={IA6} alt="image" />
                  <Image src={IA7} alt="image" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
